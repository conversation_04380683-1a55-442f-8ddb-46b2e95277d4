import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔍 [测试] 收到的登录请求体:', JSON.stringify(body, null, 2));
    console.log('🔍 [测试] inviteCode 字段:', {
      value: body.inviteCode,
      type: typeof body.inviteCode,
      hasField: 'inviteCode' in body,
      isNull: body.inviteCode === null,
      isUndefined: body.inviteCode === undefined,
      isStringNull: body.inviteCode === 'null',
      isStringUndefined: body.inviteCode === 'undefined',
      isTruthy: !!body.inviteCode
    });

    return NextResponse.json({
      success: true,
      received: body,
      inviteCodeAnalysis: {
        value: body.inviteCode,
        type: typeof body.inviteCode,
        hasField: 'inviteCode' in body,
        isNull: body.inviteCode === null,
        isUndefined: body.inviteCode === undefined,
        isStringNull: body.inviteCode === 'null',
        isStringUndefined: body.inviteCode === 'undefined',
        isTruthy: !!body.inviteCode
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
