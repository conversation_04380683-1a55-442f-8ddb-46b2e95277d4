"use client";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface PreviewModalProps {
  project: CommunityProject | null;
  isOpen: boolean;
  onClose: () => void;
  onOpen: (project: CommunityProject) => void;
}

export function PreviewModal({ project, isOpen, onClose }: PreviewModalProps) {
  if (!project || !isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-neutral-950">
      {/* 🎯 预览工具栏 - 从系统顶部栏下方开始 */}
      <div
        className="h-12 bg-card border-b border-border flex items-center justify-between px-4"
        style={{ marginTop: '44px' }}
      >
        <h2 className="text-lg font-semibold text-foreground">
          {project.title}
        </h2>
        <Button
          onClick={onClose}
          variant="outline"
          size="sm"
          className="bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground"
        >
          <X className="w-4 h-4 mr-2" />
          关闭预览
        </Button>
      </div>

      {/* 🎯 全屏预览区域 - 从工具栏下方开始到屏幕底部 */}
      <div
        className="fixed left-0 right-0 bottom-0 bg-background"
        style={{ top: '92px' }}
      >
        {/* 全屏预览内容 */}
        <iframe
          srcDoc={(() => {
            const htmlContent = project.htmlContent;
            
            // 🔧 第一步：清理HTML内容，移除所有可能导致导航的代码
            const cleanedHtml = (() => {
              let cleaned = htmlContent;
              
              // 1. 清理所有外部链接，替换为无害的占位符
              cleaned = cleaned.replace(/href\s*=\s*["']https?:\/\/[^"']*["']/gi, 'href="#"');
              cleaned = cleaned.replace(/href\s*=\s*["'][^"']*\.(com|org|net|io|co|cn)[^"']*["']/gi, 'href="#"');
              
              // 2. 清理JavaScript导航代码
              cleaned = cleaned.replace(/window\.location\s*[=\.]/gi, '// window.location');
              cleaned = cleaned.replace(/window\.open\s*\(/gi, '// window.open(');
              cleaned = cleaned.replace(/parent\.window/gi, '// parent.window');
              cleaned = cleaned.replace(/top\.window/gi, '// top.window');
              cleaned = cleaned.replace(/document\.location/gi, '// document.location');
              cleaned = cleaned.replace(/history\.(pushState|replaceState|back|forward|go)/gi, '// history.$1');
              
              // 3. 为所有form添加防止提交的处理
              cleaned = cleaned.replace(/<form([^>]*)>/gi, '<form$1 onsubmit="event.preventDefault(); return false;">');
              
              // 4. 为所有按钮添加防止默认行为
              cleaned = cleaned.replace(/<button([^>]*?)onclick\s*=\s*["']([^"']*)["']/gi, 
                '<button$1onclick="event.preventDefault(); $2"');
              cleaned = cleaned.replace(/<button([^>]*?)(?!onclick)/gi, 
                '<button$1onclick="event.preventDefault();"');
              
              // 5. 为所有链接添加防止默认行为
              cleaned = cleaned.replace(/<a([^>]*?)onclick\s*=\s*["']([^"']*)["']/gi, 
                '<a$1onclick="event.preventDefault(); $2"');
              cleaned = cleaned.replace(/<a([^>]*?)href\s*=\s*["']#["']([^>]*?)(?!onclick)/gi, 
                '<a$1href="#"$2onclick="event.preventDefault();"');
              
              console.log('🧹 预览模态框HTML内容清理完成', {
                原始长度: htmlContent.length,
                清理后长度: cleaned.length,
                清理差异: htmlContent.length - cleaned.length
              });
              
              return cleaned;
            })();
            
            // 检测是否为完整HTML文档
            const isCompleteHtml = cleanedHtml.includes('<!DOCTYPE') || cleanedHtml.includes('<html');
            
            // 强力导航阻止脚本 - 修复作用域问题
            const protectionScript = `
              <script>
                // 立即执行的强力导航阻止脚本（全屏预览模式）
                (function() {
                  'use strict';
                  
                  // 1. 在函数作用域顶部定义变量
                  var originalLocation = window.location.href;
                  var checkInterval = null;
                  
                  // 🔧 关键修复：特殊处理about:srcdoc协议
                  console.log('🔍 预览模态框原始location:', originalLocation);
                  
                  // 2. 重写所有可能的导航方法
                  if (typeof window !== 'undefined') {
                    // 阻止window.open
                    window.open = function() { 
                      console.log('🚫 预览模态框window.open被阻止');
                      return null; 
                    };
                    
                    // 重写location的所有属性
                    ['href', 'assign', 'replace', 'reload'].forEach(function(prop) {
                      try {
                        if (typeof window.location[prop] === 'function') {
                          window.location[prop] = function() { 
                            console.log('🚫 预览模态框location.' + prop + '被阻止');
                            return false; 
                          };
                        } else {
                          Object.defineProperty(window.location, prop, {
                            get: function() { return originalLocation; },
                            set: function(value) { 
                              console.log('🚫 预览模态框location.' + prop + '设置被阻止:', value);
                              return false; 
                            },
                            configurable: false
                          });
                        }
                      } catch(e) {
                        console.log('⚠️ 预览模态框无法重写location.' + prop + ':', e.message);
                      }
                    });
                    
                    // 阻止history API
                    if (window.history) {
                      window.history.pushState = function() { 
                        console.log('🚫 预览模态框history.pushState被阻止');
                        return false; 
                      };
                      window.history.replaceState = function() { 
                        console.log('🚫 预览模态框history.replaceState被阻止');
                        return false; 
                      };
                      window.history.back = function() { 
                        console.log('🚫 预览模态框history.back被阻止');
                        return false; 
                      };
                      window.history.forward = function() { 
                        console.log('🚫 预览模态框history.forward被阻止');
                        return false; 
                      };
                      window.history.go = function() { 
                        console.log('🚫 预览模态框history.go被阻止');
                        return false; 
                      };
                    }
                  }
                  
                  // 3. 在DOM加载前就阻止事件
                  if (typeof document !== 'undefined') {
                    // 使用捕获阶段拦截所有点击事件
                    document.addEventListener('click', function(e) {
                      console.log('🔍 预览模态框点击事件:', e.target.tagName, e.target);
                      e.preventDefault();
                      e.stopImmediatePropagation();
                      e.stopPropagation();
                      console.log('🚫 预览模态框所有点击事件被阻止');
                      return false;
                    }, { capture: true, passive: false });
                    
                    // 阻止所有表单提交
                    document.addEventListener('submit', function(e) {
                      console.log('🚫 预览模态框表单提交被阻止');
                      e.preventDefault();
                      e.stopImmediatePropagation();
                      return false;
                    }, { capture: true, passive: false });
                    
                    // 阻止键盘导航
                    document.addEventListener('keydown', function(e) {
                      // 阻止Enter键和其他可能触发导航的键
                      if (e.key === 'Enter' || e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                        console.log('🚫 预览模态框键盘导航被阻止:', e.key);
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        return false;
                      }
                    }, { capture: true, passive: false });
                    
                    // 阻止右键菜单
                    document.addEventListener('contextmenu', function(e) {
                      console.log('🚫 预览模态框右键菜单被阻止');
                      e.preventDefault();
                      return false;
                    }, { capture: true, passive: false });
                  }
                  
                  // 4. 🔧 关键修复：对于about:srcdoc协议，不进行location检查
                  if (originalLocation.indexOf('about:srcdoc') === -1) {
                    checkInterval = setInterval(function() {
                      var currentLocation = window.location.href;
                      if (currentLocation !== originalLocation) {
                        console.log('🚨 预览模态框检测到location变更:', currentLocation, '!==', originalLocation);
                        try {
                          window.stop(); // 停止页面加载
                          window.location.href = originalLocation;
                        } catch(e) {
                          console.log('⚠️ 预览模态框无法恢复location:', e.message);
                        }
                      }
                    }, 100);
                  } else {
                    console.log('🔧 预览模态框跳过about:srcdoc协议的location检查');
                  }
                  
                  // 5. 阻止页面卸载
                  window.addEventListener('beforeunload', function(e) {
                    console.log('🚫 预览模态框页面卸载被阻止');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                  });
                  
                  // 6. 阻止页面重新加载
                  window.addEventListener('unload', function(e) {
                    console.log('🚫 预览模态框页面重新加载被阻止');
                    e.preventDefault();
                    return false;
                  });
                  
                  console.log('🛡️ 全屏预览导航防护已激活 (协议: ' + originalLocation.split(':')[0] + ')');
                })();
              </script>
            `;
            
            // 🎯 修复：允许滚动但隐藏滚动条的CSS
            const hideScrollbarCSS = `
              <style>
                html, body {
                  overflow: auto !important;
                  scrollbar-width: none !important;
                  -ms-overflow-style: none !important;
                  scroll-behavior: smooth !important;
                }
                html::-webkit-scrollbar, body::-webkit-scrollbar {
                  display: none !important;
                  width: 0 !important;
                  height: 0 !important;
                }
                * {
                  scrollbar-width: none !important;
                  -ms-overflow-style: none !important;
                }
                *::-webkit-scrollbar {
                  display: none !important;
                  width: 0 !important;
                  height: 0 !important;
                }
              </style>
            `;
            
            if (isCompleteHtml) {
              // 完整HTML文档：在</head>前添加防护脚本和样式
              return cleanedHtml.replace(
                '</head>',
                `${protectionScript}${hideScrollbarCSS}</head>`
              );
            } else {
              // 不完整HTML片段：包装成完整文档
              return `
                <!DOCTYPE html>
                <html>
                  <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    ${protectionScript}
                    ${hideScrollbarCSS}
                  </head>
                  <body>
                    ${cleanedHtml}
                  </body>
                </html>
              `;
            }
          })()}
          className="w-full h-full border-0"
          sandbox="allow-scripts allow-same-origin"
        />
      </div>
    </div>
  );
} 