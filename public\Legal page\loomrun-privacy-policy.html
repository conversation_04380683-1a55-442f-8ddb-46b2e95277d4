<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - LoomRun</title>
    <!-- 浅色模式图标（默认） -->
    <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/light/ghost-icon-light-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/light/ghost-icon-light-16x16.png">

    <!-- 深色模式图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/dark/ghost-icon-dark-32x32.png" media="(prefers-color-scheme: dark)">
    <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/dark/ghost-icon-dark-16x16.png" media="(prefers-color-scheme: dark)">

    <!-- 兜底方案 -->
    <link rel="icon" type="image/x-icon" href="/favicon_io/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .sidebar {
            width: 280px;
            background: #1e293b;
            color: white;
            padding: 2rem 0;
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
        }

        .logo {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #334155;
            margin-bottom: 2rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: block;
            padding: 0.75rem 2rem;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .nav-link:hover, .nav-link.active {
            background: #334155;
            color: white;
            border-left-color: #3b82f6;
        }

        .content {
            flex: 1;
            padding: 3rem;
            overflow-y: auto;
        }

        .header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .header .meta {
            color: #64748b;
            font-size: 0.9rem;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin: 1.5rem 0 1rem;
        }

        .section p {
            margin-bottom: 1rem;
            color: #4b5563;
            line-height: 1.7;
        }

        .section ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        .section li {
            margin-bottom: 0.5rem;
            color: #4b5563;
        }

        .highlight {
            background: #fef3c7;
            padding: 1rem;
            border-left: 4px solid #f59e0b;
            margin: 1.5rem 0;
            border-radius: 0 4px 4px 0;
        }

        .contact-info {
            background: #f1f5f9;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .contact-info h4 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }

            .content {
                padding: 2rem 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <h1>🚀 LoomRun</h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#overview" class="nav-link active">政策概述</a>
                </li>
                <li class="nav-item">
                    <a href="#collection" class="nav-link">信息收集</a>
                </li>
                <li class="nav-item">
                    <a href="#usage" class="nav-link">信息使用</a>
                </li>
                <li class="nav-item">
                    <a href="#sharing" class="nav-link">信息共享</a>
                </li>
                <li class="nav-item">
                    <a href="#storage" class="nav-link">信息存储</a>
                </li>
                <li class="nav-item">
                    <a href="#security" class="nav-link">安全保护</a>
                </li>
                <li class="nav-item">
                    <a href="#rights" class="nav-link">用户权利</a>
                </li>
                <li class="nav-item">
                    <a href="#cookies" class="nav-link">Cookie政策</a>
                </li>
                <li class="nav-item">
                    <a href="#minors" class="nav-link">未成年人保护</a>
                </li>
                <li class="nav-item">
                    <a href="#updates" class="nav-link">政策更新</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系我们</a>
                </li>
            </ul>
        </nav>

        <main class="content">
            <div class="header">
                <h1>隐私政策</h1>
                <div class="meta">
                    最后更新时间：2025年7月29日 | 生效日期：2025年7月29日
                </div>
            </div>

            <section id="overview" class="section">
                <h2>政策概述</h2>
                <p>欢迎使用LoomRun！我们深知个人信息对您的重要性，并会全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：</p>
                <ul>
                    <li><strong>权责一致原则</strong>：我们将按照法律法规要求，采取相应的安全保护措施，尽力保护您的个人信息安全可控。</li>
                    <li><strong>目的明确原则</strong>：我们仅为实现产品功能，向您提供服务之目的收集、使用您的个人信息。</li>
                    <li><strong>选择同意原则</strong>：我们会充分尊重您的选择，您可以根据自己的需求，选择是否授权我们收集相关个人信息。</li>
                    <li><strong>最少够用原则</strong>：我们只会收集实现产品功能所必要的个人信息。</li>
                    <li><strong>确保安全原则</strong>：我们将运用各种安全保护措施以确保个人信息安全。</li>
                    <li><strong>主体参与原则</strong>：我们将尽力保证您对自己个人信息的查询、更正、删除等权利。</li>
                    <li><strong>公开透明原则</strong>：我们努力使用简明易懂的表述，向您介绍隐私政策，以便您清晰地了解我们的信息处理方式。</li>
                </ul>

                <div class="highlight">
                    <strong>重要提示：</strong>请您在使用我们的产品（或服务）前，仔细阅读并了解本《隐私政策》。一旦您开始使用LoomRun，即表示您已充分理解并同意本政策。
                </div>
            </section>

            <section id="collection" class="section">
                <h2>我们如何收集和使用您的个人信息</h2>

                <h3>1. 账户注册与登录</h3>
                <p>当您注册LoomRun账户时，我们会收集：</p>
                <ul>
                    <li>手机号码（用于账户注册、登录验证和安全保护）</li>
                    <li>微信授权信息（如选择微信登录，包括昵称、头像等基本信息）</li>
                    <li>设备信息（设备型号、操作系统版本、设备标识符等）</li>
                </ul>

                <h3>2. 产品功能使用</h3>
                <p>为了向您提供核心服务，我们可能收集：</p>
                <ul>
                    <li>项目数据（您创建的代码项目、文件内容等）</li>
                    <li>使用偏好（语言设置、主题选择、编辑器配置等）</li>
                    <li>操作日志（为改进产品体验和排查问题）</li>
                    <li>性能数据（应用崩溃信息、加载时间等技术数据）</li>
                </ul>

                <h3>3. 社区功能</h3>
                <p>当您使用社区功能时，我们会收集：</p>
                <ul>
                    <li>发布的项目信息（项目描述、代码内容、标签等）</li>
                    <li>互动数据（点赞、收藏、评论等）</li>
                    <li>用户资料（昵称、头像、个人简介等公开信息）</li>
                </ul>
            </section>

            <section id="usage" class="section">
                <h2>我们如何使用收集的信息</h2>
                <p>我们会出于以下目的，使用收集到的信息：</p>

                <h3>1. 提供基础服务</h3>
                <ul>
                    <li>创建和管理您的账户</li>
                    <li>提供代码编辑、项目管理等核心功能</li>
                    <li>保存您的项目数据和个人设置</li>
                    <li>提供客户支持和技术服务</li>
                </ul>

                <h3>2. 改善用户体验</h3>
                <ul>
                    <li>分析产品使用情况，优化功能设计</li>
                    <li>个性化推荐相关项目和内容</li>
                    <li>提供更精准的搜索结果</li>
                </ul>

                <h3>3. 安全保障</h3>
                <ul>
                    <li>验证用户身份，防止欺诈行为</li>
                    <li>检测和预防安全威胁</li>
                    <li>保护平台和用户的合法权益</li>
                </ul>

                <h3>4. 法律合规</h3>
                <ul>
                    <li>遵守适用的法律法规要求</li>
                    <li>配合监管部门的合法调查</li>
                    <li>维护公共安全和利益</li>
                </ul>
            </section>

            <section id="sharing" class="section">
                <h2>我们如何共享、转让、公开披露您的个人信息</h2>

                <h3>1. 共享</h3>
                <p>我们不会向第三方共享您的个人信息，除非：</p>
                <ul>
                    <li>获得您的明确同意</li>
                    <li>基于法律法规的要求</li>
                    <li>与我们的关联公司共享（仅限于实现本政策声明的目的）</li>
                    <li>与授权合作伙伴共享（严格限制其使用目的和范围）</li>
                </ul>

                <h3>2. 转让</h3>
                <p>我们不会将您的个人信息转让给任何公司、组织和个人，但以下情况除外：</p>
                <ul>
                    <li>获得您的明确同意</li>
                    <li>在涉及合并、收购或破产清算时，如涉及到个人信息转让，我们会要求新的持有您个人信息的公司、组织继续受本隐私政策的约束</li>
                </ul>

                <h3>3. 公开披露</h3>
                <p>我们仅会在以下情况下公开披露您的个人信息：</p>
                <ul>
                    <li>获得您明确同意后</li>
                    <li>基于法律的披露：在法律、法律程序、诉讼或政府主管部门强制性要求的情况下</li>
                </ul>
            </section>

            <section id="storage" class="section">
                <h2>我们如何保存您的个人信息</h2>

                <h3>1. 保存期限</h3>
                <p>我们仅在为提供LoomRun服务所必需的期间内保留您的个人信息：</p>
                <ul>
                    <li>账户信息：在您的账户存续期间</li>
                    <li>项目数据：根据您的设置和使用需求</li>
                    <li>日志信息：通常保存不超过12个月</li>
                    <li>当您注销账户后，我们会删除或匿名化处理您的个人信息</li>
                </ul>

                <h3>2. 保存地域</h3>
                <p>您的个人信息将存储在中华人民共和国境内。如需跨境传输，我们将会遵循相关法律法规的要求。</p>

                <h3>3. 超期处理</h3>
                <p>当我们的产品或服务发生停止运营的情形时，我们将采取合适的方式（包括但不限于推送通知、公告等形式）通知您，并在合理的期限内删除或匿名化处理您的个人信息。</p>
            </section>

            <section id="security" class="section">
                <h2>我们如何保护您的个人信息</h2>
                <p>我们非常重视个人信息安全，采用符合业界标准的安全保护措施：</p>

                <h3>1. 技术措施</h3>
                <ul>
                    <li>数据加密：采用SSL/TLS加密传输，敏感数据加密存储</li>
                    <li>访问控制：实施严格的数据访问权限管理</li>
                    <li>安全审计：定期进行安全评估和漏洞扫描</li>
                    <li>备份恢复：建立完善的数据备份和灾难恢复机制</li>
                </ul>

                <h3>2. 管理措施</h3>
                <ul>
                    <li>建立专门的信息安全团队</li>
                    <li>制定完善的数据安全管理制度</li>
                    <li>对员工进行定期的安全培训</li>
                    <li>签署保密协议，限制数据访问范围</li>
                </ul>

                <h3>3. 安全事件处理</h3>
                <p>若发生个人信息安全事件，我们将：</p>
                <ul>
                    <li>立即启动应急预案，阻止安全事件扩大</li>
                    <li>及时评估事件影响，采取补救措施</li>
                    <li>按照法律法规要求，及时向相关部门报告</li>
                    <li>以邮件、短信、推送通知等方式告知您安全事件的基本情况、可能的影响、我们已采取或将要采取的处置措施等</li>
                </ul>
            </section>

            <section id="rights" class="section">
                <h2>您的权利</h2>
                <p>按照中国相关的法律、法规、标准，我们保障您对自己的个人信息行使以下权利：</p>

                <h3>1. 访问权</h3>
                <p>您有权访问您的个人信息，法律法规规定的例外情况除外。您可以通过以下方式自行访问您的个人信息：</p>
                <ul>
                    <li>账户信息：您可以在设置页面查看和编辑您的基本账户信息</li>
                    <li>项目数据：您可以随时查看、导出您创建的项目</li>
                    <li>使用记录：您可以在相关页面查看您的操作历史</li>
                </ul>

                <h3>2. 更正权</h3>
                <p>当您发现我们处理的关于您的个人信息有错误时，您有权要求我们做出更正。您可以通过设置页面或联系我们进行更正。</p>

                <h3>3. 删除权</h3>
                <p>在以下情形中，您可以向我们提出删除个人信息的请求：</p>
                <ul>
                    <li>我们处理个人信息的行为违反法律法规</li>
                    <li>我们收集、使用您的个人信息，却未征得您的同意</li>
                    <li>我们处理个人信息的行为违反了与您的约定</li>
                    <li>您不再使用我们的产品或服务，或您注销了账号</li>
                </ul>

                <h3>4. 撤回同意权</h3>
                <p>您可以通过删除信息、关闭设备功能、在设置中进行隐私设置等方式改变您授权我们继续收集个人信息的范围或撤回您的授权。</p>

                <h3>5. 注销权</h3>
                <p>您可以随时注销您的账户。账户注销后，我们将停止为您提供产品或服务，并根据您的要求删除您的个人信息。</p>
            </section>

            <section id="cookies" class="section">
                <h2>Cookie和类似技术</h2>
                <p>为确保网站正常运转，我们会在您的计算机或移动设备上存储名为Cookie的小数据文件。</p>

                <h3>1. Cookie的使用</h3>
                <ul>
                    <li><strong>必要Cookie</strong>：这些Cookie对于网站功能是必需的，无法在我们的系统中关闭</li>
                    <li><strong>功能Cookie</strong>：这些Cookie使网站能够提供增强的功能和个性化</li>
                    <li><strong>分析Cookie</strong>：这些Cookie帮助我们了解访问者如何与网站互动</li>
                </ul>

                <h3>2. 管理Cookie</h3>
                <p>您可以通过浏览器设置管理Cookie：</p>
                <ul>
                    <li>大多数浏览器允许您查看、管理和删除Cookie</li>
                    <li>您可以设置浏览器阻止所有Cookie或在发送Cookie时提示您</li>
                    <li>请注意，禁用Cookie可能影响网站的正常功能</li>
                </ul>
            </section>

            <section id="minors" class="section">
                <h2>未成年人的个人信息保护</h2>
                <p>我们非常重视对未成年人个人信息的保护。如果您是18周岁以下的未成年人，建议您请您的父母或监护人仔细阅读本隐私政策，并在征得您的父母或监护人同意后使用我们的服务或向我们提供信息。</p>

                <p>对于经父母或监护人同意使用我们的产品或服务而收集未成年人个人信息的情况，我们只会在法律法规允许、父母或监护人明确同意或者保护未成年人所必要的情况下使用、共享、转让或披露此信息。</p>

                <p>如果我们发现自己在未事先获得可证实的父母或监护人同意的情况下收集了未成年人的个人信息，则会设法尽快删除相关数据。</p>
            </section>

            <section id="updates" class="section">
                <h2>本隐私政策如何更新</h2>
                <p>我们可能适时修订本政策的条款，该等修订构成本隐私政策的一部分。如该等修订造成您在本隐私政策下权利的实质减少，我们将在修订生效前通过在主页上显著位置提示或向您发送电子邮件或以其他方式通知您。</p>

                <p>在该种情况下，若您继续使用我们的服务，即表示同意受经修订的本隐私政策的约束。</p>

                <div class="highlight">
                    <strong>版本更新记录：</strong><br>
                    v1.0 (2025年7月29日)：初始版本发布
                </div>
            </section>

            <section id="contact" class="section">
                <h2>如何联系我们</h2>
                <p>如果您对本隐私政策有任何疑问、意见或建议，或您需要访问、更正、删除您的个人信息，或撤回您的授权同意，请通过以下方式与我们联系：</p>

                <div class="contact-info">
                    <h4>联系方式</h4>
                    <p><strong>邮箱：</strong> <EMAIL></p>
                    <p><strong>地址：</strong> 中国</p>
                    <p><strong>邮编：</strong> 100000</p>
                </div>

                <p>我们将在收到您的请求后，在15个工作日内回复您的请求。</p>

                <p>如果您对我们的回复不满意，特别是我们的个人信息处理行为损害了您的合法权益，您还可以向网信、电信、公安及工商等监管部门进行投诉或举报。</p>
            </section>
        </main>
    </div>

    <script>
        // 导航高亮
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');

            // 点击导航链接
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });

                        // 更新活跃状态
                        navLinks.forEach(l => l.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });

            // 滚动时更新导航状态
            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>