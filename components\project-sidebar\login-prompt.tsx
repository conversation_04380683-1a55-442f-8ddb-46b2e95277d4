"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { AuthModal } from "@/components/auth-modal";
import { useUser } from "@/loomrunhooks/useUser";
import { LogIn } from "lucide-react";

interface LoginPromptProps {
  onClose: () => void;
}

export function LoginPrompt({ onClose }: LoginPromptProps) {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const { refreshUser } = useUser();

  const handleLoginSuccess = async () => {
    // AuthModal 已经处理了用户状态刷新，这里只需要关闭相关UI
    setAuthModalOpen(false);
    onClose(); // 关闭侧边栏
  };

  return (
    <>
      {/* 简约的登录提示区域 - 替代项目列表显示 */}
      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="w-12 h-12 bg-zinc-800 rounded-lg flex items-center justify-center mb-4">
          <LogIn className="w-5 h-5 text-zinc-400" />
        </div>
        
        <h3 className="text-base font-medium text-white mb-2">
          请先登录
        </h3>
        
        <p className="text-sm text-zinc-400 mb-6 leading-relaxed">
          登录后可保存项目、查看历史记录<br />
          和参与社区分享
        </p>
        
        <Button
          onClick={() => setAuthModalOpen(true)}
          className="bg-zinc-700 hover:bg-zinc-600 text-white border-0 px-6 py-2"
        >
          登录
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-zinc-500 hover:text-zinc-400 mt-3"
        >
          稍后再说
        </Button>
      </div>

      {/* 认证模态框 */}
      <AuthModal
        open={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        onSuccess={handleLoginSuccess}
        title="登录 LoomRun"
        description="登录后即可保存项目、管理历史记录"
      />
    </>
  );
} 