/* 订阅弹窗动画效果 - 移除入场动画 */

/* 卡片悬停效果 */
@keyframes cardHover {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(-4px) scale(1.02);
  }
}

/* 选中卡片脉冲效果 */
@keyframes selectedPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* 标签闪烁动画 */
@keyframes tagBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.7;
  }
}

/* 渐变背景动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 加载动画增强 */
@keyframes spinEnhanced {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 按钮点击波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 数字跳动效果 */
@keyframes numberBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 图标旋转效果 */
@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 应用动画的类 */
.modal-enter {
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.backdrop-enter {
  animation: backdropFadeIn 0.3s ease-out;
}

.card-hover:hover {
  animation: cardHover 0.3s ease-out forwards;
}

.selected-pulse {
  animation: selectedPulse 2s infinite;
}

.tag-blink {
  animation: tagBlink 1.5s infinite;
}

.gradient-animated {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.spin-enhanced {
  animation: spinEnhanced 1s linear infinite;
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

.number-bounce {
  animation: numberBounce 1s ease-in-out;
}

.icon-spin {
  animation: iconSpin 2s linear infinite;
}

/* 浅色模式特定动画 */
@media (prefers-color-scheme: light) {
  .selected-pulse {
    animation: selectedPulseLightMode 2s infinite;
  }

  @keyframes selectedPulseLightMode {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
    }
  }
}

/* 深色模式特定动画 */
@media (prefers-color-scheme: dark) {
  .selected-pulse {
    animation: selectedPulse 2s infinite;
  }

  @keyframes selectedPulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(96, 165, 250, 0);
    }
  }
}

/* 切换滑块动画优化 */
@keyframes switchSlideLight {
  0% {
    transform: translateX(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: translateX(100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

@keyframes switchSlideDark {
  0% {
    transform: translateX(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: translateX(100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}

/* 响应式动画调整 */
@media (max-width: 768px) {
  .modal-enter {
    animation: modalSlideIn 0.3s ease-out;
  }
  
  .card-hover:hover {
    animation: none;
    transform: scale(1.01);
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 彻底隐藏数字输入框的上下箭头 - 全局样式 */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
  position: absolute !important;
  right: -9999px !important;
  width: 0 !important;
  height: 0 !important;
}

input[type="number"] {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
  position: absolute !important;
  right: -9999px !important;
  width: 0 !important;
  height: 0 !important;
}

.quantity-input {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}

/* 动画支付按钮样式 - 按照原始效果实现 */
.animated-pay-btn {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: none;
  transition-duration: 0.3s;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.103);
}

.animated-pay-btn::before {
  width: 100%;
  height: 100%;
  position: absolute;
  content: "";
  background-color: white;
  border-radius: 50%;
  left: -100%;
  top: 0;
  transition-duration: 0.3s;
  mix-blend-mode: difference;
}

.animated-pay-btn:hover::before {
  transition-duration: 0.3s;
  transform: translate(100%, -50%);
  border-radius: 0;
}

.animated-pay-btn:active {
  transform: translate(5px, 5px);
  transition-duration: 0.3s;
}
