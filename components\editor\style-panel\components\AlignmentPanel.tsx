"use client";
import { useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  AlignHorizontalJustifyCenter,
  AlignHorizontalJustifyStart,
  AlignHorizontalJustifyEnd
} from "lucide-react";

interface AlignmentPanelProps {
  selectedElement: HTMLElement | null;
  onStyleChange: (key: string, value: string) => void;
}

interface AlignmentOption {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  styles: Record<string, string>;
}

export function AlignmentPanel({ selectedElement, onStyleChange }: AlignmentPanelProps) {

  // 简化的对齐选项 - 只保留最常用的
  const alignmentOptions: AlignmentOption[] = [
    {
      icon: AlignLeft,
      label: "左对齐",
      styles: { 'text-align': 'left' }
    },
    {
      icon: AlignCenter,
      label: "居中",
      styles: { 'text-align': 'center' }
    },
    {
      icon: AlignRight,
      label: "右对齐",
      styles: { 'text-align': 'right' }
    },
    {
      icon: AlignJustify,
      label: "两端",
      styles: { 'text-align': 'justify' }
    }
  ];

  // 快速居中选项
  const quickCenterOptions: AlignmentOption[] = [
    {
      icon: AlignHorizontalJustifyCenter,
      label: "快速居中",
      styles: {
        'display': 'flex',
        'justify-content': 'center',
        'align-items': 'center',
        'min-height': 'screen'
      }
    }
  ];

  // 应用对齐样式
  const applyAlignment = useCallback((option: AlignmentOption) => {
    console.log(`🎯 应用对齐样式: ${option.label}`, option.styles);

    // 批量应用样式
    Object.entries(option.styles).forEach(([property, value]) => {
      onStyleChange(property, value);
    });
  }, [onStyleChange]);

  // 判断元素类型
  const getElementType = useCallback((element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    const isTextElement = ['p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a', 'label', 'button'].includes(tagName);

    return { isTextElement };
  }, []);

  // 渲染对齐按钮 - 简洁版本，图标+文字
  const renderAlignmentButton = useCallback((option: AlignmentOption) => {
    const Icon = option.icon;
    return (
      <Button
        key={option.label}
        variant="outline"
        size="sm"
        onClick={() => applyAlignment(option)}
        className="h-8 px-3 flex items-center space-x-2 border-border hover:bg-muted hover:border-primary/50 transition-all duration-200 text-xs"
        title={option.label}
      >
        <Icon className="w-3 h-3" />
        <span>{option.label}</span>
      </Button>
    );
  }, [applyAlignment]);

  if (!selectedElement) {
    return null;
  }

  const { isTextElement } = getElementType(selectedElement);

  return (
    <div className="space-y-3">
      {/* 文本对齐 */}
      <div className="space-y-3">
        <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
          文本对齐
        </h3>
        <div className="grid grid-cols-2 gap-2">
          {alignmentOptions.map(renderAlignmentButton)}
        </div>
      </div>

      {/* 快速居中 */}
      <div className="space-y-3">
        <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
          快速居中
        </h3>
        <div className="grid grid-cols-1 gap-2">
          {quickCenterOptions.map(renderAlignmentButton)}
        </div>
      </div>
    </div>
  );
}
