import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';
import { addPointsTransactionWithExpiry } from '@/lib/points-service';
import { getNumberSetting } from '@/lib/points-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const orderId = parseInt(id);
    
    if (isNaN(orderId)) {
      return NextResponse.json({ success: false, error: "订单ID无效" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, error: "用户不存在" }, { status: 401 });
    }

    const { payment_method = 'mock' } = await request.json();

    // 模拟支付延迟
    const paymentDelay = await getNumberSetting('mock_payment_delay_seconds', 3);
    await new Promise(resolve => setTimeout(resolve, paymentDelay * 1000));

    // 开始数据库事务
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    try {
      await connection.beginTransaction();

      // 先尝试从订阅订单表查找
      const [subscriptionRows] = await connection.execute(
        `SELECT id, order_no, membership_type, plan_key, duration_months, 
                discount_price, status, order_expires_at
         FROM membership_orders 
         WHERE id = ? AND user_id = ? AND status = 'pending'`,
        [orderId, user.id]
      );

      if (subscriptionRows.length > 0) {
        const order = subscriptionRows[0];
        
        // 检查订单是否过期
        if (new Date() > new Date(order.order_expires_at)) {
          await connection.rollback();
          return NextResponse.json({ success: false, error: "订单已过期" }, { status: 400 });
        }

        // 获取计划信息
        const [planRows] = await connection.execute(
          `SELECT points_included, points_validity_days FROM subscription_plans WHERE plan_key = ?`,
          [order.plan_key]
        );

        if (planRows.length === 0) {
          await connection.rollback();
          return NextResponse.json({ success: false, error: "计划不存在" }, { status: 404 });
        }

        const plan = planRows[0];

        // 更新订单状态
        await connection.execute(
          `UPDATE membership_orders 
           SET status = 'paid', payment_method = ?, paid_at = NOW(), updated_at = NOW()
           WHERE id = ?`,
          [payment_method, orderId]
        );

        // 计算会员到期时间
        const membershipEndDate = new Date();
        membershipEndDate.setMonth(membershipEndDate.getMonth() + order.duration_months);

        // 创建会员记录
        await connection.execute(
          `INSERT INTO user_subscriptions 
           (user_id, plan_key, order_id, start_date, end_date, status, created_at, updated_at)
           VALUES (?, ?, ?, NOW(), ?, 'active', NOW(), NOW())`,
          [user.id, order.plan_key, orderId, membershipEndDate]
        );

        // 发放积分（如果有）
        if (plan.points_included > 0) {
          const pointsExpiresAt = new Date();
          pointsExpiresAt.setDate(pointsExpiresAt.getDate() + plan.points_validity_days);

          // 创建积分余额记录
          const [balanceResult] = await connection.execute(
            `INSERT INTO user_points_balance
             (user_id, points_type, points_amount, expires_at, source_order_id, source_plan_key)
             VALUES (?, 'subscription', ?, ?, ?, ?)`,
            [user.id, plan.points_included, pointsExpiresAt, orderId, order.plan_key]
          );

          // 创建积分交易记录
          const [userRows] = await connection.execute(
            'SELECT points FROM users WHERE id = ?',
            [user.id]
          );
          const currentPoints = userRows[0].points;
          const newPoints = currentPoints + plan.points_included;

          await connection.execute(
            `INSERT INTO points_transactions
             (user_id, transaction_type, points_amount, balance_before, balance_after,
              source_type, points_type, expires_at, balance_record_id, source_id, description)
             VALUES (?, 'earn', ?, ?, ?, 'subscription', 'subscription', ?, ?, ?, ?)`,
            [
              user.id,
              plan.points_included,
              currentPoints,
              newPoints,
              pointsExpiresAt,
              balanceResult.insertId,
              orderId.toString(),
              `订阅${order.membership_type.toUpperCase()}会员获得${plan.points_included}积分`
            ]
          );

          // 更新用户总积分
          await connection.execute(
            'UPDATE users SET points = ?, total_earned_points = total_earned_points + ? WHERE id = ?',
            [newPoints, plan.points_included, user.id]
          );
        }

        await connection.commit();

        return NextResponse.json({
          success: true,
          data: {
            order_id: orderId,
            order_no: order.order_no,
            amount: order.discount_price,
            membership_type: order.membership_type,
            duration_months: order.duration_months,
            points_included: plan.points_included
          }
        });
      }

      // 再尝试从充值订单表查找
      const [rechargeRows] = await connection.execute(
        `SELECT id, order_no, package_key, points_amount, bonus_points, points_validity_days,
                discount_price, status, order_expires_at
         FROM recharge_orders 
         WHERE id = ? AND user_id = ? AND status = 'pending'`,
        [orderId, user.id]
      );

      if (rechargeRows.length > 0) {
        const order = rechargeRows[0];
        
        // 检查订单是否过期
        if (new Date() > new Date(order.order_expires_at)) {
          await connection.rollback();
          return NextResponse.json({ success: false, error: "订单已过期" }, { status: 400 });
        }

        // 更新订单状态
        await connection.execute(
          `UPDATE recharge_orders 
           SET status = 'paid', payment_method = ?, paid_at = NOW(), updated_at = NOW()
           WHERE id = ?`,
          [payment_method, orderId]
        );

        // 计算积分有效期
        const pointsExpiresAt = order.points_validity_days > 0 
          ? new Date(Date.now() + order.points_validity_days * 24 * 60 * 60 * 1000)
          : null; // 永久有效

        const totalPoints = order.points_amount + order.bonus_points;

        // 创建积分余额记录
        const [balanceResult] = await connection.execute(
          `INSERT INTO user_points_balance
           (user_id, points_type, points_amount, expires_at, source_order_id)
           VALUES (?, 'recharge', ?, ?, ?)`,
          [user.id, totalPoints, pointsExpiresAt, orderId]
        );

        // 创建积分交易记录
        const [userRows] = await connection.execute(
          'SELECT points FROM users WHERE id = ?',
          [user.id]
        );
        const currentPoints = userRows[0].points;
        const newPoints = currentPoints + totalPoints;

        await connection.execute(
          `INSERT INTO points_transactions
           (user_id, transaction_type, points_amount, balance_before, balance_after,
            source_type, points_type, expires_at, balance_record_id, source_id, description)
           VALUES (?, 'earn', ?, ?, ?, 'recharge', 'recharge', ?, ?, ?, ?)`,
          [
            user.id,
            totalPoints,
            currentPoints,
            newPoints,
            pointsExpiresAt,
            balanceResult.insertId,
            orderId.toString(),
            `充值获得${order.points_amount}积分${order.bonus_points > 0 ? ` + ${order.bonus_points}赠送积分` : ''}`
          ]
        );

        // 更新用户总积分
        await connection.execute(
          'UPDATE users SET points = ?, total_earned_points = total_earned_points + ? WHERE id = ?',
          [newPoints, totalPoints, user.id]
        );

        await connection.commit();

        return NextResponse.json({
          success: true,
          data: {
            order_id: orderId,
            order_no: order.order_no,
            amount: order.discount_price,
            points_amount: order.points_amount,
            bonus_points: order.bonus_points,
            total_points: totalPoints
          }
        });
      }

      await connection.rollback();
      return NextResponse.json({ success: false, error: "订单不存在或已处理" }, { status: 404 });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('支付处理失败:', error);
    return NextResponse.json(
      { success: false, error: '支付处理失败' },
      { status: 500 }
    );
  }
}
