import { NextResponse } from "next/server";

export async function POST() {
  try {
    const response = NextResponse.json(
      { success: true, message: "登出成功" },
      { status: 200 }
    );

    // 清除JWT Token Cookie
    response.cookies.set("loomrun_token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0,
      path: "/"
    });

    return response;
  } catch (error) {
    console.error("登出API错误:", error);
    return NextResponse.json(
      { success: false, message: "登出失败" },
      { status: 500 }
    );
  }
} 