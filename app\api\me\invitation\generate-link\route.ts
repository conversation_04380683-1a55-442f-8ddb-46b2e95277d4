import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { generateInvitationLink } from "@/lib/invitation-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function POST() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const result = await generateInvitationLink(user.id);
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        inviteUrl: result.inviteUrl
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('生成邀请链接失败:', error);
    return NextResponse.json({
      success: false,
      message: '生成邀请链接失败'
    }, { status: 500 });
  }
}
