import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const orderId = parseInt(id);
    
    if (isNaN(orderId)) {
      return NextResponse.json({ success: false, error: "订单ID无效" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, error: "用户不存在" }, { status: 401 });
    }

    // 先尝试取消订阅订单
    const subscriptionResult = await executeQuery(
      `UPDATE membership_orders 
       SET status = 'cancelled', updated_at = NOW()
       WHERE id = ? AND user_id = ? AND status = 'pending'`,
      [orderId, user.id]
    ) as any;

    if (subscriptionResult.affectedRows > 0) {
      return NextResponse.json({
        success: true,
        message: "订阅订单已取消"
      });
    }

    // 再尝试取消充值订单
    const rechargeResult = await executeQuery(
      `UPDATE recharge_orders 
       SET status = 'cancelled', updated_at = NOW()
       WHERE id = ? AND user_id = ? AND status = 'pending'`,
      [orderId, user.id]
    ) as any;

    if (rechargeResult.affectedRows > 0) {
      return NextResponse.json({
        success: true,
        message: "充值订单已取消"
      });
    }

    return NextResponse.json({ 
      success: false, 
      error: "订单不存在或无法取消" 
    }, { status: 404 });

  } catch (error) {
    console.error('取消订单失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
