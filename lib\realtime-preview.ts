/**
 * 🎯 实时预览系统 - 无刷新的丝滑样式应用
 * 
 * 核心思想：
 * 1. 不刷新iframe，直接操作DOM
 * 2. 实时应用样式变化
 * 3. 维护撤销/重做历史
 * 4. 确保状态同步
 */

export interface StyleChange {
  element: HTMLElement;
  property: string;
  oldValue: string;
  newValue: string;
  timestamp: number;
}

export interface RealtimePreviewState {
  changes: StyleChange[];
  currentIndex: number;
}

export class RealtimePreviewManager {
  private iframe: HTMLIFrameElement | null = null;
  private iframeDoc: Document | null = null;
  private styleChanges: StyleChange[] = [];
  private currentIndex: number = -1;
  private onStateChange?: (canUndo: boolean, canRedo: boolean) => void;

  constructor(iframe: HTMLIFrameElement) {
    this.iframe = iframe;
    this.iframeDoc = iframe.contentDocument;
    console.log('🎯 实时预览管理器初始化', {
      hasIframe: !!this.iframe,
      hasDoc: !!this.iframeDoc
    });
  }

  /**
   * 设置状态变化回调
   */
  setOnStateChange(callback: (canUndo: boolean, canRedo: boolean) => void) {
    this.onStateChange = callback;
  }

  /**
   * 实时应用样式 - 核心方法
   */
  applyStyleRealtime(
    element: HTMLElement, 
    property: string, 
    value: string,
    saveToHistory: boolean = true
  ): boolean {
    if (!this.iframeDoc || !element) {
      console.warn('❌ 无法应用样式：缺少iframe文档或元素');
      return false;
    }

    try {
      // 1. 获取旧值
      const oldValue = element.style.getPropertyValue(property) || '';
      
      // 2. 如果值相同，跳过（但撤销/重做操作除外）
      if (oldValue === value && saveToHistory) {
        console.log('⏭️ 跳过相同样式值', { property, value });
        return false;
      }

      // 3. 保存到历史记录（在应用前）
      if (saveToHistory) {
        this.saveStyleChange(element, property, oldValue, value);
      }

      // 4. 🎯 关键：直接应用样式，无需刷新
      element.style.setProperty(property, value, 'important');

      console.log('✅ 实时样式已应用', {
        element: element.tagName,
        className: element.className,
        property,
        oldValue,
        newValue: value,
        saveToHistory
      });

      // 5. 触发状态更新回调
      this.notifyStateChange();

      return true;
    } catch (error) {
      console.error('❌ 应用样式失败:', error);
      return false;
    }
  }

  /**
   * 保存样式变化到历史记录
   */
  private saveStyleChange(
    element: HTMLElement,
    property: string,
    oldValue: string,
    newValue: string
  ) {
    // 如果当前不在历史记录末尾，删除后面的记录
    if (this.currentIndex < this.styleChanges.length - 1) {
      this.styleChanges = this.styleChanges.slice(0, this.currentIndex + 1);
    }

    // 添加新的变化记录
    const change: StyleChange = {
      element,
      property,
      oldValue,
      newValue,
      timestamp: Date.now()
    };

    this.styleChanges.push(change);
    this.currentIndex = this.styleChanges.length - 1;

    // 限制历史记录大小
    if (this.styleChanges.length > 50) {
      this.styleChanges = this.styleChanges.slice(-50);
      this.currentIndex = this.styleChanges.length - 1;
    }

    console.log('📝 样式变化已保存到历史', {
      historyLength: this.styleChanges.length,
      currentIndex: this.currentIndex,
      change: {
        element: element.tagName,
        property,
        oldValue,
        newValue
      }
    });
  }

  /**
   * 撤销样式变化
   */
  undo(): boolean {
    if (!this.canUndo()) {
      console.log('❌ 无法撤销：没有可撤销的变化', {
        currentIndex: this.currentIndex,
        historyLength: this.styleChanges.length
      });
      return false;
    }

    try {
      const change = this.styleChanges[this.currentIndex];

      console.log('🎯 开始撤销操作', {
        currentIndex: this.currentIndex,
        change: {
          element: change.element.tagName,
          property: change.property,
          currentValue: change.newValue,
          revertTo: change.oldValue
        }
      });

      // 🎯 关键修复：直接应用样式，不通过applyStyleRealtime避免循环
      change.element.style.setProperty(change.property, change.oldValue, 'important');

      // 更新索引
      this.currentIndex--;

      console.log('✅ 样式撤销成功', {
        currentIndex: this.currentIndex,
        change: {
          element: change.element.tagName,
          property: change.property,
          revertedTo: change.oldValue
        }
      });

      // 通知状态变化
      this.notifyStateChange();
      return true;
    } catch (error) {
      console.error('❌ 撤销失败:', error);
    }

    return false;
  }

  /**
   * 重做样式变化
   */
  redo(): boolean {
    if (!this.canRedo()) {
      console.log('❌ 无法重做：没有可重做的变化', {
        currentIndex: this.currentIndex,
        historyLength: this.styleChanges.length
      });
      return false;
    }

    try {
      const nextIndex = this.currentIndex + 1;
      const change = this.styleChanges[nextIndex];

      console.log('🎯 开始重做操作', {
        currentIndex: this.currentIndex,
        nextIndex,
        change: {
          element: change.element.tagName,
          property: change.property,
          applyValue: change.newValue
        }
      });

      // 🎯 关键修复：直接应用样式，不通过applyStyleRealtime避免循环
      change.element.style.setProperty(change.property, change.newValue, 'important');

      // 更新索引
      this.currentIndex = nextIndex;

      console.log('✅ 样式重做成功', {
        currentIndex: this.currentIndex,
        change: {
          element: change.element.tagName,
          property: change.property,
          appliedValue: change.newValue
        }
      });

      // 通知状态变化
      this.notifyStateChange();
      return true;
    } catch (error) {
      console.error('❌ 重做失败:', error);
    }

    return false;
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return this.currentIndex >= 0;
  }

  /**
   * 检查是否可以重做
   */
  canRedo(): boolean {
    return this.currentIndex < this.styleChanges.length - 1;
  }

  /**
   * 获取当前状态
   */
  getState(): RealtimePreviewState {
    return {
      changes: [...this.styleChanges],
      currentIndex: this.currentIndex
    };
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.styleChanges = [];
    this.currentIndex = -1;
    this.notifyStateChange();
    console.log('🧹 样式历史记录已清空');
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange() {
    if (this.onStateChange) {
      this.onStateChange(this.canUndo(), this.canRedo());
    }
  }

  /**
   * 批量应用样式（用于初始化或重置）
   */
  applyStylesBatch(styles: Array<{
    element: HTMLElement;
    property: string;
    value: string;
  }>) {
    console.log('🎯 批量应用样式', { count: styles.length });
    
    styles.forEach(({ element, property, value }) => {
      this.applyStyleRealtime(element, property, value, false);
    });

    this.notifyStateChange();
  }

  /**
   * 获取元素的当前样式值
   */
  getCurrentStyle(element: HTMLElement, property: string): string {
    return element.style.getPropertyValue(property) || '';
  }

  /**
   * 检查元素是否存在于iframe中
   */
  isElementValid(element: HTMLElement): boolean {
    if (!this.iframeDoc) return false;
    return this.iframeDoc.contains(element);
  }

  /**
   * 更新iframe引用（当iframe重新加载时）
   */
  updateIframe(iframe: HTMLIFrameElement) {
    this.iframe = iframe;
    this.iframeDoc = iframe.contentDocument;
    console.log('🔄 iframe引用已更新', {
      hasIframe: !!this.iframe,
      hasDoc: !!this.iframeDoc
    });
  }
}
