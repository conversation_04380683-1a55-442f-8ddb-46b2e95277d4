# Dependencies
node_modules/
/.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Admin system - prevent from being uploaded to GitHub
LoomRun_admin/

# Production builds
/.next/
/out/
/build/
/dist/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
/coverage/
*.lcov

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example

# Vercel
.vercel

# Turbo
.turbo

# Next.js
.next/
out/

# Nuxt.js
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# SSL certificates (if any)
*.pem
*.key
*.crt

# Database
*.sqlite
*.sqlite3
*.db

# Cache
.cache/

# Backup files
*.bak
*.backup

# Project specific
/scripts/ssl/
/uploads/
/public/uploads/
# 但保留目录结构文件
!/public/uploads/.gitkeep
!/public/uploads/*/.gitkeep

# Development files
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Deployment files
deploy.sh
deploy.bat 