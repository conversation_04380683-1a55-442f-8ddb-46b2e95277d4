/**
 * 颜色处理工具类 - 按照测试页面成功逻辑实现
 * 解决RGB颜色空格问题和TailwindCSS类转换
 */

export class ColorUtils {
  /**
   * 🔧 核心方法：清理颜色值中的空格，确保CSS类名有效
   * @param colorValue 原始颜色值
   * @returns 清理后的颜色值
   */
  static cleanColorValue(colorValue: string): string {
    if (!colorValue || typeof colorValue !== 'string') {
      return colorValue;
    }

    // 移除所有空格，确保CSS类名有效
    const cleaned = colorValue.replace(/\s+/g, '');
    
    console.log(`🎨 颜色值清理: "${colorValue}" → "${cleaned}"`);
    return cleaned;
  }

  /**
   * 🔧 将CSS属性和颜色值转换为TailwindCSS类
   * @param property CSS属性名
   * @param colorValue 颜色值
   * @returns TailwindCSS类名或null
   */
  static convertColorToTailwindClass(property: string, colorValue: string): string | null {
    const cleanValue = this.cleanColorValue(colorValue);
    
    switch (property) {
      case 'background-color':
      case 'backgroundColor':
        return `bg-[${cleanValue}]`;
      
      case 'color':
        return `text-[${cleanValue}]`;
      
      case 'border-color':
      case 'borderColor':
        return `border-[${cleanValue}]`;
      
      default:
        return null;
    }
  }

  /**
   * 🔧 检测颜色格式类型
   * @param colorValue 颜色值
   * @returns 颜色格式类型
   */
  static detectColorFormat(colorValue: string): 'hex' | 'rgb' | 'rgba' | 'hsl' | 'hsla' | 'unknown' {
    if (!colorValue) return 'unknown';
    
    const cleaned = colorValue.toLowerCase().replace(/\s+/g, '');
    
    if (cleaned.startsWith('#')) return 'hex';
    if (cleaned.startsWith('rgb(')) return 'rgb';
    if (cleaned.startsWith('rgba(')) return 'rgba';
    if (cleaned.startsWith('hsl(')) return 'hsl';
    if (cleaned.startsWith('hsla(')) return 'hsla';
    
    return 'unknown';
  }

  /**
   * 🔧 验证颜色值是否有效
   * @param colorValue 颜色值
   * @returns 是否有效
   */
  static isValidColor(colorValue: string): boolean {
    if (!colorValue) return false;
    
    try {
      // 创建临时元素测试颜色值
      const tempElement = document.createElement('div');
      tempElement.style.color = colorValue;
      
      // 如果浏览器接受这个颜色值，style.color会被设置
      return tempElement.style.color !== '';
    } catch {
      return false;
    }
  }

  /**
   * 🔧 移除元素上的旧颜色类
   * @param element HTML元素
   * @param property CSS属性
   */
  static removeOldColorClasses(element: HTMLElement, property: string): void {
    const classesToRemove: string[] = [];
    
    element.classList.forEach(cls => {
      switch (property) {
        case 'background-color':
        case 'backgroundColor':
          if (cls.startsWith('bg-[') || (cls.startsWith('bg-') && cls.includes('#'))) {
            classesToRemove.push(cls);
          }
          break;
        
        case 'color':
          if (cls.startsWith('text-[') && (cls.includes('#') || cls.includes('rgb') || cls.includes('hsl'))) {
            classesToRemove.push(cls);
          }
          break;
        
        case 'border-color':
        case 'borderColor':
          if (cls.startsWith('border-[') && (cls.includes('#') || cls.includes('rgb') || cls.includes('hsl'))) {
            classesToRemove.push(cls);
          }
          break;
      }
    });
    
    classesToRemove.forEach(cls => {
      element.classList.remove(cls);
    });
    
    if (classesToRemove.length > 0) {
      console.log(`🗑️ 已移除旧颜色类: ${classesToRemove.join(' ')}`);
    }
  }

  /**
   * 🔧 应用颜色到元素（按照测试页面逻辑）
   * @param element HTML元素
   * @param property CSS属性
   * @param colorValue 颜色值
   * @param previewOnly 是否仅预览
   */
  static applyColorToElement(
    element: HTMLElement, 
    property: string, 
    colorValue: string, 
    previewOnly: boolean = false
  ): void {
    const cleanValue = this.cleanColorValue(colorValue);
    
    if (previewOnly) {
      // 仅预览：设置内联样式
      element.style.setProperty(property, cleanValue);
      console.log(`👀 预览颜色: ${property} = ${cleanValue}`);
    } else {
      // 正式应用：转换为TailwindCSS类
      const tailwindClass = this.convertColorToTailwindClass(property, cleanValue);
      
      if (tailwindClass) {
        // 移除旧的颜色类
        this.removeOldColorClasses(element, property);
        
        // 添加新的TailwindCSS类
        element.classList.add(tailwindClass);
        
        // 移除内联样式
        element.style.removeProperty(property);
        
        console.log(`🎯 应用TailwindCSS颜色类: ${property}: ${cleanValue} → ${tailwindClass}`);
      } else {
        // 回退到内联样式
        element.style.setProperty(property, cleanValue);
        console.log(`📝 应用内联颜色样式: ${property} = ${cleanValue}`);
      }
    }
  }

  /**
   * 🔧 批量应用颜色样式
   * @param element HTML元素
   * @param colorStyles 颜色样式对象
   * @param previewOnly 是否仅预览
   */
  static applyBatchColors(
    element: HTMLElement, 
    colorStyles: Record<string, string>, 
    previewOnly: boolean = false
  ): void {
    Object.entries(colorStyles).forEach(([property, value]) => {
      this.applyColorToElement(element, property, value, previewOnly);
    });
  }

  /**
   * 🔧 获取元素的当前颜色值
   * @param element HTML元素
   * @param property CSS属性
   * @returns 当前颜色值
   */
  static getCurrentColor(element: HTMLElement, property: string): string {
    // 优先从计算样式获取
    const computedStyle = window.getComputedStyle(element);
    const computedValue = computedStyle.getPropertyValue(property);
    
    if (computedValue && computedValue !== 'rgba(0, 0, 0, 0)' && computedValue !== 'transparent') {
      return computedValue;
    }
    
    // 回退到内联样式
    return element.style.getPropertyValue(property) || '';
  }

  /**
   * 🔧 测试RGB颜色处理（用于调试）
   * @param testColors 测试颜色数组
   */
  static testRGBProcessing(testColors: string[]): void {
    console.log('🧪 开始RGB颜色处理测试...');
    
    testColors.forEach((color, index) => {
      const cleaned = this.cleanColorValue(color);
      const format = this.detectColorFormat(color);
      const isValid = this.isValidColor(cleaned);
      const tailwindClass = this.convertColorToTailwindClass('background-color', color);
      
      console.log(`测试 ${index + 1}:`, {
        original: color,
        cleaned,
        format,
        isValid,
        tailwindClass
      });
    });
    
    console.log('✅ RGB颜色处理测试完成');
  }
}

// 🔧 导出常用的颜色格式测试数据
export const TEST_COLORS = [
  '#ff6b6b',
  'rgb(255, 107, 107)',
  'rgba(255, 107, 107, 0.8)',
  'hsl(0, 100%, 70%)',
  'hsla(0, 100%, 70%, 0.9)',
  'rgb(69,183,209)',
  'rgba(150,206,180,0.9)'
];

// 🔧 默认导出
export default ColorUtils;
