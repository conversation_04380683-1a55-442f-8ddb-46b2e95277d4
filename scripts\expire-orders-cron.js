#!/usr/bin/env node

/**
 * 订单过期检查定时任务脚本
 * 
 * 使用方法：
 * 1. 直接运行: node scripts/expire-orders-cron.js
 * 2. 添加到crontab: */5 * * * * /usr/bin/node /path/to/your/project/scripts/expire-orders-cron.js
 * 3. 使用PM2: pm2 start scripts/expire-orders-cron.js --cron "*/5 * * * *"
 */

const https = require('https');
const http = require('http');

// 配置
const config = {
  // 你的应用域名和端口
  host: process.env.APP_HOST || 'localhost',
  port: process.env.APP_PORT || 3000,
  protocol: process.env.APP_PROTOCOL || 'http',
  
  // API密钥（可选，用于验证）
  apiKey: process.env.CRON_API_KEY || 'default_key',
  
  // 超时时间（毫秒）
  timeout: 30000
};

function makeRequest() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      api_key: config.apiKey
    });

    const options = {
      hostname: config.host,
      port: config.port,
      path: '/api/orders/expire',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: config.timeout
    };

    const client = config.protocol === 'https' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: result
          });
        } catch (error) {
          reject(new Error(`解析响应失败: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.write(postData);
    req.end();
  });
}

async function main() {
  const startTime = new Date();
  console.log(`[${startTime.toISOString()}] 🚀 开始执行订单过期检查任务...`);

  try {
    const response = await makeRequest();
    
    if (response.statusCode === 200 && response.data.success) {
      const { data } = response.data;
      console.log(`[${new Date().toISOString()}] ✅ 任务执行成功:`);
      console.log(`  - 过期订阅订单: ${data.expired_subscription_orders}个`);
      console.log(`  - 过期充值订单: ${data.expired_recharge_orders}个`);
      console.log(`  - 总计过期订单: ${data.total_expired}个`);
      
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();
      console.log(`  - 执行耗时: ${duration}ms`);
      
      process.exit(0);
    } else {
      console.error(`[${new Date().toISOString()}] ❌ 任务执行失败:`, response.data);
      process.exit(1);
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ❌ 任务执行异常:`, error.message);
    process.exit(1);
  }
}

// 执行任务
main();
