export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekRequest {
  model: string;
  messages: DeepSeekMessage[];
  stream?: boolean;
  max_tokens?: number;
  temperature?: number;
}

export interface DeepSeekStreamChunk {
  choices: Array<{
    delta: {
      content?: string;
      role?: string;
    };
    finish_reason?: string | null;
    index: number;
  }>;
  id: string;
  object: string;
  created: number;
  model: string;
}

export interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
    index: number;
  }>;
  id: string;
  object: string;
  created: number;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepSeekClient {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL = "https://api.deepseek.com") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async *chatCompletionStream(request: DeepSeekRequest): AsyncGenerator<DeepSeekStreamChunk> {
    // Validate model name
    if (!['deepseek-chat', 'deepseek-reasoner'].includes(request.model)) {
      throw new Error(`Invalid model: ${request.model}. Supported models: deepseek-chat, deepseek-reasoner`);
    }

    // Validate messages
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: true,
      ...(request.max_tokens && { max_tokens: Math.min(request.max_tokens, 8192) }), // DeepSeek API的实际限制
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('DeepSeek API Request:', JSON.stringify(requestPayload, null, 2));

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API Error Response:', errorText);
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Failed to get response reader');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed === '' || trimmed === 'data: [DONE]') continue;
          
          if (trimmed.startsWith('data: ')) {
            try {
              const jsonStr = trimmed.slice(6);
              const data = JSON.parse(jsonStr);
              yield data;
            } catch (error) {
              console.warn('Failed to parse SSE data:', trimmed, error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  async chatCompletion(request: DeepSeekRequest): Promise<DeepSeekResponse> {
    // Validate model name
    if (!['deepseek-chat', 'deepseek-reasoner'].includes(request.model)) {
      throw new Error(`Invalid model: ${request.model}. Supported models: deepseek-chat, deepseek-reasoner`);
    }

    // Validate messages
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: false,
      ...(request.max_tokens && { max_tokens: Math.min(request.max_tokens, 8192) }), // DeepSeek API的实际限制
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('DeepSeek API Request:', JSON.stringify(requestPayload, null, 2));

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API Error Response:', errorText);
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }
} 