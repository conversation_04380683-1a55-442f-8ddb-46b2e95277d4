import { useState, useCallback, useRef } from "react";
import {
  isMacPlatform,
  isInInputElement,
  isInEditorEnvironment,
  hasSelectedElement,
  isInEditMode
} from "@/lib/platform-utils";

/**
 * 🎯 简化版撤销/重做系统 Hook - 专注于HTML内容撤销
 *
 * 支持功能：
 * - Ctrl+Z / Cmd+Z 撤销
 * - Ctrl+Y / Cmd+Shift+Z 重做
 * - 连续撤销/重做
 * - 自动保存HTML状态
 * - 防抖优化
 */

export interface UndoRedoState {
  html: string;
  timestamp: number;
  action: string; // 描述这次修改的操作
}

interface UseUndoRedoOptions {
  maxHistorySize?: number; // 最大历史记录数量
  debounceMs?: number; // 防抖延迟
}

export const useUndoRedo = (
  initialHtml: string,
  options: UseUndoRedoOptions = {}
) => {
  const {
    maxHistorySize = 30
  } = options;

  // 🎯 彻底重写：正确的撤销系统逻辑
  const [undoState, setUndoState] = useState(() => {
    console.log(`🎯 撤销系统初始化`, {
      initialHtml: initialHtml.substring(0, 50) + '...',
      message: "创建空历史记录，等待第一次保存"
    });

    // 🎯 关键修复：初始化为空历史记录，等待第一次保存
    return {
      history: [] as UndoRedoState[],
      currentIndex: -1 // -1表示没有历史记录
    };
  });

  // 是否正在执行撤销/重做操作（防止循环）
  const isUndoRedoOperation = useRef(false);

  // 🎯 彻底修复：正确的能力计算
  const canUndo = undoState.history.length > 0 && undoState.currentIndex >= 0;
  const canRedo = undoState.currentIndex < undoState.history.length - 1;

  // 🎯 彻底修复：使用函数式更新避免闭包问题
  const saveState = useCallback((html: string, action: string) => {
    if (isUndoRedoOperation.current || !html) {
      console.log(`⏭️ 跳过保存状态: ${action}`, {
        isUndoRedoOperation: isUndoRedoOperation.current,
        hasHtml: !!html
      });
      return;
    }

    console.log(`🎯 准备保存状态: ${action}`, {
      htmlToSave: html.substring(0, 50) + '...'
    });

    const newState: UndoRedoState = {
      html,
      timestamp: Date.now(),
      action: action || '未知操作'
    };

    setUndoState(prevUndoState => {
      console.log(`🎯 保存状态时的前一状态`, {
        currentIndex: prevUndoState.currentIndex,
        historyLength: prevUndoState.history.length
      });

      // 🎯 彻底修复：检查是否与当前状态重复
      if (prevUndoState.currentIndex >= 0) {
        const currentState = prevUndoState.history[prevUndoState.currentIndex];
        if (currentState && currentState.html === html) {
          console.log(`⏭️ 跳过重复状态: ${action}`);
          return prevUndoState; // 返回原状态，不更新
        }
      }

      // 🎯 彻底修复：如果当前不在历史记录的末尾，删除后面的记录
      let newHistory: UndoRedoState[];

      if (prevUndoState.currentIndex >= 0) {
        // 有历史记录，截取到当前位置
        newHistory = prevUndoState.history.slice(0, prevUndoState.currentIndex + 1);
      } else {
        // 没有历史记录，创建新的
        newHistory = [];
      }

      // 添加新状态
      newHistory.push(newState);

      // 限制历史记录大小
      if (newHistory.length > maxHistorySize) {
        newHistory = newHistory.slice(-maxHistorySize);
      }

      const newIndex = newHistory.length - 1;

      console.log(`✅ 状态已保存: ${action}`, {
        historyLength: newHistory.length,
        currentIndex: newIndex,
        canUndo: newIndex >= 0,
        canRedo: false
      });

      return {
        history: newHistory,
        currentIndex: newIndex
      };
    });
  }, [maxHistorySize]);

  // 🎯 彻底修复：使用函数式更新的撤销操作
  const undo = useCallback((): string | null => {
    if (isUndoRedoOperation.current) {
      console.log(`❌ 撤销被阻止: 正在执行撤销/重做操作`);
      return null;
    }

    let resultHtml: string | null = null;

    setUndoState(prevState => {
      console.log(`🎯 尝试撤销`, {
        currentIndex: prevState.currentIndex,
        historyLength: prevState.history.length
      });

      // 检查是否可以撤销 - 需要至少有2个状态，且当前不在第一个状态
      const canUndoNow = prevState.history.length > 0 && prevState.currentIndex > 0;

      if (!canUndoNow) {
        console.log(`❌ 撤销被阻止`, {
          canUndoNow,
          currentIndex: prevState.currentIndex,
          historyLength: prevState.history.length,
          reason: prevState.currentIndex <= 0 ? '已在最早状态' : '无历史记录'
        });
        return prevState; // 不更新状态
      }

      try {
        isUndoRedoOperation.current = true;

        const newIndex = prevState.currentIndex - 1;

        if (newIndex >= 0) {
          const targetState = prevState.history[newIndex];

          if (targetState && targetState.html) {
            resultHtml = targetState.html;

            console.log(`✅ 撤销成功: ${targetState.action}`, {
              fromIndex: prevState.currentIndex,
              toIndex: newIndex,
              canUndo: newIndex >= 0,
              canRedo: true
            });

            setTimeout(() => {
              isUndoRedoOperation.current = false;
            }, 50);

            return {
              ...prevState,
              currentIndex: newIndex
            };
          }
        }
      } catch (error) {
        console.error('❌ 撤销操作失败:', error);
      }

      setTimeout(() => {
        isUndoRedoOperation.current = false;
      }, 50);

      return prevState; // 撤销失败，不更新状态
    });

    return resultHtml;
  }, []);

  // 🎯 彻底修复：使用函数式更新的重做操作
  const redo = useCallback((): string | null => {
    if (isUndoRedoOperation.current) {
      console.log(`❌ 重做被阻止: 正在执行撤销/重做操作`);
      return null;
    }

    let resultHtml: string | null = null;

    setUndoState(prevState => {
      console.log(`🎯 尝试重做`, {
        currentIndex: prevState.currentIndex,
        historyLength: prevState.history.length
      });

      // 检查是否可以重做
      const canRedoNow = prevState.currentIndex < prevState.history.length - 1;

      if (!canRedoNow) {
        console.log(`❌ 重做被阻止`, {
          canRedoNow,
          currentIndex: prevState.currentIndex,
          historyLength: prevState.history.length
        });
        return prevState; // 不更新状态
      }

      try {
        isUndoRedoOperation.current = true;

        const newIndex = prevState.currentIndex + 1;

        if (newIndex < prevState.history.length) {
          const targetState = prevState.history[newIndex];

          if (targetState && targetState.html) {
            resultHtml = targetState.html;

            console.log(`✅ 重做成功: ${targetState.action}`, {
              fromIndex: prevState.currentIndex,
              toIndex: newIndex,
              canUndo: true,
              canRedo: newIndex < prevState.history.length - 1
            });

            setTimeout(() => {
              isUndoRedoOperation.current = false;
            }, 50);

            return {
              ...prevState,
              currentIndex: newIndex
            };
          }
        }
      } catch (error) {
        console.error('❌ 重做操作失败:', error);
      }

      setTimeout(() => {
        isUndoRedoOperation.current = false;
      }, 50);

      return prevState; // 重做失败，不更新状态
    });

    return resultHtml;
  }, []);

  // 🎯 修复：正确的键盘快捷键支持 - 需要外部HTML更新函数
  const setupKeyboardShortcuts = useCallback((onHtmlUpdate: (html: string) => void) => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 🎯 优化：使用工具函数进行精确判断
      if (!isInEditorEnvironment() || (!isInEditMode() && !hasSelectedElement())) {
        return;
      }

      // 🎯 优化：避免在输入框中触发
      if (isInInputElement(event.target)) {
        return;
      }

      // 🎯 优化：使用现代化平台检测
      const isMac = isMacPlatform();
      const ctrlKey = isMac ? event.metaKey : event.ctrlKey;

      if (ctrlKey && event.key.toLowerCase() === 'z' && !event.shiftKey) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        const undoHtml = undo();
        if (undoHtml) {
          onHtmlUpdate(undoHtml);
        }
      } else if (
        (ctrlKey && event.key.toLowerCase() === 'y') ||
        (ctrlKey && event.shiftKey && event.key.toLowerCase() === 'z')
      ) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        const redoHtml = redo();
        if (redoHtml) {
          onHtmlUpdate(redoHtml);
        }
      }
    };

    // 🎯 优化：使用捕获阶段，确保优先处理
    document.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });

    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
    };
  }, [undo, redo]);

  // 🎯 防抖定时器已移除，无需清理

  return {
    // 核心状态
    canUndo,
    canRedo,

    // 核心操作
    saveState,
    undo,
    redo,

    // 🎯 修复：添加键盘快捷键设置函数
    setupKeyboardShortcuts,

    // 历史记录（只读）
    history: undoState.history.slice(),
    currentIndex: undoState.currentIndex,

    // 🎯 修复：添加当前HTML状态
    getCurrentHtml: () => undoState.history[undoState.currentIndex]?.html || ''
  };
};
