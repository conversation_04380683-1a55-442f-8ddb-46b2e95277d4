import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

// 生成指定长度的随机邀请码（测试用）
const generateRandomCode = (length: number): string => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 测试邀请码生成逻辑
const testGenerateInviteCode = async (): Promise<string> => {
  let code: string;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  // 从数据库获取邀请码长度设置
  let codeLength = 8; // 默认8位
  try {
    const lengthSetting = await executeQuery(
      'SELECT setting_value FROM system_settings WHERE setting_key = ? AND is_active = 1',
      ['invitation_code_length']
    ) as { setting_value: string }[];
    
    if (lengthSetting.length > 0) {
      const configuredLength = parseInt(lengthSetting[0].setting_value);
      if (configuredLength >= 4 && configuredLength <= 20) { // 合理范围限制
        codeLength = configuredLength;
      }
    }
    console.log(`🔍 [测试] 使用邀请码长度: ${codeLength}位`);
  } catch (error) {
    console.error('获取邀请码长度设置失败，使用默认长度8位:', error);
  }

  while (!isUnique && attempts < maxAttempts) {
    // 根据配置的长度生成邀请码
    code = generateRandomCode(codeLength);

    try {
      const existing = await executeQuery(
        'SELECT 1 FROM users WHERE invite_code = ? LIMIT 1',
        [code]
      ) as any[];
      isUnique = existing.length === 0;
    } catch (error) {
      console.error('检查邀请码唯一性失败:', error);
      // 如果查询失败，使用时间戳确保唯一性
      code = `U${Date.now().toString(36).toUpperCase()}`.substring(0, codeLength);
      isUnique = true;
    }

    attempts++;
  }

  if (!isUnique) {
    // 最后的备用方案：使用时间戳
    code = `U${Date.now().toString(36).toUpperCase()}`.substring(0, codeLength);
  }

  console.log(`✅ [测试] 生成邀请码: ${code} (长度: ${code.length})`);
  return code!;
};

export async function GET() {
  try {
    console.log('🧪 开始测试邀请码长度配置...');

    // 1. 检查当前数据库中的邀请码长度设置
    const lengthSetting = await executeQuery(
      'SELECT setting_value FROM system_settings WHERE setting_key = ? AND is_active = 1',
      ['invitation_code_length']
    ) as { setting_value: string }[];

    const configuredLength = lengthSetting.length > 0 ? parseInt(lengthSetting[0].setting_value) : 6;
    console.log('📋 数据库配置的邀请码长度:', configuredLength);

    // 2. 检查现有用户的邀请码长度分布
    const existingCodes = await executeQuery(
      'SELECT invite_code, LENGTH(invite_code) as code_length FROM users WHERE invite_code IS NOT NULL ORDER BY id DESC LIMIT 10'
    ) as { invite_code: string; code_length: number }[];

    console.log('📊 现有邀请码长度分布:', existingCodes);

    // 3. 测试生成新的邀请码
    const testCodes = [];
    for (let i = 0; i < 5; i++) {
      const newCode = await testGenerateInviteCode();
      testCodes.push({
        code: newCode,
        length: newCode.length
      });
    }

    // 4. 计算不同长度的容量
    const capacityAnalysis = [
      { length: 4, capacity: Math.pow(36, 4), formatted: '1.68M' },
      { length: 6, capacity: Math.pow(36, 6), formatted: '2.18B' },
      { length: 8, capacity: Math.pow(36, 8), formatted: '2.82T' },
      { length: 10, capacity: Math.pow(36, 10), formatted: '3.66P' },
      { length: 12, capacity: Math.pow(36, 12), formatted: '4.74E' }
    ];

    return NextResponse.json({
      success: true,
      data: {
        configuredLength,
        existingCodes,
        testGeneratedCodes: testCodes,
        capacityAnalysis,
        recommendation: {
          current: configuredLength,
          recommended: configuredLength >= 8 ? configuredLength : 8,
          reason: configuredLength < 8 ? '建议使用8位或更长的邀请码以避免重复' : '当前长度合适'
        }
      }
    });

  } catch (error) {
    console.error('❌ 测试邀请码长度失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const { newLength } = await request.json();

    if (!newLength || newLength < 4 || newLength > 20) {
      return NextResponse.json({
        success: false,
        error: '邀请码长度必须在4-20位之间'
      }, { status: 400 });
    }

    // 更新邀请码长度设置
    await executeQuery(
      'UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
      [newLength.toString(), 'invitation_code_length']
    );

    console.log(`✅ 邀请码长度已更新为: ${newLength}位`);

    return NextResponse.json({
      success: true,
      message: `邀请码长度已更新为 ${newLength} 位`,
      newLength
    });

  } catch (error) {
    console.error('❌ 更新邀请码长度失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
