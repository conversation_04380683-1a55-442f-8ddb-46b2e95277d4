"use client";
import { useUpdateEffect } from "react-use";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import classNames from "classnames";
import { toast } from "sonner";
import { createUndoKeyEvent, createRedoKeyEvent, getUndoShortcut, getRedoShortcut } from "@/lib/platform-utils";
import { 
  ChevronDown, 
  Clock,
  Download,
  Copy,
  FileText
} from "lucide-react";
import { usePathname } from "next/navigation";

import ExportMenu from "@/components/editor/export-menu";

import { cn } from "@/lib/utils";

import { useIframeScrollOptimization } from "@/loomrunhooks/useScrollOptimization";
import { ElementToolbar } from "../element-toolbar";

interface ProjectVersion {
  id: string;
  version_number: number;
  title: string;
  prompt: string;
  html_content: string;
  parent_version: number | null;
  is_active: boolean;
  created_at: string;
  metadata: {
    lines_changed: number;
    generation_time: number;
    model_used: string;
    is_modification: boolean;
  };
}

interface LineData {
  content: string;
  visualLines: number;
  indentLevel: number;
  isRealLine: boolean;
  realLineNumber: number;
  isEmptyLine: boolean;
}

// VS Code风格的代码编辑器组件 - 简化版本，只用于展示
const CodeEditor = ({ html, isStreaming }: { html: string; isStreaming?: boolean }) => {
  const [copied, setCopied] = useState(false);
  const [containerWidth, setContainerWidth] = useState(0);
  const codeContainerRef = useRef<HTMLDivElement>(null);
  const lineNumbersRef = useRef<HTMLDivElement>(null);
  const codeContentRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(html);
      setCopied(true);
      toast.success("代码已复制！");
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error("复制失败");
    }
  };

  const handleDownload = () => {
    try {
      const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'index.html';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("下载成功！");
    } catch {
      toast.error("下载失败");
    }
  };

  // 同步滚动
  const handleScroll = () => {
    if (codeContainerRef.current && lineNumbersRef.current) {
      lineNumbersRef.current.scrollTop = codeContainerRef.current.scrollTop;
    }
  };

  // 检测缩进级别
  const getIndentLevel = (line: string): number => {
    const match = line.match(/^(\s*)/);
    const indent = match ? match[1] : '';
    // 计算缩进级别（2个空格或1个tab = 1级）
    return Math.floor(indent.replace(/\t/g, '  ').length / 2);
  };

  // 计算行的视觉行数（考虑换行）- 确保换行内容在缩进指示线右侧
  const calculateVisualLines = (line: string, availableWidth: number): number => {
    if (!line.trim() || availableWidth <= 0) return 1;
    
    // 字符平均宽度 (Cascadia Code 14px)
    const charWidth = 8.4;
    const indentLevel = getIndentLevel(line);
    const indentWidth = indentLevel * 16; // 缩进占用的宽度
    const paddingWidth = 32; // 左右padding
    
    // 关键：计算缩进指示线右侧可用的宽度
    const effectiveWidth = availableWidth - indentWidth - paddingWidth;
    
    if (effectiveWidth <= charWidth * 10) return 1; // 最小保证10个字符宽度
    
    const charsPerLine = Math.floor(effectiveWidth / charWidth);
    
    // 只计算实际内容长度（去掉前导空格）
    const actualContent = line.replace(/^\s+/, '');
    if (!actualContent) return 1;
    
    return Math.max(1, Math.ceil(actualContent.length / charsPerLine));
  };

  // 渲染缩进指示线 - 根据视觉行数拉长，空行保持连续
  const renderIndentGuides = (lineData: LineData, lineIndex: number) => {
    const indentLevel = lineData.indentLevel;
    const guides = [];
    const totalHeight = lineData.visualLines * 21; // 根据视觉行数计算总高度
    
    for (let i = 0; i < indentLevel; i++) {
      guides.push(
        <div
          key={`indent-${lineIndex}-${i}`}
          className={`absolute w-px transition-opacity duration-150 ${
            lineData.isEmptyLine 
              ? 'bg-gray-300 dark:bg-gray-600 opacity-15' // 空行时稍微淡一些但保持连续
              : 'bg-gray-300 dark:bg-gray-600 opacity-25 hover:opacity-50'
          }`}
          style={{
            left: `${16 + (i * 16)}px`,
            top: 0,
            height: `${totalHeight}px`, // 关键：根据视觉行数拉长
            pointerEvents: 'none',
            zIndex: 1
          }}
        />
      );
    }
    
    return guides;
  };

  // 监听容器宽度变化
  useEffect(() => {
    if (codeContainerRef.current) {
      const updateWidth = () => {
        if (codeContainerRef.current) {
          setContainerWidth(codeContainerRef.current.clientWidth);
        }
      };

      updateWidth();
      
      resizeObserverRef.current = new ResizeObserver(updateWidth);
      resizeObserverRef.current.observe(codeContainerRef.current);
      
      return () => {
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
        }
      };
    }
  }, []);

  // 预计算所有行的视觉信息
  const linesData = useMemo(() => {
    const lines = html.split('\n');
    const availableWidth = containerWidth - 64; // 减去padding
    
    return lines.map((line, index) => {
      const isEmptyLine = !line.trim();
      
      // 对于空行，继承上一行的缩进级别来保持缩进指示线连续
      let inheritedIndentLevel = 0;
      if (isEmptyLine && index > 0) {
        // 向上查找最近的非空行的缩进级别
        for (let i = index - 1; i >= 0; i--) {
          const prevLine = lines[i];
          if (prevLine.trim()) {
            inheritedIndentLevel = getIndentLevel(prevLine);
            break;
          }
        }
        // 如果向上没找到，向下查找
        if (inheritedIndentLevel === 0) {
          for (let i = index + 1; i < lines.length; i++) {
            const nextLine = lines[i];
            if (nextLine.trim()) {
              inheritedIndentLevel = getIndentLevel(nextLine);
              break;
            }
          }
        }
      }
      
      return {
        content: line,
        visualLines: isEmptyLine ? 1 : calculateVisualLines(line, availableWidth), // 空行固定1行
        indentLevel: isEmptyLine ? inheritedIndentLevel : getIndentLevel(line),
        isRealLine: true, // 所有行都是真实行，包括空行
        realLineNumber: index + 1,
        isEmptyLine
      };
    });
  }, [html, containerWidth]);





  return (
    <div className="w-full h-full flex flex-col bg-white dark:bg-[#1e1e1e] text-gray-800 dark:text-[#d4d4d4] code-editor-container">
      {/* 标签栏 - 使用主题颜色 */}
      <div className="flex items-center bg-gray-100 dark:bg-[#2d2d30] border-b border-gray-300 dark:border-[#3e3e42] py-1">
        <div className="flex items-center px-3 bg-white dark:bg-[#1e1e1e] border-r border-gray-300 dark:border-[#3e3e42] text-sm font-medium">
          <FileText className="w-3 h-3 text-[#4ec9b0] mr-1.5" />
          <span className="text-gray-700 dark:text-[#cccccc]">index.html</span>
          <div className={`w-1.5 h-1.5 rounded-full ml-1.5 ${isStreaming ? 'bg-orange-400 animate-pulse' : 'bg-[#4ec9b0]'}`}></div>
          {isStreaming && (
            <span className="text-orange-400 text-xs ml-2 animate-pulse">实时生成中...</span>
          )}
        </div>
        <div className="flex-1"></div>
        <div className="flex items-center gap-1 px-3">
          <button
            onClick={handleCopy}
            className={cn(
              "px-2 text-sm font-medium rounded transition-colors flex items-center gap-1",
              copied 
                ? "bg-blue-600 dark:bg-[#0e639c] text-white" 
                : "bg-blue-600 hover:bg-blue-700 dark:bg-[#0e639c] dark:hover:bg-[#1177bb] text-white"
            )}
          >
            <Copy className="w-3 h-3" />
            {copied ? "已复制" : "复制"}
          </button>
          
          <button
            onClick={handleDownload}
            className="px-2 text-sm font-medium bg-gray-600 hover:bg-gray-700 dark:bg-[#3c3c3c] dark:hover:bg-[#464647] text-white dark:text-[#cccccc] rounded transition-colors flex items-center gap-1"
          >
            <Download className="w-3 h-3" />
            下载
          </button>
        </div>
      </div>

      {/* 代码编辑区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 行号区域 - 智能对齐优化 */}
        <div 
          ref={lineNumbersRef}
          className="bg-gray-50 dark:bg-[#1e1e1e] border-r border-gray-300 dark:border-[#3e3e42] text-gray-500 dark:text-[#858585] select-none overflow-hidden relative"
          style={{ 
            fontFamily: "'Cascadia Code', 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, Consolas, monospace",
            fontSize: '14px',
            lineHeight: '21px',
            minWidth: '60px',
            paddingTop: '16px',
            paddingBottom: '16px',
            paddingLeft: '12px',
            paddingRight: '12px'
          }}
        >
          {linesData.map((lineData: LineData, index: number) => (
            <div 
              key={index}
              className="text-right select-none relative"
              style={{ 
                height: `${lineData.visualLines * 21}px`, // 根据视觉行数调整高度
                lineHeight: '21px',
                fontSize: '14px',
                color: 'inherit'
              }}
            >
              {/* 只在第一个视觉行显示行号 */}
              <span className="inline-block w-full text-right" style={{ height: '21px' }}>
                {lineData.realLineNumber}
              </span>
              {/* 其余视觉行留空 */}
              {Array.from({ length: lineData.visualLines - 1 }).map((_, emptyIndex) => (
                <div key={`empty-${emptyIndex}`} style={{ height: '21px' }} />
              ))}
            </div>
          ))}
        </div>
        
        {/* 代码内容区域 - 智能换行优化 */}
        <div 
          ref={codeContainerRef}
          className="flex-1 overflow-auto bg-white dark:bg-[#1e1e1e] vscode-scrollbar vscode-code relative"
          onScroll={handleScroll}
          style={{ 
            fontFamily: "'Cascadia Code', 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, Consolas, monospace",
            fontSize: '14px',
            lineHeight: '21px'
          }}
        >
          <div className="py-4 px-4 relative">
            {linesData.map((lineData: LineData, index: number) => (
              <div
                key={index}
                ref={index === 0 ? codeContentRef : undefined}
                className="code-line relative"
                data-empty={lineData.isEmptyLine}
                style={{ 
                  fontFamily: "'Cascadia Code', 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, Consolas, monospace",
                  fontSize: '14px',
                  lineHeight: '21px',
                  minHeight: `${lineData.visualLines * 21}px`, // 根据视觉行数设置高度
                  margin: 0,
                  padding: 0,
                  wordBreak: 'break-all',
                  whiteSpace: 'pre-wrap',
                  overflowWrap: 'anywhere'
                }}
              >
                {/* 缩进指示线 - 根据视觉行数拉长，空行保持连续 */}
                {renderIndentGuides(lineData, index)}
                
                {/* 代码内容 - 确保换行内容在缩进指示线右侧 */}
                <div 
                  className="text-gray-800 dark:text-[#d4d4d4] relative z-10"
                  style={{
                    minHeight: `${lineData.visualLines * 21}px`,
                    lineHeight: '21px',
                    display: 'flex',
                    alignItems: 'flex-start',
                  }}
                >
                  {/* 缩进空间占位 - 确保内容在缩进指示线右侧 */}
                  <div 
                    style={{ 
                      width: `${lineData.indentLevel * 16}px`,
                      flexShrink: 0,
                      height: '21px'
                    }}
                  />
                  
                  {/* 实际代码内容区域 */}
                  <div
                    style={{
                      flex: 1,
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      minHeight: `${lineData.visualLines * 21}px`,
                      lineHeight: '21px',
                    }}
                  >
                    {/* 处理空行和非空行 */}
                    {lineData.isEmptyLine 
                      ? ' ' // 空行显示一个空格占位，保持行高
                      : (lineData.content.replace(/^\s+/, '') || ' ') // 非空行去掉前导空格
                    }
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 状态栏 - 主题优化 */}
      <div className="px-3 py-1 bg-gray-100 dark:bg-[#2d2d30] border-t border-gray-300 dark:border-[#3e3e42] flex items-center justify-between text-xs text-gray-600 dark:text-[#cccccc]">
        <div className="flex items-center gap-3">
          <span className="flex items-center gap-1">
            <div className="w-1.5 h-1.5 bg-[#4ec9b0] rounded-full"></div>
            HTML
          </span>
          <span>UTF-8</span>
          <span>LF</span>
          <span>{html.split('\n').length} 行</span>
          <span>{(html.length / 1024).toFixed(1)}KB</span>
        </div>
        <div className="flex items-center gap-2">
          <span>LoomRun</span>
        </div>
      </div>
    </div>
  );
};

export const Preview = ({
  html,
  streamingHtml,
  isResizing,
  isAiWorking,
  ref,
  device,
  currentTab,
  iframeRef,
  isEditableModeEnabled,
  selectedElement, // 🎯 新增：外部传入的选中元素
  undoRedoSystem, // 🎯 修复：撤销系统
  onClickElement,
  projectId: propProjectId,
  onVersionSelect,
  currentVersionNumber,
  onVersionNumberChange,
  onVersionContentUpdate,
  onDeviceChange,
  onRefresh,
  onModeToggle,
  onHtmlChange, // 🎯 修复：HTML变更回调
}: {
  html: string;
  streamingHtml?: string;
  isResizing: boolean;
  isAiWorking: boolean;
  ref: React.RefObject<HTMLDivElement | null>;
  iframeRef?: React.RefObject<HTMLIFrameElement | null>;
  device: string;
  currentTab: string;
  isEditableModeEnabled?: boolean;
  selectedElement?: HTMLElement | null; // 🎯 新增：外部传入的选中元素
  // 🎯 修复：添加撤销系统
  undoRedoSystem?: {
    canUndo: boolean;
    canRedo: boolean;
    undo: () => string | null;
    redo: () => string | null;
  };
  onClickElement?: (element: HTMLElement) => void;
  projectId?: string;
  onVersionSelect?: (version: ProjectVersion) => void;
  currentVersionNumber?: number;
  onVersionNumberChange?: (versionNumber: number) => void; // 🎯 新增：版本号变化回调
  onVersionContentUpdate?: (versionNumber: number, htmlContent: string) => void; // 🎯 新增：版本内容更新回调
  onDeviceChange?: (device: string) => void;
  onRefresh?: () => void;
  onModeToggle?: () => void;
  onHtmlChange?: (html: string) => void; // 🎯 修复：HTML变更回调
}) => {
  const pathname = usePathname();
  
  // 🎯 智能获取projectId：优先使用传入的props，否则从URL中解析
  const projectId = propProjectId || (() => {
    const match = pathname.match(/\/projects\/(\d+)/);
    return match ? match[1] : undefined;
  })();

  const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);
  const [versions, setVersions] = useState<ProjectVersion[]>([]);
  const [currentVersion, setCurrentVersion] = useState<number>(currentVersionNumber || 1);
  const [isVersionMenuOpen, setIsVersionMenuOpen] = useState(false);
  const [isEditingText, setIsEditingText] = useState(false);
  const [loadingVersions, setLoadingVersions] = useState(false);
  const [showCode, setShowCode] = useState(false);

  // 简化状态管理
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastStreamingContentRef = useRef<string>('');
  const streamingUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 专业级元素选择器 - 精确无残留
  const handleMouseOver = useCallback((event: MouseEvent) => {
    if (!iframeRef?.current || isAiWorking) return;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return;

    const targetElement = event.target as HTMLElement;

    // 🚫 忽略无效元素
    if (targetElement === iframeDocument.body || targetElement === iframeDocument.documentElement) {
      return;
    }

    // 🧹 立即清理所有残留状态 - 确保无残留
    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }

    // 🚨 关键修复：避免导航栏抖动 - 智能悬停状态管理
    const currentHovered = iframeDocument.querySelector('.hovered-element');

    // 如果目标元素已经是悬停状态，直接返回，避免重复操作
    if (currentHovered === targetElement) {
      return;
    }

    // 🎯 检查是否是导航栏相关元素，使用更温和的处理
    const isNavElement = targetElement.closest('nav, header, .navbar, .nav, [role="navigation"]');

    if (isNavElement) {
      // 🎯 对导航栏元素使用防抖处理，减少频繁更新
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }

      throttleTimeoutRef.current = setTimeout(() => {
        // 温和地清理悬停状态，避免导航栏动画冲突
        if (currentHovered && currentHovered !== targetElement) {
          currentHovered.classList.remove('hovered-element');
        }
        targetElement.classList.add('hovered-element');
        setHoveredElement(targetElement);
      }, 50); // 导航栏元素使用更长的防抖时间
    } else {
      // 🚀 非导航栏元素立即应用新状态
      if (currentHovered) {
        currentHovered.classList.remove('hovered-element');
      }

      requestAnimationFrame(() => {
        if (targetElement && !targetElement.classList.contains('hovered-element')) {
          targetElement.classList.add('hovered-element');
          setHoveredElement(targetElement);
        }
      });
    }
  }, [iframeRef, isAiWorking]);

  const handleMouseOut = useCallback((event: MouseEvent) => {
    if (!iframeRef?.current) return;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return;

    // 🧹 立即清理，无延迟
    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }

    // 🎯 检查鼠标是否真的离开了iframe区域
    const relatedTarget = event.relatedTarget as HTMLElement;
    if (relatedTarget && iframeDocument.contains(relatedTarget)) {
      return; // 还在iframe内部，不清理
    }

    // 🚀 立即清理悬停状态，但保留选中状态
    requestAnimationFrame(() => {
      iframeDocument.querySelectorAll('.hovered-element').forEach(el => {
        // 🎯 关键修复：只清理悬停状态，不清理选中状态
        if (!el.classList.contains('selected-element-highlight')) {
          el.classList.remove('hovered-element');
        }
      });
      setHoveredElement(null);
    });
  }, [iframeRef]);

  const handleClick = useCallback((event: MouseEvent) => {
    if (!iframeRef?.current) return;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return;

    // 🚨 关键修复：编辑模式下完全阻止所有原始事件，包括按钮功能
    if (isEditableModeEnabled) {
      const targetElement = event.target as HTMLElement;
      const isButton = targetElement.closest('button');
      const isLink = targetElement.closest('a');
      const isInput = targetElement.closest('input[type="submit"], input[type="button"]');

      // 对功能性元素使用最强的阻止机制
      if (isButton || isLink || isInput) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        console.log('🚫 编辑模式：功能性元素完全阻止', {
          target: targetElement.tagName,
          type: isButton ? 'button' : isLink ? 'link' : 'input',
          className: targetElement.className
        });
      } else {
        // 对普通元素只阻止默认行为
        event.preventDefault();

        console.log('🎯 编辑模式：普通元素默认行为已阻止', {
          target: targetElement.tagName,
          className: targetElement.className
        });
      }

      // 不阻止事件传播，让选择逻辑继续执行
    }

    const targetElement = event.target as HTMLElement;
    if (targetElement !== iframeDocument.body) {
      // 🎯 关键修复：在编辑模式下立即应用选中高亮，并确保持久化
      if (isEditableModeEnabled) {
        // 清理之前选中元素的高亮
        iframeDocument.querySelectorAll('.selected-element-highlight').forEach(el => {
          el.classList.remove('selected-element-highlight');
        });

        // 为新选中的元素添加高亮
        targetElement.classList.add('selected-element-highlight');

        console.log('🎯 元素选中高亮已应用', {
          element: targetElement.tagName,
          className: targetElement.className
        });

        // 🎯 关键修复：使用定时器确保高亮在编辑模式禁用后仍然保持
        setTimeout(() => {
          if (targetElement && iframeDocument.contains(targetElement)) {
            targetElement.classList.add('selected-element-highlight');
            console.log('🎯 选中元素高亮已确保持久化');
          }
        }, 100);
      }

      requestAnimationFrame(() => {
        onClickElement?.(targetElement);
      });
    }
  }, [iframeRef, onClickElement, isEditableModeEnabled]);

  // 🎯 强化版：确保选中元素样式始终存在且正确
  const ensureSelectedElementStyles = useCallback(() => {
    if (!iframeRef?.current) return false;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return false;

    try {
      // 🎯 强制移除旧样式，确保重新创建
      const existingStyles = iframeDocument.querySelectorAll('#selected-element-style, #edit-mode-style');
      existingStyles.forEach(style => style.remove());

      // 🎯 创建完整的样式表，包含悬停和选中样式
      const persistentStyle = iframeDocument.createElement('style');
      persistentStyle.id = 'selected-element-style';
      persistentStyle.textContent = `
        /* 🎯 悬停元素样式 */
        .hovered-element {
          outline: 2px solid rgba(59, 130, 246, 0.8) !important;
          outline-offset: 1px !important;
          background-color: rgba(59, 130, 246, 0.06) !important;
          box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3) !important;
          transition: none !important;
          position: relative !important;
          z-index: 1000 !important;
        }

        /* 🎯 选中元素样式 - 只使用边框，不覆盖背景色 */
        .selected-element-highlight {
          outline: 3px solid rgba(34, 197, 94, 0.9) !important;
          outline-offset: 2px !important;
          box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.4) !important;
          transition: none !important;
          position: relative !important;
          z-index: 1001 !important;
          /* 🚨 关键修复：移除背景色覆盖，让实际背景色可见 */
        }

        /* 🎯 确保选中状态覆盖悬停状态，但不覆盖背景色 */
        .selected-element-highlight.hovered-element {
          outline: 3px solid rgba(34, 197, 94, 0.9) !important;
          outline-offset: 2px !important;
          box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.4) !important;
          /* 🚨 关键修复：移除背景色覆盖 */
        }
      `;

      iframeDocument.head.appendChild(persistentStyle);
      console.log('✅ 选中元素样式已强制重新创建');
      return true;
    } catch (error) {
      console.error('❌ 创建选中元素样式失败:', error);
      return false;
    }
  }, [iframeRef]);

  // 🎯 基于HTML内容的版本检测 - 确保菜单显示与预览内容完全一致
  const detectVersionFromContent = useCallback((htmlContent: string, versionsList: ProjectVersion[]) => {
    if (!versionsList.length || !htmlContent.trim()) {
      return null;
    }
    
    // 查找与当前HTML内容完全匹配的版本
    const matchingVersion = versionsList.find(version => {
      // 去除空白字符进行比较，避免格式差异影响匹配
      const versionContent = version.html_content.trim().replace(/\s+/g, ' ');
      const currentContent = htmlContent.trim().replace(/\s+/g, ' ');
      return versionContent === currentContent;
    });
    
    if (matchingVersion) {
      console.log('🎯 Preview: 基于内容检测到匹配版本', {
        detectedVersion: matchingVersion.version_number,
        contentLength: htmlContent.length
      });
      return matchingVersion.version_number;
    }
    
    console.log('🔍 Preview: 未找到匹配的版本内容', {
      currentContentLength: htmlContent.length,
      availableVersions: versionsList.map(v => ({
        version: v.version_number,
        contentLength: v.html_content.length
      }))
    });
    return null;
  }, []); // 🔧 移除versions依赖，通过参数传入

  // 🎯 监听HTML内容变化，自动检测并更新版本号 - 优化性能
  useEffect(() => {
    const currentHtml = streamingHtml || html;

    if (!currentHtml.trim() || !versions.length) {
      return;
    }

    // 🔧 关键修复：AI生成时跳过版本检测，避免频繁刷新
    if (isAiWorking || streamingHtml) {
      console.log('🔧 Preview: AI正在生成，跳过版本检测避免刷新');
      return;
    }

    // 🔧 性能优化：大幅增加防抖时间，避免AI生成时频繁检测
    const timeoutId = setTimeout(() => {
      const detectedVersion = detectVersionFromContent(currentHtml, versions);

      if (detectedVersion && detectedVersion !== currentVersion) {
        console.log('🔄 Preview: HTML内容变化，自动更新版本号', {
          oldVersion: currentVersion,
          detectedVersion,
          contentLength: currentHtml.length
        });
        setCurrentVersion(detectedVersion);

        // 🎯 关键修复：通知外部版本号变化
        if (onVersionNumberChange) {
          onVersionNumberChange(detectedVersion);
          console.log('✅ Preview: 已通知外部版本号变化', { newVersion: detectedVersion });
        }
      }
    }, 3000); // 🔧 增加到3000ms防抖，避免AI生成时频繁刷新

    return () => clearTimeout(timeoutId);
  }, [html, streamingHtml, versions, detectVersionFromContent, isAiWorking]); // 🔧 添加 isAiWorking 依赖

  // 获取版本列表 - 移除不必要的依赖，防止无限循环
  const fetchVersions = useCallback(async () => {
    if (!projectId) {
      console.log('🔍 Preview: fetchVersions - 没有projectId，跳过');
      return;
    }
    
    console.log('🔄 Preview: 开始获取版本列表', { projectId });
    setLoadingVersions(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/versions`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Preview: 版本列表获取成功', {
          projectId,
          versionsCount: data.versions?.length || 0,
          versions: data.versions?.map((v: ProjectVersion) => ({
            id: v.id,
            version_number: v.version_number,
            is_active: v.is_active
          }))
        });
        setVersions(data.versions || []);
        
        // 🎯 获取版本列表后，立即检测当前HTML对应的版本
        const currentHtml = streamingHtml || html;
        if (currentHtml.trim() && data.versions?.length > 0) {
          const detectedVersion = data.versions.find((v: ProjectVersion) => {
            const versionContent = v.html_content.trim().replace(/\s+/g, ' ');
            const currentContent = currentHtml.trim().replace(/\s+/g, ' ');
            return versionContent === currentContent;
          });
          
          if (detectedVersion) {
            console.log('🎯 Preview: 版本列表加载后检测到匹配版本', {
              detectedVersion: detectedVersion.version_number,
              currentVersion
            });
            if (detectedVersion.version_number !== currentVersion) {
              setCurrentVersion(detectedVersion.version_number);

              // 🎯 关键修复：通知外部版本号变化
              if (onVersionNumberChange) {
                onVersionNumberChange(detectedVersion.version_number);
                console.log('✅ Preview: 已通知外部版本号变化（版本列表加载后检测）', { newVersion: detectedVersion.version_number });
              }
            }
          } else if (!currentVersionNumber && currentVersion === 1) {
            // 只有在没有明确版本号且当前为默认值时，才使用活跃版本
            const activeVersion = data.versions.find((v: ProjectVersion) => v.is_active);
            if (activeVersion) {
              console.log('🔧 Preview: 未检测到匹配版本，使用活跃版本', {
                activeVersion: activeVersion.version_number
              });
              setCurrentVersion(activeVersion.version_number);

              // 🎯 关键修复：通知外部版本号变化
              if (onVersionNumberChange) {
                onVersionNumberChange(activeVersion.version_number);
                console.log('✅ Preview: 已通知外部版本号变化（使用活跃版本）', { newVersion: activeVersion.version_number });
              }
            }
          }
        }
      } else {
        console.error('❌ Preview: 版本列表获取失败', {
          status: response.status,
          statusText: response.statusText
        });
      }
    } catch (error) {
      console.error('❌ Preview: 版本列表获取异常', error);
    } finally {
      setLoadingVersions(false);
    }
  }, [projectId]); // 🔧 关键修复：只依赖projectId，移除其他依赖防止无限循环

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return "刚刚";
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`;
    return date.toLocaleDateString("zh-CN");
  };

  // 🎯 优化版本选择处理 - 确保版本菜单立即反映选择的版本
  const handleVersionSelect = (version: ProjectVersion) => {
    console.log('🔄 Preview: 用户选择版本', {
      selectedVersion: version.version_number,
      previousVersion: currentVersion
    });

    // 🎯 关键修复：立即更新版本号显示，确保菜单同步
    setCurrentVersion(version.version_number);
    setIsVersionMenuOpen(false);

    // 🎯 关键修复：通知外部版本号变化
    if (onVersionNumberChange) {
      onVersionNumberChange(version.version_number);
      console.log('✅ Preview: 已通知外部版本号变化（用户选择）', { newVersion: version.version_number });
    }

    // 调用外部回调，更新预览内容
    onVersionSelect?.(version);

    console.log('✅ Preview: 版本切换完成', {
      newVersion: version.version_number,
      menuClosed: true,
      contentLength: version.html_content.length
    });
  };

  // 🔧 修复：只在projectId真正变化时获取版本列表
  useEffect(() => {
    if (projectId) {
      console.log('🔄 Preview: projectId变化，获取版本列表', { projectId });
      fetchVersions();
    }
  }, [projectId]); // 🔧 关键修复：移除fetchVersions依赖，防止无限循环

  // 🎯 精准同步外部传入的版本号 - 确保菜单显示与预览内容一致
  useEffect(() => {
    if (currentVersionNumber !== undefined && currentVersionNumber !== currentVersion) {
      console.log('🔄 Preview: 外部版本号变化，同步到菜单显示', {
        externalVersion: currentVersionNumber,
        currentMenuVersion: currentVersion,
        needsUpdate: true
      });
      
      setCurrentVersion(currentVersionNumber);
      console.log('✅ Preview: 菜单版本号已同步', {
        oldVersion: currentVersion,
        newVersion: currentVersionNumber
      });
    }
  }, [currentVersionNumber]); // 🔧 关键修复：移除currentVersion依赖，防止无限循环

  // 🎯 优化新版本创建时的处理 - 确保菜单显示最新版本，添加防抖
  useEffect(() => {
    if (!projectId) {
      console.log('🔍 Preview: 没有projectId，跳过版本更新监听');
      return;
    }

    console.log('🎯 Preview: 设置版本更新监听器', { projectId });

    // 🔧 添加防抖函数，避免频繁刷新
    const debouncedRefresh = (() => {
      let timeoutId: NodeJS.Timeout | null = null;
      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          fetchVersions();
        }, 300); // 300ms防抖
      };
    })();

    // 🔧 方法1：BroadcastChannel监听（现代浏览器）
    let versionUpdateChannel: BroadcastChannel | null = null;
    let storageListener: ((e: StorageEvent) => void) | null = null;
    let customEventListener: ((e: CustomEvent) => void) | null = null;

    try {
      if ('BroadcastChannel' in window) {
        versionUpdateChannel = new BroadcastChannel('version-updates');
        versionUpdateChannel.addEventListener('message', (event) => {
          console.log('📡 Preview: 收到BroadcastChannel消息', event.data);
          if ((event.data.type === 'version-created' || event.data.type === 'version-updated') && event.data.projectId === projectId) {
            console.log('🔄 Preview: BroadcastChannel检测到版本更新', {
              projectId,
              versionNumber: event.data.versionNumber,
              eventType: event.data.type
            });

            // 🎯 关键修复：如果是版本内容更新，立即更新版本列表缓存
            if (event.data.type === 'version-updated' && event.data.versionNumber && event.data.htmlContent) {
              setVersions(prevVersions => {
                const updated = prevVersions.map(version => {
                  if (version.version_number === event.data.versionNumber) {
                    console.log('🔄 Preview: 更新版本缓存内容', {
                      versionNumber: version.version_number,
                      oldLength: version.html_content.length,
                      newLength: event.data.htmlContent.length
                    });
                    return {
                      ...version,
                      html_content: event.data.htmlContent
                    };
                  }
                  return version;
                });
                return updated;
              });

              // 🎯 如果更新的是当前预览的版本，通知外部更新HTML内容
              if (event.data.versionNumber === currentVersion && onVersionContentUpdate) {
                console.log('🔄 Preview: 当前预览版本内容已更新，通知外部更新HTML');
                const currentHtml = streamingHtml || html;
                if (currentHtml !== event.data.htmlContent) {
                  onVersionContentUpdate(event.data.versionNumber, event.data.htmlContent);
                  console.log('✅ Preview: 已通知外部更新HTML内容', {
                    versionNumber: event.data.versionNumber,
                    htmlLength: event.data.htmlContent.length
                  });
                }
              }
            }

            // 🎯 版本创建时，立即更新菜单显示为最新版本
            if (event.data.type === 'version-created' && event.data.versionNumber && event.data.versionNumber !== currentVersion) {
              console.log('🎯 Preview: 立即更新菜单显示为最新版本', {
                oldVersion: currentVersion,
                newVersion: event.data.versionNumber
              });
              setCurrentVersion(event.data.versionNumber);

              // 🎯 关键修复：通知外部版本号变化
              if (onVersionNumberChange) {
                onVersionNumberChange(event.data.versionNumber);
                console.log('✅ Preview: 已通知外部版本号变化（新版本创建）', { newVersion: event.data.versionNumber });
              }
            }

            debouncedRefresh(); // 🔧 使用防抖刷新
          }
        });
        console.log('✅ Preview: BroadcastChannel监听器已设置');
      } else {
        console.log('⚠️ Preview: BroadcastChannel不可用');
      }
    } catch (error) {
      console.log('⚠️ Preview: BroadcastChannel设置失败', error);
    }

    // 🔧 方法2：localStorage事件监听（兜底方案）
    storageListener = (e: StorageEvent) => {
      console.log('📡 Preview: 收到localStorage事件', { key: e.key, newValue: e.newValue });
      if (e.key === 'version-created-event') {
        try {
          const eventData = JSON.parse(e.newValue || '{}');
          console.log('📡 Preview: 解析localStorage事件数据', eventData);
          if (eventData.projectId === projectId) {
            console.log('🔄 Preview: localStorage检测到版本更新', {
              projectId,
              versionNumber: eventData.versionNumber,
              eventType: eventData.type
            });

            // 🎯 关键修复：如果是版本内容更新，立即更新版本列表缓存
            if (eventData.type === 'version-updated' && eventData.versionNumber && eventData.htmlContent) {
              setVersions(prevVersions => {
                const updated = prevVersions.map(version => {
                  if (version.version_number === eventData.versionNumber) {
                    console.log('🔄 Preview: 更新版本缓存内容（localStorage）', {
                      versionNumber: version.version_number,
                      oldLength: version.html_content.length,
                      newLength: eventData.htmlContent.length
                    });
                    return {
                      ...version,
                      html_content: eventData.htmlContent
                    };
                  }
                  return version;
                });
                return updated;
              });
            }

            // 🎯 版本创建时，立即更新菜单显示为最新版本
            if (eventData.type === 'version-created' && eventData.versionNumber && eventData.versionNumber !== currentVersion) {
              console.log('🎯 Preview: 立即更新菜单显示为最新版本', {
                oldVersion: currentVersion,
                newVersion: eventData.versionNumber
              });
              setCurrentVersion(eventData.versionNumber);

              // 🎯 关键修复：通知外部版本号变化
              if (onVersionNumberChange) {
                onVersionNumberChange(eventData.versionNumber);
                console.log('✅ Preview: 已通知外部版本号变化（localStorage事件）', { newVersion: eventData.versionNumber });
              }
            }

            debouncedRefresh(); // 🔧 使用防抖刷新
          } else {
            console.log('🔍 Preview: localStorage事件项目ID不匹配', {
              eventProjectId: eventData.projectId,
              currentProjectId: projectId
            });
          }
        } catch (error) {
          console.log('⚠️ Preview: localStorage事件解析失败', error);
        }
      }
    };
    window.addEventListener('storage', storageListener);
    console.log('✅ Preview: localStorage监听器已设置');

    // 🔧 方法3：自定义事件监听（同页面通信）
    customEventListener = (e: CustomEvent) => {
      console.log('📡 Preview: 收到自定义事件', e.detail);
      if (e.detail?.projectId === projectId) {
        console.log('🔄 Preview: 自定义事件检测到版本更新', {
          projectId,
          versionNumber: e.detail.versionNumber,
          eventType: e.detail.type
        });

        // 🎯 关键修复：如果是版本内容更新，立即更新版本列表缓存
        if (e.detail.type === 'version-updated' && e.detail.versionNumber && e.detail.htmlContent) {
          setVersions(prevVersions => {
            const updated = prevVersions.map(version => {
              if (version.version_number === e.detail.versionNumber) {
                console.log('🔄 Preview: 更新版本缓存内容（自定义事件）', {
                  versionNumber: version.version_number,
                  oldLength: version.html_content.length,
                  newLength: e.detail.htmlContent.length
                });
                return {
                  ...version,
                  html_content: e.detail.htmlContent
                };
              }
              return version;
            });
            return updated;
          });
        }

        // 🎯 版本创建时，立即更新菜单显示为最新版本
        if (e.detail.type === 'version-created' && e.detail.versionNumber && e.detail.versionNumber !== currentVersion) {
          console.log('🎯 Preview: 立即更新菜单显示为最新版本', {
            oldVersion: currentVersion,
            newVersion: e.detail.versionNumber
          });
          setCurrentVersion(e.detail.versionNumber);

          // 🎯 关键修复：通知外部版本号变化
          if (onVersionNumberChange) {
            onVersionNumberChange(e.detail.versionNumber);
            console.log('✅ Preview: 已通知外部版本号变化（自定义事件）', { newVersion: e.detail.versionNumber });
          }
        }

        debouncedRefresh(); // 🔧 使用防抖刷新
      }
    };
    window.addEventListener('refreshVersionList', customEventListener as EventListener);
    console.log('✅ Preview: 自定义事件监听器已设置');

    // 🔧 减少轮询频率，从5秒改为10秒
    const pollInterval = setInterval(() => {
      const lastCheck = localStorage.getItem('version-check-trigger');
      if (lastCheck && lastCheck === projectId) {
        console.log('🔄 Preview: 定时检查触发版本刷新', { projectId });
        debouncedRefresh(); // 🔧 使用防抖刷新
        localStorage.removeItem('version-check-trigger');
      }
    }, 10000); // 🔧 减少轮询频率到10秒

    return () => {
      console.log('🧹 Preview: 清理版本更新监听器', { projectId });
      if (versionUpdateChannel) {
        versionUpdateChannel.close();
      }
      if (storageListener) {
        window.removeEventListener('storage', storageListener);
      }
      if (customEventListener) {
        window.removeEventListener('refreshVersionList', customEventListener as EventListener);
      }
      clearInterval(pollInterval);
    };
  }, [projectId]); // 🔧 关键修复：只依赖projectId，移除其他依赖防止循环

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.version-menu-container')) {
        setIsVersionMenuOpen(false);
      }
    };

    if (isVersionMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isVersionMenuOpen]);

  useUpdateEffect(() => {
    const cleanupListeners = () => {
      if (iframeRef?.current?.contentDocument) {
        const iframeDocument = iframeRef.current.contentDocument;
        iframeDocument.removeEventListener("mouseover", handleMouseOver);
        iframeDocument.removeEventListener("mouseout", handleMouseOut);
        iframeDocument.removeEventListener("mouseleave", handleMouseOut);
        iframeDocument.removeEventListener("click", handleClick);

        // 🧹 清理所有残留状态
        iframeDocument.querySelectorAll('.hovered-element').forEach(el => {
          el.classList.remove('hovered-element');
        });
      }
    };

    const setupEventListeners = () => {
      if (!iframeRef?.current) return;

      const iframeDocument = iframeRef.current.contentDocument;
      if (!iframeDocument) return;

      // 🎯 关键修复：先确保样式存在，再绑定事件
      const stylesReady = ensureSelectedElementStyles();
      if (!stylesReady) {
        console.warn('⚠️ 样式未就绪，延迟绑定事件监听器');
        setTimeout(setupEventListeners, 50);
        return;
      }

      cleanupListeners();

      if (isEditableModeEnabled) {
        console.log('🎯 绑定编辑模式事件监听器');

        // 🎯 使用更精确的事件监听
        iframeDocument.addEventListener("mouseover", handleMouseOver, {
          passive: true,
          capture: false
        });
        iframeDocument.addEventListener("mouseout", handleMouseOut, {
          passive: true,
          capture: false
        });
        iframeDocument.addEventListener("mouseleave", handleMouseOut, {
          passive: true,
          capture: false
        });
        // 🚨 关键修复：最高优先级事件拦截 - 确保在所有其他监听器之前执行
        iframeDocument.addEventListener("click", handleClick, {
          passive: false,
          capture: true  // 使用捕获阶段，最早拦截事件
        });

        // 🎯 简化：只监听必要的事件类型
        console.log('✅ 编辑模式事件监听器已绑定（简化版）');

        console.log('✅ 编辑模式事件监听器已绑定');
      }
    };

    // 🎯 延迟执行，确保iframe完全加载
    const timeoutId = setTimeout(setupEventListeners, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanupListeners();
    };
  }, [iframeRef, isEditableModeEnabled, handleMouseOver, handleMouseOut, handleClick, ensureSelectedElementStyles]);

  // 🎯 直接删除元素功能 - 立即删除但保留编辑状态
  const handleDeleteElement = useCallback((element: HTMLElement) => {
    if (!iframeRef?.current) return;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return;

    try {
      // 🎯 保存元素信息用于显示和撤销
      const elementInfo = {
        tagName: element.tagName,
        className: element.className,
        id: element.id,
        textContent: element.textContent?.trim().substring(0, 100) || '无文本内容',
        outerHTML: element.outerHTML,
        parentElement: element.parentElement,
        nextSibling: element.nextSibling
      };

      // 🎯 立即删除元素
      element.remove();

      // 🎯 触发删除完成事件，通知样式面板显示删除状态
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('elementDeleted', {
          detail: {
            elementInfo: elementInfo,
            deletedAt: Date.now()
          }
        }));
      }

      console.log('✅ 元素已删除', {
        element: elementInfo.tagName,
        className: elementInfo.className
      });

      toast.success('元素已删除，点击保存应用更改');
    } catch (error) {
      console.error('❌ 删除元素失败:', error);
      toast.error('删除元素失败');
    }
  }, [iframeRef]);

  // 🎯 编辑文本功能
  const handleEditText = useCallback((element: HTMLElement) => {
    if (!iframeRef?.current) return;

    const iframeDocument = iframeRef.current.contentDocument;
    if (!iframeDocument) return;

    try {
      // 获取元素的文本内容
      const currentText = element.textContent || '';

      // 设置元素为可编辑
      element.contentEditable = 'true';
      element.focus();

      // 选中所有文本
      const range = iframeDocument.createRange();
      range.selectNodeContents(element);
      const selection = iframeDocument.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);

      // 添加编辑样式
      element.style.outline = '2px solid #3b82f6';
      element.style.outlineOffset = '2px';
      element.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';

      setIsEditingText(true);

      // 处理编辑完成
      const handleEditComplete = () => {
        element.contentEditable = 'false';
        element.style.outline = '';
        element.style.outlineOffset = '';
        element.style.backgroundColor = '';

        setIsEditingText(false);

        // 获取更新后的HTML并保存
        const updatedHtml = iframeDocument.documentElement.outerHTML;

        // 触发HTML更新
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('htmlUpdated', {
            detail: { html: updatedHtml, source: 'textEdit' }
          }));
        }

        console.log('✅ 文本编辑完成', {
          element: element.tagName,
          oldText: currentText,
          newText: element.textContent
        });

        toast.success('文本已更新');

        // 清理事件监听器
        element.removeEventListener('blur', handleEditComplete);
        element.removeEventListener('keydown', handleKeyDown);
      };

      // 处理键盘事件
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          handleEditComplete();
        } else if (e.key === 'Escape') {
          // 取消编辑，恢复原文本
          element.textContent = currentText;
          handleEditComplete();
        }
      };

      // 添加事件监听器
      element.addEventListener('blur', handleEditComplete);
      element.addEventListener('keydown', handleKeyDown);

      console.log('🎯 开始编辑文本', {
        element: element.tagName,
        text: currentText
      });

    } catch (error) {
      console.error('❌ 编辑文本失败:', error);
      toast.error('编辑文本失败');
    }
  }, [iframeRef, setIsEditingText]);

  // 🚨 强化版：项目首次创建时的强制初始化 + 清理旧事件
  useEffect(() => {
    if (!projectId) return; // 只在有项目ID时执行

    const forceInitializeForNewProject = () => {
      console.log('🚀 项目首次创建，强制初始化编辑功能');

      const initAttempt = () => {
        if (!iframeRef?.current?.contentDocument) {
          console.log('⏳ iframe未就绪，延迟初始化');
          setTimeout(initAttempt, 100);
          return;
        }

        const iframeDocument = iframeRef.current.contentDocument;
        const iframeWindow = iframeDocument.defaultView as any;

        // 🚨 关键修复：强制清理旧的事件监听器
        if (iframeWindow && typeof iframeWindow.__editModeCleanup === 'function') {
          iframeWindow.__editModeCleanup();
          console.log('🧹 强制清理了旧的事件监听器');
        }

        // 1. 强制注入样式
        const stylesApplied = ensureSelectedElementStyles();
        console.log('🎯 强制样式注入结果:', stylesApplied);

        // 2. 如果在编辑模式，确保事件监听器正确绑定
        if (isEditableModeEnabled) {
          console.log('🎯 编辑模式已启用，重新绑定事件监听器');

          // 强制触发事件监听器重新绑定
          setTimeout(() => {
            // 通过改变状态触发 useUpdateEffect 重新执行
            setHoveredElement(prev => prev); // 触发重新绑定
          }, 100);
        }

        console.log('✅ 项目首次创建初始化完成');
      };

      // 延迟执行，确保所有组件都已挂载
      setTimeout(initAttempt, 300);
    };

    // 只在项目ID变化时执行一次
    forceInitializeForNewProject();
  }, [projectId, ensureSelectedElementStyles, isEditableModeEnabled, iframeRef]);

  // iframe滚动性能优化
  useIframeScrollOptimization(iframeRef || { current: null }, isEditableModeEnabled);

  // 编辑模式变化时重新注入样式
  useEffect(() => {
    const applyEditStyles = () => {
      if (iframeRef?.current?.contentDocument) {
        const iframeDocument = iframeRef.current.contentDocument;
        
        const existingStyle = iframeDocument.querySelector('#edit-mode-style');
        if (existingStyle) {
          existingStyle.remove();
        }
        
        if (isEditableModeEnabled) {
          const style = iframeDocument.createElement('style');
          style.id = 'edit-mode-style';
          style.textContent = `
            *, *::before, *::after {
              cursor: crosshair !important;
              pointer-events: auto !important;
            }
            html, body {
              cursor: crosshair !important;
            }
            a, button, input, textarea, select {
              cursor: crosshair !important;
              pointer-events: auto !important;
            }
            /* 🎯 关键修复：禁用所有交互元素的默认行为 */
            a {
              text-decoration: none !important;
            }
            button, input[type="submit"], input[type="button"] {
              pointer-events: auto !important;
            }
            .hovered-element {
              outline: 2px solid rgba(59, 130, 246, 0.8) !important;
              outline-offset: 1px !important;
              background-color: rgba(59, 130, 246, 0.06) !important;
              box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3) !important;
              transition: none !important;
              position: relative !important;
              z-index: 1000 !important;
            }
            .selected-element-highlight {
              outline: 3px solid rgba(34, 197, 94, 0.9) !important;
              outline-offset: 2px !important;
              background-color: rgba(34, 197, 94, 0.08) !important;
              box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.4) !important;
              transition: none !important;
              position: relative !important;
              z-index: 1001 !important;
            }
          `;
          iframeDocument.head.appendChild(style);

          // 🎯 关键修复：注入JavaScript来阻止所有默认行为
          const script = iframeDocument.createElement('script');
          script.id = 'edit-mode-script';
          script.textContent = `
            (function() {
              'use strict';
              console.log('🎯 编辑模式：注入事件阻止脚本');

              // 🔧 创建全局清理函数
              window.__editModeCleanup = function() {
                if (window.__editModeListeners) {
                  window.__editModeListeners.forEach(({ element, type, listener, options }) => {
                    element.removeEventListener(type, listener, options);
                  });
                  window.__editModeListeners = [];
                  console.log('✅ 编辑模式事件监听器已清理');
                }
              };

              // 🚨 强制清理：先调用清理函数清除旧的监听器
              if (typeof window.__editModeCleanup === 'function') {
                window.__editModeCleanup();
              }

              // 🔧 存储事件监听器以便后续清理
              window.__editModeListeners = [];

              console.log('🔄 开始绑定新的编辑模式事件监听器');

              // 🚨 关键修复：移除所有按钮的内联事件处理器
              const buttons = document.querySelectorAll('button');
              buttons.forEach(button => {
                // 移除内联事件处理器
                button.onclick = null;
                button.onmousedown = null;
                button.onmouseup = null;

                // 移除事件属性
                button.removeAttribute('onclick');
                button.removeAttribute('onmousedown');
                button.removeAttribute('onmouseup');

                console.log('🧹 已清理按钮的内联事件:', button.tagName, button.className);
              });

              // 🎯 精准修复：只阻止功能性元素的默认行为，不阻止用户点击
              const clickHandler = function(e) {
                const target = e.target;
                const link = target.closest && target.closest('a');
                const button = target.closest && target.closest('button');
                const input = target.closest && target.closest('input[type="submit"], input[type="button"]');

                // 只对功能性元素阻止默认行为，不阻止事件传播
                if (link) {
                  const href = link.getAttribute('href');
                  if (href && href !== '#' && !href.startsWith('#')) {
                    e.preventDefault(); // 只阻止导航
                    console.log('🚫 编辑模式：链接导航已阻止', href);
                  }
                } else if (button) {
                  // 🚨 关键修复：阻止所有按钮，不管类型
                  e.preventDefault();
                  e.stopPropagation();
                  e.stopImmediatePropagation();
                  console.log('🚫 编辑模式：所有按钮功能已阻止', button.tagName);
                  return false;
                } else if (input) {
                  e.preventDefault(); // 阻止输入按钮功能
                  console.log('🚫 编辑模式：输入按钮功能已阻止', input.type);
                }

                // 关键：不阻止事件传播，让用户的选择点击正常工作
              };

              // 阻止所有表单提交
              const submitHandler = function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🚫 编辑模式：表单提交被阻止');
                return false;
              };

              // 阻止回车键提交
              const keydownHandler = function(e) {
                if (e.key === 'Enter' && (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA')) {
                  e.preventDefault();
                  console.log('🚫 编辑模式：回车键被阻止');
                  return false;
                }
              };

              // 🚨 关键修复：使用最高优先级阻止所有可能的事件
              const options = { capture: true, passive: false };

              // 🎯 添加多种事件类型的阻止，确保覆盖所有可能的交互
              const eventTypes = ['click', 'mousedown', 'mouseup', 'dblclick'];
              eventTypes.forEach(eventType => {
                document.addEventListener(eventType, clickHandler, options);
              });

              document.addEventListener('submit', submitHandler, options);
              document.addEventListener('keydown', keydownHandler, options);

              // 记录监听器以便清理
              eventTypes.forEach(eventType => {
                window.__editModeListeners.push(
                  { element: document, type: eventType, listener: clickHandler, options }
                );
              });

              window.__editModeListeners.push(
                { element: document, type: 'submit', listener: submitHandler, options },
                { element: document, type: 'keydown', listener: keydownHandler, options }
              );

              console.log('✅ 编辑模式：所有交互事件已被禁用');
            })();
          `;
          iframeDocument.head.appendChild(script);

          console.log('🎯 编辑模式样式和脚本已注入 - 所有交互已禁用');
        } else {
          // 🎯 关键修复：保留选中元素样式，只移除编辑模式相关样式
          const existingStyle = iframeDocument.getElementById('edit-mode-style');
          if (existingStyle) {
            // 🎯 创建新的样式标签，只包含选中元素的样式
            const persistentStyle = iframeDocument.createElement('style');
            persistentStyle.id = 'selected-element-style';
            persistentStyle.textContent = `
              .selected-element-highlight {
                outline: 3px solid rgba(34, 197, 94, 0.9) !important;
                outline-offset: 2px !important;
                box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.4) !important;
                transition: none !important;
                position: relative !important;
                z-index: 1001 !important;
              }
            `;

            // 先添加持久样式，再移除编辑模式样式
            iframeDocument.head.appendChild(persistentStyle);
            existingStyle.remove();
            console.log('✅ 编辑模式样式已移除，选中元素样式已保留');
          }

          // 🔧 调用清理函数移除事件监听器
          const iframeWindow = iframeDocument.defaultView as any;
          if (iframeWindow && typeof iframeWindow.__editModeCleanup === 'function') {
            iframeWindow.__editModeCleanup();
          }

          // 🔧 移除脚本标签
          const existingScript = iframeDocument.getElementById('edit-mode-script');
          if (existingScript) {
            existingScript.remove();
            console.log('✅ 编辑模式脚本已移除');
          }

          // 🎯 清理所有悬停状态和选中状态
          iframeDocument.querySelectorAll('.hovered-element').forEach(el => {
            el.classList.remove('hovered-element');
          });

          // 🎯 关键修复：编辑模式禁用时不清理选中元素高亮，保持用户选择的可见性
          console.log('🎯 编辑模式禁用，保留选中元素高亮状态');

          console.log('✅ 编辑模式已完全禁用，所有高亮状态已清理，原始交互功能已恢复');
        }
      }
    };
    
    applyEditStyles();

    const iframe = iframeRef?.current;
    if (iframe) {
      const handleLoad = () => {
        console.log('🔄 iframe加载完成，应用编辑样式');
        applyEditStyles();

        // 🎯 关键修复：立即确保选中元素样式存在
        setTimeout(() => {
          const stylesApplied = ensureSelectedElementStyles();
          console.log('🎯 iframe加载后样式状态:', { stylesApplied });

          // 🎯 如果有选中元素，重新应用选中状态
          if (selectedElement && stylesApplied) {
            const iframeDocument = iframe.contentDocument;
            if (iframeDocument) {
              // 清理旧的选中状态
              iframeDocument.querySelectorAll('.selected-element-highlight').forEach(el => {
                el.classList.remove('selected-element-highlight');
              });

              // 重新应用选中状态（如果元素仍然存在）
              const elementInNewDoc = iframeDocument.querySelector(`[data-element-id="${selectedElement.dataset.elementId}"]`) as HTMLElement;
              if (elementInNewDoc) {
                elementInNewDoc.classList.add('selected-element-highlight');
                console.log('✅ 重新应用了选中元素高亮');
              }
            }
          }
        }, 10); // 减少延迟，立即应用
      };

      iframe.addEventListener('load', handleLoad);
      return () => {
        iframe.removeEventListener('load', handleLoad);
      };
    }
  }, [isEditableModeEnabled, iframeRef, selectedElement, ensureSelectedElementStyles]);

  // 🎯 强化版：在HTML内容变化时确保样式和事件监听器都正确设置
  useEffect(() => {
    const initializeAfterContentChange = () => {
      if (!iframeRef?.current?.contentDocument) return;

      console.log('🔄 HTML内容变化，重新初始化样式和事件');

      // 1. 确保样式存在
      const stylesApplied = ensureSelectedElementStyles();

      // 2. 如果在编辑模式且样式已应用，确保事件监听器正确绑定
      if (isEditableModeEnabled && stylesApplied) {
        const iframeDocument = iframeRef.current.contentDocument;

        // 检查事件监听器是否已绑定（通过检查是否有编辑模式样式）
        const hasEditModeStyles = iframeDocument.getElementById('selected-element-style');
        if (!hasEditModeStyles) {
          console.log('🔄 重新绑定事件监听器');
          // 触发事件监听器重新绑定
          setTimeout(() => {
            // 这会触发 useUpdateEffect 重新执行
            setHoveredElement(null);
          }, 50);
        }
      }

      console.log('✅ HTML内容变化后初始化完成', {
        stylesApplied,
        isEditableModeEnabled,
        hasIframeDoc: !!iframeRef?.current?.contentDocument
      });
    };

    const timeoutId = setTimeout(initializeAfterContentChange, 100);

    return () => clearTimeout(timeoutId);
  }, [html, streamingHtml, ensureSelectedElementStyles, isEditableModeEnabled, iframeRef]);



  // 🚀 核心功能：流式更新iframe内容，避免刷新
  const updateIframeStreamingContent = useCallback((newStreamingHtml: string) => {
    if (!iframeRef?.current?.contentDocument || !newStreamingHtml) {
      return;
    }

    try {
      const iframeDoc = iframeRef.current.contentDocument;
      const lastContent = lastStreamingContentRef.current;

      // 🎯 关键：只有当内容真正变化时才更新
      if (newStreamingHtml === lastContent) {
        return;
      }

      console.log('🔄 流式更新iframe内容', {
        lastLength: lastContent.length,
        newLength: newStreamingHtml.length,
        isIncremental: newStreamingHtml.startsWith(lastContent)
      });

      // 🚀 方案1：智能增量更新
      if (lastContent && newStreamingHtml.startsWith(lastContent)) {
        const newContent = newStreamingHtml.slice(lastContent.length);
        if (newContent.trim()) {
          console.log('🎯 尝试增量更新', { newContentLength: newContent.length });

          // 🔧 智能增量更新：直接更新body的innerHTML
          const body = iframeDoc.body;
          if (body) {
            try {
              // 🎯 关键：直接设置完整的新HTML到body
              const parser = new DOMParser();
              const newDoc = parser.parseFromString(newStreamingHtml, 'text/html');

              if (newDoc.body) {
                // 保持滚动位置
                const scrollTop = body.scrollTop;

                // 更新body内容
                body.innerHTML = newDoc.body.innerHTML;

                // 恢复滚动位置（如果需要）
                if (scrollTop > 0) {
                  body.scrollTop = scrollTop;
                }

                lastStreamingContentRef.current = newStreamingHtml;
                console.log('✅ 智能增量更新成功');
                return;
              }
            } catch (error) {
              console.warn('⚠️ 智能增量更新失败，使用完整替换:', error);
            }
          }
        }
      }

      // 🚀 方案2：完整替换（但不刷新iframe）
      iframeDoc.open();
      iframeDoc.write(newStreamingHtml);
      iframeDoc.close();

      lastStreamingContentRef.current = newStreamingHtml;
      console.log('✅ 完整更新成功');

    } catch (error) {
      console.warn('⚠️ 流式更新失败，回退到srcDoc方式:', error);
      // 回退方案：使用srcDoc（会刷新）
      if (iframeRef.current) {
        iframeRef.current.srcDoc = newStreamingHtml;
      }
    }
  }, [iframeRef]);

  // 🎯 监听streamingHtml变化，进行流式更新
  useEffect(() => {
    if (!streamingHtml) {
      return;
    }

    // 🔧 防抖处理，避免过于频繁的更新
    if (streamingUpdateTimeoutRef.current) {
      clearTimeout(streamingUpdateTimeoutRef.current);
    }

    streamingUpdateTimeoutRef.current = setTimeout(() => {
      updateIframeStreamingContent(streamingHtml);
    }, 50); // 50ms防抖，保持流畅性

    return () => {
      if (streamingUpdateTimeoutRef.current) {
        clearTimeout(streamingUpdateTimeoutRef.current);
      }
    };
  }, [streamingHtml, updateIframeStreamingContent]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
      if (streamingUpdateTimeoutRef.current) {
        clearTimeout(streamingUpdateTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={ref}
      className={classNames(
        "w-full h-full relative z-0 flex flex-col overflow-hidden preview-container-optimized critical-optimized",
        {
          "max-lg:h-0 max-lg:overflow-hidden": currentTab === "chat",
          "max-lg:h-full max-lg:overflow-hidden": currentTab === "preview",
          "edit-mode-active": isEditableModeEnabled,
        }
      )}
      style={{ margin: 0, padding: 0 }}
      onClick={(e) => {
        if (isAiWorking) {
          e.preventDefault();
          e.stopPropagation();
          toast.warning("不要着急哦~ 要等AI完成工作呀 ✨");
        }
      }}
    >
      {/* 标签栏 - 主题优化 */}
      <div className="w-full bg-card border-b border-border flex items-center justify-between flex-shrink-0 px-2 m-0">
        <div className="flex items-center ml-1 py-1">
          <button 
            className={cn(
              "px-3 py-1.5 text-sm font-medium transition-colors duration-200 rounded-t-md",
              !showCode ? "bg-secondary text-foreground border-b-2 border-blue-500" : "text-muted-foreground hover:text-foreground hover:bg-secondary/50"
            )}
            onClick={() => setShowCode(false)}
          >
            预览
          </button>
          <button 
            className={cn(
              "px-3 py-1.5 text-sm font-medium transition-colors duration-200 rounded-t-md ml-1",
              showCode ? "bg-secondary text-foreground border-b-2 border-blue-500" : "text-muted-foreground hover:text-foreground hover:bg-secondary/50"
            )}
            onClick={() => setShowCode(true)}
          >
            代码
          </button>
          
          {/* 设备尺寸选择器 */}
          {!showCode && (
            <div className="flex items-center ml-6 gap-2">
              <span className="text-xs text-muted-foreground">尺寸:</span>
              <select 
                value={device}
                onChange={(e) => onDeviceChange?.(e.target.value)}
                className="bg-secondary border border-border rounded px-2 py-1 text-xs text-foreground hover:border-ring focus:border-ring focus:outline-none"
              >
                <option value="desktop">🖥️ 桌面</option>
                <option value="mobile">📱 iPhone 14 Pro Max</option>
                <option value="tablet">📱 iPad Pro</option>
                <option value="iphone-se">📱 iPhone SE</option>
                <option value="iphone-xr">📱 iPhone XR</option>
                <option value="samsung-s8">📱 Samsung Galaxy S8+</option>
                <option value="samsung-s20">📱 Samsung Galaxy S20 Ultra</option>
                <option value="ipad-mini">📱 iPad Mini</option>
                <option value="surface-pro">📱 Surface Pro 7</option>
              </select>
              
              {/* 撤销/重做按钮 - 在编辑模式或有选中元素时显示 */}
              {(isEditableModeEnabled || selectedElement) && (
                <>
                  <div className="w-px h-4 bg-border mx-1"></div>
                  <button
                    onClick={() => {
                      // 🎯 修复：直接调用撤销系统
                      if (undoRedoSystem) {
                        const undoHtml = undoRedoSystem.undo();
                        if (undoHtml && onHtmlChange) {
                          onHtmlChange(undoHtml);
                        }
                      }
                    }}
                    disabled={!undoRedoSystem?.canUndo}
                    className="p-1.5 text-muted-foreground hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/20 rounded transition-all duration-150 hover:scale-105 active:scale-95 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100"
                    title={`撤销 (${getUndoShortcut()})`}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                    </svg>
                  </button>
                  <button
                    onClick={() => {
                      // 🎯 修复：直接调用撤销系统
                      if (undoRedoSystem) {
                        const redoHtml = undoRedoSystem.redo();
                        if (redoHtml && onHtmlChange) {
                          onHtmlChange(redoHtml);
                        }
                      }
                    }}
                    disabled={!undoRedoSystem?.canRedo}
                    className="p-1.5 text-muted-foreground hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-950/20 rounded transition-all duration-150 hover:scale-105 active:scale-95 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100"
                    title={`重做 (${getRedoShortcut()})`}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10H11a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6" />
                    </svg>
                  </button>
                </>
              )}

              {/* 刷新按钮 */}
              <button
                onClick={onRefresh}
                className="p-1.5 text-muted-foreground hover:text-foreground hover:bg-secondary rounded transition-colors"
                title="刷新预览"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>

              {/* 模式切换按钮 */}
              <button
                onClick={onModeToggle}
                className="p-1.5 text-muted-foreground hover:text-foreground hover:bg-secondary rounded transition-colors"
                title={currentTab === "preview" ? "切换到聊天模式" : "切换到预览模式"}
              >
                {currentTab === "preview" ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
          )}
        </div>
        
        {/* 导出菜单和版本选择器容器 - 优化显示条件 */}
        {(projectId || (streamingHtml || html).trim().length > 0) && (
          <div className="flex items-center gap-1">
            {/* 导出菜单 - 有内容时立即显示 */}
            {(streamingHtml || html).trim().length > 0 && (
              <ExportMenu
                htmlContent={streamingHtml || html}
                projectTitle={projectId ? `项目-${projectId}` : "LoomRun项目"}
                projectId={projectId ? parseInt(projectId) : undefined}
              />
            )}
            
            {/* 版本选择菜单 - 仅在有项目ID时显示 */}
            {projectId && (
              <div className="relative version-menu-container">
            <button
              onClick={() => setIsVersionMenuOpen(!isVersionMenuOpen)}
              className="h-8 px-3 text-xs text-foreground hover:text-foreground bg-secondary/70 hover:bg-secondary/80 border border-border hover:border-ring rounded-md transition-all duration-200 flex items-center gap-1.5 shadow-md hover:shadow-lg"
              disabled={loadingVersions}
              data-version-button="updated"
            >
              v{currentVersion}
              <ChevronDown className={cn(
                "w-3 h-3 transition-transform duration-200",
                isVersionMenuOpen && "rotate-180"
              )} />
            </button>
            
            {/* 下拉菜单 - 主题优化 */}
            {isVersionMenuOpen && (
              <div className="absolute right-0 top-full mt-1 w-28 bg-card border border-border rounded-md shadow-xl z-50 max-h-80 overflow-y-auto">
                <div className="py-1">
                  {versions.length === 0 ? (
                    <div className="px-3 py-3 text-center text-muted-foreground text-xs">
                      {loadingVersions ? "加载中..." : "暂无版本记录"}
                    </div>
                  ) : (
                    versions.map((version) => (
                      <div
                        key={version.id}
                        className={cn(
                          "px-3 py-1.5 hover:bg-secondary/50 cursor-pointer transition-colors duration-150",
                          version.version_number === currentVersion && "bg-secondary/30"
                        )}
                        onClick={() => handleVersionSelect(version)}
                      >
                        <div>
                          <div className="flex items-center gap-1.5 mb-1">
                            <span className={cn(
                              "text-xs font-medium",
                              version.version_number === currentVersion ? "text-blue-600 dark:text-blue-400" : "text-foreground"
                            )}>
                              版本 {version.version_number}
                            </span>
                            {version.version_number === currentVersion && (
                              <span className="text-xs bg-green-500/20 text-green-600 dark:text-green-400 px-1 py-0.5 rounded text-[10px]">
                                当前
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="w-2.5 h-2.5" />
                            {formatTime(version.created_at)}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* 内容区域 */}
      <div className="flex-1 relative flex justify-center items-stretch overflow-hidden">
        {/* 静态网格背景 - 主题优化 */}
        <div className="absolute inset-0 -z-10">
          {/* 深色模式专用网格背景 */}
          <div 
            className="absolute inset-0 dark:block hidden"
            style={{
              backgroundImage: `
                linear-gradient(rgba(6, 182, 212, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(6, 182, 212, 0.02) 1px, transparent 1px),
                linear-gradient(rgba(59, 130, 246, 0.015) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.015) 1px, transparent 1px)
              `,
              backgroundSize: "80px 80px, 80px 80px, 40px 40px, 40px 40px"
            }}
          />
          
          {/* 浅色模式专用网格背景 */}
          <div 
            className="absolute inset-0 block dark:hidden"
            style={{
              backgroundImage: `
                linear-gradient(rgba(59, 130, 246, 0.04) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.04) 1px, transparent 1px),
                linear-gradient(rgba(16, 185, 129, 0.025) 1px, transparent 1px),
                linear-gradient(90deg, rgba(16, 185, 129, 0.025) 1px, transparent 1px)
              `,
              backgroundSize: "80px 80px, 80px 80px, 40px 40px, 40px 40px"
            }}
          />
          
          {/* 浅色模式微妙点阵 */}
          <div 
            className="absolute inset-0 block dark:hidden opacity-10"
            style={{
              backgroundImage: `
                radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.08) 1px, transparent 0)
              `,
              backgroundSize: "40px 40px"
            }}
          />
        </div>
        {showCode ? (
          <div 
            className={classNames(
              "w-full h-full bg-white dark:bg-[#0d1117] code-scroll-optimized memory-optimized",
              {
                "lg:border lg:border-border lg:shadow-sm":
                  currentTab !== "preview",
              }
            )}
          >
          <CodeEditor html={streamingHtml || html} isStreaming={!!streamingHtml} />
        </div>
      ) : (
        <iframe
          id="preview-iframe"
          ref={iframeRef}
          title="output"
          className={classNames(
            "w-full h-full select-none bg-background iframe-scroll-optimized scroll-optimized",
            {
              "pointer-events-none": isResizing || isAiWorking,
              // 设备样式 - 主题优化
              "lg:max-w-[428px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[28px] mobile-scroll-optimized device-frame-optimized": 
                device === "mobile",
              "lg:max-w-[1024px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[18px]": 
                device === "tablet",
              "lg:max-w-[375px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[28px]": 
                device === "iphone-se",
              "lg:max-w-[414px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[28px]": 
                device === "iphone-xr",
              "lg:max-w-[360px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[18px]": 
                device === "samsung-s8",
              "lg:max-w-[412px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[22px]": 
                device === "samsung-s20",
              "lg:max-w-[768px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[18px]": 
                device === "ipad-mini",
              "lg:max-w-[912px] lg:mx-auto lg:border lg:border-border lg:shadow-sm lg:rounded-[8px]": 
                device === "surface-pro",
              "lg:border lg:border-border lg:shadow-sm":
                device === "desktop",
            }
          )}
          srcDoc={html}
          onLoad={() => {
            console.log('🔄 iframe 初始加载完成');

            // 🎯 重置流式内容追踪
            lastStreamingContentRef.current = html;

            // 🚀 如果有待处理的streamingHtml，立即应用
            if (streamingHtml && streamingHtml !== html) {
              console.log('🔄 iframe加载完成，应用待处理的流式内容');
              setTimeout(() => {
                updateIframeStreamingContent(streamingHtml);
              }, 100);
            }

            // 🎯 基本的滚动处理（仅在需要时）
            if (iframeRef?.current?.contentWindow?.document?.body && !isAiWorking) {
              setTimeout(() => {
                if (iframeRef.current?.contentWindow?.document?.body) {
                  iframeRef.current.contentWindow.document.body.scrollIntoView({
                    block: "start",
                    inline: "nearest",
                    behavior: "instant",
                  });
                }
              }, 200);
            }
          }}
        />
      )}

      {/* 🎯 元素工具栏 - 删除和编辑文本按钮 */}
      {selectedElement && iframeRef && (
        <ElementToolbar
          selectedElement={selectedElement}
          iframeRef={iframeRef}
          onDeleteElement={handleDeleteElement}
          onEditText={handleEditText}
        />
      )}
      </div>
    </div>
  );
};