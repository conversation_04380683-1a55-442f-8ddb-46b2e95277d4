import Link from "next/link";
import { formatDistance } from "date-fns";
import { EllipsisVert<PERSON>, Settings, MessageSquare, Code2, Clock } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ProjectCardProps {
  project: {
    id: number;
    title: string;
    html_content: string;
    created_at: string;
    updated_at: string;
    latest_version?: number;
    version_count?: number;
    message_count?: number;
  };
}

export function ProjectCard({ project }: ProjectCardProps) {
  return (
    <div className="text-neutral-200 space-y-4 group cursor-pointer">
      <Link
        href={`/projects/${project.id}`}
        className="relative bg-neutral-900 rounded-2xl overflow-hidden h-44 w-full flex items-center justify-end flex-col px-3 border border-neutral-800 hover:border-neutral-600 transition-all duration-200"
      >
        <iframe
          srcDoc={project.html_content}
          className="absolute inset-0 w-full h-full top-0 left-0 group-hover:brightness-75 transition-all duration-200 pointer-events-none"
          style={{ transform: 'scale(0.5)', transformOrigin: 'top left', width: '200%', height: '200%' }}
        />

        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center">
          <Button
            variant="default"
            className="translate-y-4 group-hover:translate-y-0 transition-all duration-200"
          >
            打开项目
          </Button>
        </div>
      </Link>
      
      <div className="flex items-start justify-between gap-3">
        <div className="flex-1 min-w-0">
          <p className="text-neutral-200 text-base font-semibold line-clamp-1 mb-1">
            {project.title || '未命名项目'}
          </p>
          
          <div className="flex items-center gap-4 text-xs text-neutral-500 mb-1">
            {(project.version_count || 0) > 0 && (
              <div className="flex items-center gap-1">
                <Code2 className="w-3 h-3" />
                <span>v{project.latest_version || 1}</span>
              </div>
            )}
            {(project.message_count || 0) > 0 && (
              <div className="flex items-center gap-1">
                <MessageSquare className="w-3 h-3" />
                <span>{project.message_count} 消息</span>
              </div>
            )}
          </div>
          
          <p className="text-sm text-neutral-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            更新于 {formatDistance(
              new Date(project.updated_at),
              new Date(),
              { addSuffix: true, locale: {
                formatDistance: () => ""
              }}
            )}
          </p>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="flex-shrink-0">
              <EllipsisVertical className="text-neutral-400 size-4 hover:text-neutral-300 transition-colors duration-200" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            <DropdownMenuGroup>
              <Link href={`/projects/${project.id}`}>
                <DropdownMenuItem>
                  <Code2 className="size-4 text-neutral-100 mr-2" />
                  编辑项目
                </DropdownMenuItem>
              </Link>
              <DropdownMenuItem>
                <Settings className="size-4 text-neutral-100 mr-2" />
                项目设置
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
