import { toast } from 'sonner';
import { PerformanceLogger } from './performance-logger';
import { unifiedCache } from './unified-cache';

export interface ProjectData {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  chatHistory?: ChatHistoryItem[];
}

export interface ChatHistoryItem {
  id: string;
  message_type: 'user' | 'ai';
  content: string;
  html_content?: string;
  created_at: string;
  version_id?: number;
  metadata?: Record<string, unknown>; // 添加metadata字段
}

// 🔧 新增：原始聊天历史项接口（从API返回的格式）
interface RawChatHistoryItem {
  id?: string | number;
  message_type: 'user' | 'ai';
  content?: string;
  html_content?: string;
  created_at: string;
  version_id?: number;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isGenerating?: boolean;
  htmlContent?: string;
  versionNumber?: number;
  images?: string[]; // 添加图片支持
  requestType?: 'POST' | 'PUT'; // 添加请求类型支持，用于确定AI消息的显示方式
}

export class ProjectManager {
  private static instance: ProjectManager | null = null;
  // 🎯 性能优化：增加缓存引用
  private cache = unifiedCache;
  private deletingProjects = new Set<number>(); // 正在删除的项目ID集合
  public currentProjectId: string | null = null; // 当前项目ID

  private constructor() {
    console.log('ProjectManager initialized');
  }

  static getInstance(): ProjectManager {
    if (!ProjectManager.instance) {
      ProjectManager.instance = new ProjectManager();
    }
    return ProjectManager.instance;
  }

  // 🔧 保存对话到项目（增强版本信息反馈）
  async saveConversationToProject(projectId: number, userPrompt: string, aiHtmlContent: string): Promise<{ success: boolean; versionNumber?: number }> {
    try {
      // 🎯 设置保存状态（UI反馈）
      this.cache.setSavingStatus(projectId, true);
      
      PerformanceLogger.startTimer('save-conversation');
      
      const response = await fetch(`/api/me/projects/${projectId}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userPrompt,
          aiHtmlContent
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const versionNumber = data.result?.versionNumber ? parseInt(data.result.versionNumber) : undefined;
        const versionInfo = versionNumber ? ` (v${versionNumber})` : '';
        
        console.log('✅ 对话已保存到项目' + versionInfo);
        
        // 🚀 性能优化：异步清除缓存，不阻塞主流程
        setTimeout(() => {
          this.cache.invalidateProject(projectId);
        }, 100);
        console.log('🧹 ProjectManager: 已安排缓存清理（异步）');
        
        // 🎯 更新保存状态
        this.cache.setSavingStatus(projectId, false);
        
        toast.success(`对话已保存${versionInfo}`, {
          duration: 2000,
          position: "bottom-right",
        });

        // 🎯 广播版本更新事件 - 高性能实时更新
        if (versionNumber) {
          this.broadcastVersionCreated(projectId.toString(), versionNumber);
        }
        
        PerformanceLogger.endTimer('save-conversation');
        return { success: true, versionNumber };
      } else {
        console.error('❌ 保存对话失败', response.status);
        this.cache.setSavingStatus(projectId, false);
        return { success: false };
      }
    } catch (error) {
      console.error('❌ 保存对话失败:', error);
      this.cache.setSavingStatus(projectId, false);
      PerformanceLogger.error('保存对话异常', error);
      return { success: false };
    }
  }

  // 🔧 转换原始聊天历史数据的通用方法
  private transformRawChatHistory(rawChatHistory: unknown[]): ChatHistoryItem[] {
    if (!Array.isArray(rawChatHistory)) return [];
    
    return rawChatHistory.map((item: unknown) => {
      const typedItem = item as RawChatHistoryItem;
      return {
        id: typedItem.id?.toString() || Date.now().toString(),
        message_type: typedItem.message_type,
        content: typedItem.content || '',
        html_content: typedItem.html_content || '',
        created_at: typedItem.created_at,
        version_id: typedItem.version_id
      };
    });
  }

  // 🔧 加载项目数据（包括聊天历史）
  async loadProjectData(projectId: number): Promise<ProjectData | null> {
    try {
      console.log('📖 加载项目数据:', projectId);
      
      // 🎯 优先从缓存获取
      const cachedData = await this.cache.getProjectDetail(projectId);
      if (cachedData) {
        console.log('✅ 从缓存加载项目数据', {
          projectId,
          hasChatHistory: !!(cachedData.chatHistory && cachedData.chatHistory.length > 0),
          chatHistoryLength: cachedData.chatHistory?.length || 0
        });
        
        // 🔧 关键修复：如果缓存中没有聊天历史，强制从网络加载
        if (!cachedData.chatHistory || cachedData.chatHistory.length === 0) {
          console.log('⚠️ 缓存中缺少聊天历史，强制从网络加载');
          // 清除缓存并从网络重新加载
          this.cache.invalidateProject(projectId);
        } else {
          return {
            ...cachedData,
            chatHistory: this.transformRawChatHistory(cachedData.chatHistory || [])
          };
        }
      }
      
      // 🌐 网络请求
      console.log('🌐 从网络加载项目数据:', projectId);
      const response = await fetch(`/api/me/projects/${projectId}`);
      const data = await response.json();
      
      if (data.ok) {
        console.log('✅ 项目数据加载成功:', {
          projectId: data.project.id,
          title: data.project.title,
          chatHistoryCount: data.chatHistory?.length || 0,
          chatHistoryPreview: data.chatHistory?.slice(0, 2).map((msg: unknown) => {
            const typedMsg = msg as { id: string; message_type: string; content?: string; html_content?: string };
            return {
              id: typedMsg.id,
              type: typedMsg.message_type,
              hasContent: !!(typedMsg.content || typedMsg.html_content)
            };
          }) || []
        });
        
        const projectData = {
          ...data.project,
          chatHistory: this.transformRawChatHistory(data.chatHistory || [])
        };
        
        // 🔧 缓存将在下次请求时自动更新
        
        return projectData;
      } else {
        console.error('❌ 加载项目数据失败:', data.error);
        return null;
      }
    } catch (error) {
      console.error('❌ 加载项目数据异常:', error);
      return null;
    }
  }

  // 🎯 获取项目保存状态（UI使用）
  getProjectSavingStatus(projectId: number): { saving: boolean; lastSaved: number } {
    return this.cache.getSavingStatus(projectId);
  }

  // 🔧 转换聊天历史格式
  transformChatHistory(chatHistoryData: ChatHistoryItem[]): ChatMessage[] {
    if (!chatHistoryData || chatHistoryData.length === 0) {
      return [];
    }

    // 按时间排序并去重
    const uniqueMessages = new Map();
    chatHistoryData
      .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      .forEach((msg) => {
        const key = `${msg.message_type}-${msg.created_at}-${(msg.content || msg.html_content || '').slice(0, 50)}`;
        if (!uniqueMessages.has(key)) {
          uniqueMessages.set(key, msg);
        }
      });
    
    const sortedMessages = Array.from(uniqueMessages.values());
    
    const transformedChatHistory = sortedMessages.map((msg: ChatHistoryItem) => {
      // 🔧 关键修复：使用数据库返回的version_number字段，而不是version_id
      const typedMsg = msg as ChatHistoryItem & { version_number?: number; metadata?: Record<string, unknown> };
      const versionNumber = typedMsg.version_number;
      
      // 🔧 新增：从metadata中解析图片信息
      let images: string[] | undefined = undefined;
      if (typedMsg.metadata && typeof typedMsg.metadata === 'object') {
        const metadata = typedMsg.metadata as { images?: string[] };
        images = metadata.images;
      }
      
      console.log('🔧 处理聊天消息:', {
        messageType: msg.message_type,
        hasVersionNumber: !!versionNumber,
        versionNumber: versionNumber,
        messageId: msg.id,
        hasImages: !!(images && images.length > 0),
        imageCount: images?.length || 0
      });
      
      // 🎯 关键修复：根据版本号推断requestType
      let requestType: 'POST' | 'PUT' | undefined = undefined;
      if (msg.message_type === 'ai' && versionNumber) {
        requestType = versionNumber === 1 ? 'POST' : 'PUT';
      }
      
      return {
        id: msg.id?.toString() || Date.now().toString(),
        type: msg.message_type,
        content: msg.content || '',
        htmlContent: msg.html_content || '',
        timestamp: new Date(msg.created_at),
        isGenerating: false,
        versionNumber: msg.message_type === 'ai' ? versionNumber : undefined,
        images: images, // 添加图片信息
        requestType: requestType // 🎯 添加推断的requestType
      };
    });

    console.log('🔄 聊天历史转换完成:', transformedChatHistory.length, '条消息');
    return transformedChatHistory;
  }

  // 🔧 更新项目内容
  async updateProject(projectId: number, html: string, prompts: string[]): Promise<boolean> {
    try {
      const response = await fetch(`/api/me/projects/${projectId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          html_content: html,
          prompts,
        }),
      });

      const data = await response.json();

      if (data.ok) {
        console.log('✅ 项目更新成功');
        toast.success("已保存", {
          duration: 1500,
          position: "bottom-right",
        });
        return true;
      } else {
        console.error('❌ 项目更新失败:', data.error);
        toast.error("保存失败: " + (data.error || "未知错误"));
        return false;
      }
    } catch (error) {
      console.error('❌ 项目更新异常:', error);
      toast.error("保存失败，请检查网络连接");
      return false;
    }
  }

  // 🗑️ 高效删除项目 - 重试机制 + 缓存清理 + 性能优化
  async deleteProject(projectId: number, maxRetries = 1): Promise<{
    success: boolean;
    error?: string;
    networkError?: boolean;
  }> {
    // 🚫 防止重复删除
    if (this.deletingProjects.has(projectId)) {
      console.warn(`⚠️ 项目 ${projectId} 正在删除中，忽略重复请求`);
      return { success: false, error: '项目正在删除中' };
    }

    this.deletingProjects.add(projectId);
    const startTime = performance.now();
    let lastError: Error | null = null;

    // 🔄 重试逻辑
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        console.log(`🗑️ 删除项目尝试 ${attempt}/${maxRetries + 1} - ID:${projectId}`);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            if (!controller.signal.aborted) {
              console.log(`⏰ 项目 ${projectId} 删除请求超时，中止请求`);
              controller.abort();
            }
          }, 5000); // 5秒超时

          const response = await fetch(`/api/me/projects/${projectId}`, {
            method: "DELETE",
            credentials: "include",
            signal: controller.signal,
            headers: {
              "Content-Type": "application/json",
            },
          });

          // 🧹 及时清理超时
            clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            const duration = performance.now() - startTime;
            
            console.log(`✅ 项目删除成功 (${duration.toFixed(1)}ms):`, {
              projectId,
              attempt,
              deleted: data.deleted || {}
            });

          // 🧹 立即清理所有相关缓存
            await this.cleanupProjectCaches(projectId);

            // 📊 性能监控日志
            if (process.env.NODE_ENV === 'development') {
              const { PerformanceLogger } = await import('@/lib/performance-logger');
              PerformanceLogger.deleteOperation('项目删除', {
                projectId,
                duration,
                success: true,
                retries: attempt - 1,
                cacheCleared: true
              });
            }

            toast.success("项目已删除", {
              duration: 2000,
              position: "bottom-right",
            });

            this.deletingProjects.delete(projectId);
            return { success: true };
          }

          // 🔍 处理不同的HTTP错误状态
          const errorData = await response.json().catch(() => ({ error: '服务器响应格式错误' }));
          
          if (response.status === 404) {
            // 项目不存在，视为删除成功
            await this.cleanupProjectCaches(projectId);
            this.deletingProjects.delete(projectId);
            return { success: true };
          }

          if (response.status === 503 || response.status === 408) {
            // 服务器临时不可用或超时，可以重试
            if (attempt <= maxRetries) {
              const delay = Math.pow(2, attempt - 1) * 1000; // 指数退避
              console.log(`⏳ 延迟 ${delay}ms 后重试...`);
              await new Promise(resolve => setTimeout(resolve, delay));
              continue;
            }
          }

          const errorMessage = errorData.error || `HTTP ${response.status}`;
          console.error(`❌ 删除失败 (尝试 ${attempt}):`, errorMessage);
          this.deletingProjects.delete(projectId);
          return { 
            success: false, 
            error: errorMessage,
            networkError: response.status >= 500
          };

                 } catch (error) {
           lastError = error as Error;
           const duration = performance.now() - startTime;
           
           console.error(`❌ 删除异常 (尝试 ${attempt}, ${duration.toFixed(1)}ms):`, error);
 
           // 🔍 友好的错误处理
           if (error instanceof Error && error.name === 'AbortError') {
             console.warn(`⚠️ 项目 ${projectId} 删除请求超时`);
             lastError = new Error('请求超时，请检查网络连接');
           }
 
           // 🔍 网络错误判断
           const isNetworkError = error instanceof Error && (
             error.name === 'AbortError' ||
             error.message.includes('fetch') ||
             error.message.includes('network') ||
             error.message.includes('timeout')
           );
 
           if (isNetworkError && attempt <= maxRetries) {
             const delay = Math.pow(2, attempt - 1) * 1000; // 指数退避：1s, 2s, 4s
             console.log(`🔄 网络错误，${delay}ms 后重试...`);
             await new Promise(resolve => setTimeout(resolve, delay));
             continue;
           }
         
         // 最后一次尝试失败
         if (attempt === maxRetries + 1) {
           break;
         }
       }
    }

    // 🚨 所有重试都失败了
    const finalError = lastError?.message || '删除失败';
    const isNetworkError = lastError instanceof Error && (
      lastError.name === 'AbortError' ||
      lastError.message.includes('fetch') ||
      lastError.message.includes('network')
    );

    toast.error(isNetworkError ? "网络错误，请检查连接后重试" : finalError, {
      duration: 4000,
      position: "bottom-right",
    });

    this.deletingProjects.delete(projectId);
    return { 
      success: false, 
      error: finalError,
      networkError: isNetworkError
    };
  }

  // 🧹 清理项目相关缓存（高效版本）
  private async cleanupProjectCaches(projectId: number): Promise<void> {
    try {
      console.log(`🧹 清理项目 ${projectId} 相关缓存...`);

      // 🗑️ 使用统一缓存的清理方法
      await this.cache.clearProjectCaches(projectId);

      console.log(`✅ 缓存清理完成 - 项目 ${projectId}`);
    } catch (error) {
      console.warn('⚠️ 缓存清理失败（不影响删除结果）:', error);
    }
  }

  /**
   * 🎯 广播项目创建事件，实现实时更新
   */
  private broadcastProjectCreated(projectId: string, html: string, prompts: string[]): void {
    try {
      const eventData = {
        type: 'project-created',
        projectId,
        htmlLength: html.length,
        promptsCount: prompts.length,
        timestamp: Date.now()
      };

      // 🔧 方式1：BroadcastChannel（现代浏览器）
      if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
        const channel = new BroadcastChannel('project-updates');
        channel.postMessage(eventData);
        channel.close();
      }

      // 🔧 方式2：localStorage事件（兼容性更好）
      if (typeof window !== 'undefined') {
        localStorage.setItem('project-created-event', JSON.stringify(eventData));
      }

      // 🔧 方式3：自定义事件（确保ProjectSidebar能收到）
      if (typeof window !== 'undefined') {
        const customEvent = new CustomEvent('forceRefreshProjects', {
          detail: eventData
        });
        window.dispatchEvent(customEvent);
      }

      console.log('📡 ProjectManager: 项目创建事件已广播', { projectId, methods: 3 });

    } catch (error) {
      console.error('⚠️ ProjectManager: 广播项目创建事件失败:', error);
    }
  }

  /**
   * 🎯 广播版本更新事件，实现版本菜单实时更新
   */
  private broadcastVersionCreated(projectId: string, versionNumber: number): void {
    try {
      const eventData = {
        type: 'version-created',
        projectId,
        versionNumber,
        timestamp: Date.now()
      };

      console.log('📢 ProjectManager: 广播版本更新事件', eventData);

      // 🔧 方式1：BroadcastChannel（现代浏览器）
      if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
        const channel = new BroadcastChannel('version-updates');
        channel.postMessage(eventData);
        channel.close();
      }

      // 🔧 方式2：localStorage事件（兼容性更好）
      if (typeof window !== 'undefined') {
        localStorage.setItem('version-created-event', JSON.stringify(eventData));
        console.log('📡 ProjectManager: localStorage事件已设置', eventData);
        // 立即清理，避免localStorage污染
        setTimeout(() => {
          localStorage.removeItem('version-created-event');
        }, 100);
      }

      // 🔧 方式3：自定义事件（同页面通信）
      if (typeof window !== 'undefined') {
        const customEvent = new CustomEvent('refreshVersionList', {
          detail: eventData
        });
        window.dispatchEvent(customEvent);
        console.log('📡 ProjectManager: 自定义事件已派发', eventData);
      }

      // 🔧 方式4：定时检查触发器（兜底方案）
      if (typeof window !== 'undefined') {
        localStorage.setItem('version-check-trigger', projectId);
        console.log('📡 ProjectManager: 定时检查触发器已设置', { projectId });
      }

      console.log('✅ ProjectManager: 版本更新事件已广播', { projectId, versionNumber, methods: 3 });

    } catch (error) {
      console.error('⚠️ ProjectManager: 广播版本更新事件失败:', error);
    }
  }

  // 🔧 核心方法：基于第一条用户消息创建项目
  async createProjectFromFirstMessage(firstUserMessage: string): Promise<string | null> {
    try {
      PerformanceLogger.startTimer('create-project-from-message');
      console.log('🚀 ProjectManager: 基于第一条消息创建项目', {
        messageLength: firstUserMessage.length,
        messagePreview: firstUserMessage.substring(0, 50) + '...'
      });
      
      // 🌐 调用API创建项目
      const response = await fetch('/api/me/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          firstMessage: firstUserMessage
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`创建失败: ${response.status} - ${errorText}`);
        }

      const data = await response.json();
      const projectId = data.project?.id || data.projectId || data.id;

      if (!projectId) {
        throw new Error('API返回无效的项目ID');
      }

      console.log('✅ ProjectManager: 项目创建成功', { projectId });

      // 🔧 更新当前项目ID
      this.currentProjectId = projectId.toString();
      
      // 🔧 关键修复：立即设置项目ID到全局状态，确保后续组件能正确获取
      console.log('🔧 ProjectManager: 设置当前项目ID为', this.currentProjectId);

      // 🔧 清除相关缓存，确保新项目数据生效
      await this.cache.clearProjectCaches(parseInt(projectId));

      // 🎯 广播项目创建事件，通知项目列表刷新
      this.broadcastProjectCreated(projectId.toString(), '', [firstUserMessage]);

      PerformanceLogger.endTimer('create-project-from-message');
      return projectId.toString();

    } catch (error) {
      console.error('❌ ProjectManager: 项目创建失败:', error);
      PerformanceLogger.endTimer('create-project-from-message');
      return null;
    }
  }

  // 🎯 专门用于社区项目创建的方法
  async createProjectFromCommunityProject(title: string, htmlContent: string): Promise<string | null> {
    try {
      PerformanceLogger.startTimer('create-community-project');
      console.log('🚀 ProjectManager: 从社区项目创建', {
        title,
        htmlLength: htmlContent.length
      });

      // 🌐 调用API创建社区项目
      const response = await fetch('/api/me/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          firstMessage: title,
          isCommunityProject: true,
          communityHtmlContent: htmlContent
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`创建失败: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const projectId = data.project?.id || data.projectId || data.id;

      if (!projectId) {
        throw new Error('API返回无效的项目ID');
      }

      console.log('✅ ProjectManager: 社区项目创建成功', { projectId });

      // 🔧 更新当前项目ID
      this.currentProjectId = projectId.toString();

      // 🔧 清除相关缓存，确保新项目数据生效
      await this.cache.clearProjectCaches(parseInt(projectId));

      // 🎯 广播项目创建事件，通知项目列表刷新
      this.broadcastProjectCreated(projectId.toString(), title, [title]);

      PerformanceLogger.endTimer('create-community-project');
      return projectId.toString();
    } catch (error) {
      console.error('❌ ProjectManager: 创建社区项目失败', error);
      PerformanceLogger.endTimer('create-community-project');
      return null;
    }
  }
} 