"use client";
import { RefObject } from "react";
import { Preview } from "@/components/editor/preview";
import { Project } from "@/types";

interface ProjectVersion {
  id: string;
  version_number: number;
  title: string;
  prompt: string;
  html_content: string;
  parent_version: number | null;
  is_active: boolean;
  created_at: string;
  metadata: {
    lines_changed: number;
    generation_time: number;
    model_used: string;
    is_modification: boolean;
  };
}

interface PreviewAreaProps {
  // 内容状态
  html: string;
  setHtml: (html: string) => void;
  streamingHtml?: string;

  // AI工作状态
  isAiWorking: boolean;

  // 编辑状态
  isEditableModeEnabled: boolean;
  setIsEditableModeEnabled: (enabled: boolean) => void;
  selectedElement: HTMLElement | null; // 🎯 新增：选中元素状态
  setSelectedElement: (element: HTMLElement | null) => void;

  // 🎯 修复：添加撤销系统
  undoRedoSystem?: {
    canUndo: boolean;
    canRedo: boolean;
    undo: () => string | null;
    redo: () => string | null;
  };

  // 布局状态
  currentTab: string;
  setCurrentTab: (tab: "chat" | "preview") => void;
  device: string;
  setDevice: (device: string) => void;
  isLargeScreen: boolean;
  isClient: boolean;

  // 项目状态
  project?: Project | null;
  currentVersionNumber: number;
  setCurrentVersionNumber: (version: number) => void;

  // 引用
  preview: RefObject<HTMLDivElement | null>;
  iframeRef: RefObject<HTMLIFrameElement | null>;
}

export function PreviewArea({
  html,
  setHtml,
  streamingHtml,
  isAiWorking,
  isEditableModeEnabled,
  setIsEditableModeEnabled,
  selectedElement, // 🎯 新增：接收选中元素状态
  setSelectedElement,
  undoRedoSystem, // 🎯 修复：接收撤销系统
  currentTab,
  setCurrentTab,
  device,
  setDevice,
  isLargeScreen,
  isClient,
  project,
  currentVersionNumber,
  setCurrentVersionNumber,
  preview,
  iframeRef,
}: PreviewAreaProps) {
  
  const handleVersionSelect = (version: ProjectVersion) => {
    // 切换到选中的版本
    setHtml(version.html_content);
    setCurrentVersionNumber(version.version_number);
  };

  const handleClickElement = (element: HTMLElement) => {
    // 🎯 关键修复：先设置选中元素，再禁用编辑模式
    setSelectedElement(element);

    // 🎯 延迟禁用编辑模式，确保选中状态被正确处理
    setTimeout(() => {
      setIsEditableModeEnabled(false);
    }, 50);

    // 移动端：点击元素后自动切换回聊天模式
    if (!isLargeScreen) {
      setCurrentTab("chat");
    }
  };

  const handleDeviceChange = (newDevice: string) => {
    setDevice(newDevice);
  };

  const handleRefresh = () => {
    // 刷新iframe
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  const handleModeToggle = () => {
    setCurrentTab(currentTab === "preview" ? "chat" : "preview");
  };

  const handleHtmlChange = (newHtml: string) => {
    setHtml(newHtml);
    // 可以在这里添加额外的逻辑，比如保存到历史记录
    console.log('代码编辑器更新了HTML内容');
  };

  const handleVersionNumberChange = (versionNumber: number) => {
    // 🎯 关键修复：同步版本号变化到外部状态
    setCurrentVersionNumber(versionNumber);
    console.log('🔄 PreviewArea: 版本号已同步更新', {
      newVersionNumber: versionNumber
    });
  };

  const handleVersionContentUpdate = (versionNumber: number, htmlContent: string) => {
    // 🎯 关键修复：当版本内容更新且是当前预览版本时，更新HTML状态
    if (versionNumber === currentVersionNumber) {
      setHtml(htmlContent);
      console.log('🔄 PreviewArea: 当前预览版本内容已更新', {
        versionNumber,
        htmlLength: htmlContent.length
      });
    }
  };

  return (
    <div 
      className={`transition-all duration-300 ease-in-out ${
        isClient && !isLargeScreen && currentTab === "chat" 
          ? "hidden" 
          : "flex-1 h-full"
      } overflow-hidden`}
      style={{ margin: 0, padding: 0 }}
    >
      <Preview
        html={html}
        streamingHtml={streamingHtml}
        isResizing={false}
        isAiWorking={isAiWorking}
        ref={preview}
        device={device}
        currentTab={currentTab}
        isEditableModeEnabled={isEditableModeEnabled}
        selectedElement={selectedElement} // 🎯 传递选中元素状态
        undoRedoSystem={undoRedoSystem} // 🎯 修复：传递撤销系统
        iframeRef={iframeRef}
        projectId={project?.id?.toString()}
        currentVersionNumber={currentVersionNumber}
        onVersionSelect={handleVersionSelect}
        onVersionNumberChange={handleVersionNumberChange}
        onVersionContentUpdate={handleVersionContentUpdate}
        onClickElement={handleClickElement}
        onDeviceChange={handleDeviceChange}
        onRefresh={handleRefresh}
        onModeToggle={handleModeToggle}
        onHtmlChange={handleHtmlChange}
      />
    </div>
  );
} 