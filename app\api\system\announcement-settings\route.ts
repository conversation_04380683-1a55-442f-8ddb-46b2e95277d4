import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    // 获取公告相关设置
    const settings = await executeQuery(
      `SELECT setting_key, setting_value, setting_type
       FROM system_settings
       WHERE setting_key IN (
         'show_invitation_banner',
         'invitation_enabled',
         'invitation_points_per_user',
         'max_invitations_per_user',
         'activity_points_config'
       ) AND is_active = 1`,
      []
    ) as { setting_key: string; setting_value: string; setting_type: string }[];

    const config: Record<string, any> = {
      show_invitation_banner: true, // 默认显示
      invitation_enabled: false,
      invitation_points_per_user: 100,
      max_invitations_per_user: 10,
      invitation_validity_days: 15 // 默认15天
    };

    let activityPointsConfig: any = null;

    settings.forEach(setting => {
      let value: any = setting.setting_value;

      switch (setting.setting_type) {
        case 'boolean':
          value = setting.setting_value === '1' || setting.setting_value === 'true';
          break;
        case 'number':
          value = parseInt(setting.setting_value) || 0;
          break;
        case 'json':
          try {
            value = JSON.parse(setting.setting_value);
          } catch {
            value = setting.setting_value;
          }
          break;
      }

      if (setting.setting_key === 'activity_points_config') {
        activityPointsConfig = value;
      } else {
        config[setting.setting_key] = value;
      }
    });

    // 从活动积分配置中提取邀请活动的有效期
    if (activityPointsConfig && activityPointsConfig.invitation) {
      config.invitation_validity_days = activityPointsConfig.invitation.validity_days || 15;
    }

    return NextResponse.json(config);
  } catch (error) {
    console.error('获取公告设置失败:', error);
    return NextResponse.json(
      { error: '获取公告设置失败' },
      { status: 500 }
    );
  }
}
