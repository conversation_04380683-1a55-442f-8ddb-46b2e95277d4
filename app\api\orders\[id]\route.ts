import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const orderId = parseInt(id);
    
    if (isNaN(orderId)) {
      return NextResponse.json({ success: false, error: "订单ID无效" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, error: "用户不存在" }, { status: 401 });
    }

    // 先尝试从订阅订单表查找
    const subscriptionResults = await executeQuery(
      `SELECT id, order_no, membership_type, plan_key, duration_months, 
              original_price, discount_price, payment_method, status, 
              paid_at, expires_at, order_expires_at, created_at, updated_at
       FROM membership_orders 
       WHERE id = ? AND user_id = ?`,
      [orderId, user.id]
    ) as any[];

    if (subscriptionResults.length > 0) {
      const order = subscriptionResults[0];
      
      // 获取计划信息
      const planResults = await executeQuery(
        `SELECT points_included FROM subscription_plans WHERE plan_key = ?`,
        [order.plan_key]
      ) as any[];

      const orderData = {
        id: order.id,
        order_no: order.order_no,
        order_type: 'subscription',
        amount: order.discount_price,
        original_price: order.original_price,
        discount_price: order.discount_price,
        status: order.status,
        expires_at: order.order_expires_at,
        created_at: order.created_at,
        membership_type: order.membership_type,
        plan_key: order.plan_key,
        duration_months: order.duration_months,
        points_included: planResults.length > 0 ? planResults[0].points_included : 0,
        payment_method: order.payment_method,
        paid_at: order.paid_at
      };

      return NextResponse.json({
        success: true,
        data: orderData
      });
    }

    // 再尝试从充值订单表查找
    const rechargeResults = await executeQuery(
      `SELECT id, order_no, package_key, points_amount, bonus_points, points_validity_days,
              original_price, discount_price, payment_method, status, 
              paid_at, order_expires_at, created_at, updated_at
       FROM recharge_orders 
       WHERE id = ? AND user_id = ?`,
      [orderId, user.id]
    ) as any[];

    if (rechargeResults.length > 0) {
      const order = rechargeResults[0];
      
      const orderData = {
        id: order.id,
        order_no: order.order_no,
        order_type: 'recharge',
        amount: order.discount_price,
        original_price: order.original_price,
        discount_price: order.discount_price,
        status: order.status,
        expires_at: order.order_expires_at,
        created_at: order.created_at,
        package_key: order.package_key,
        points_amount: order.points_amount,
        bonus_points: order.bonus_points,
        points_validity_days: order.points_validity_days,
        payment_method: order.payment_method,
        paid_at: order.paid_at
      };

      return NextResponse.json({
        success: true,
        data: orderData
      });
    }

    return NextResponse.json({ success: false, error: "订单不存在" }, { status: 404 });

  } catch (error) {
    console.error('获取订单信息失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
