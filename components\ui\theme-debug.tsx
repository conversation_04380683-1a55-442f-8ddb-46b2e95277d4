"use client"

import { useThemeLanguage } from "@/components/providers/theme-language-provider"
import { useEffect, useState } from "react"

export function ThemeDebug() {
  const { theme, resolvedTheme } = useThemeLanguage()
  const [domTheme, setDomTheme] = useState<string>('')
  const [cssVars, setCssVars] = useState<Record<string, string>>({})
  
  useEffect(() => {
    // 检查DOM上的主题类
    const root = document.documentElement
    const classes = root.classList.toString()
    setDomTheme(classes)
    
    // 检查CSS变量
    const computedStyle = getComputedStyle(root)
    setCssVars({
      background: computedStyle.getPropertyValue('--background'),
      foreground: computedStyle.getPropertyValue('--foreground'),
      primary: computedStyle.getPropertyValue('--primary'),
    })
  }, [theme, resolvedTheme])
  
  return (
    <div className="fixed bottom-4 left-4 z-50 p-3 bg-white dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-700 rounded-lg shadow-lg text-xs max-w-xs">
      <div className="font-bold mb-2">🎨 主题调试</div>
      <div>Hook主题: {theme}</div>
      <div>解析主题: {resolvedTheme}</div>
      <div>DOM类: {domTheme}</div>
      <div className="mt-2">
        <div className="font-semibold">CSS变量:</div>
        <div>--background: {cssVars.background}</div>
        <div>--foreground: {cssVars.foreground}</div>
        <div>--primary: {cssVars.primary}</div>
      </div>
      <div className="mt-2 flex gap-2">
        <div className="w-4 h-4 bg-background border border-border rounded" title="background"></div>
        <div className="w-4 h-4 bg-primary border border-border rounded" title="primary"></div>
        <div className="w-4 h-4 bg-secondary border border-border rounded" title="secondary"></div>
        <div className="w-4 h-4 bg-zinc-100 dark:bg-zinc-800 border border-border rounded" title="zinc"></div>
      </div>
    </div>
  )
} 