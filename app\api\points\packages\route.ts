import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';
import { getBooleanSetting } from '@/lib/points-service';

export async function GET(request: NextRequest) {
  try {
    // 检查充值服务是否启用
    const rechargeEnabled = await getBooleanSetting('recharge_service_enabled', false);
    
    if (!rechargeEnabled) {
      return NextResponse.json({
        success: true,
        data: {
          enabled: false,
          packages: []
        }
      });
    }

    // 获取所有激活的积分套餐
    const packages = await executeQuery(
      `SELECT 
        id,
        package_key,
        package_name,
        points_amount,
        original_price,
        discount_price,
        bonus_points,
        description,
        display_order
       FROM points_packages 
       WHERE is_active = 1 
       ORDER BY display_order ASC, points_amount ASC`,
      []
    ) as any[];

    // 格式化套餐数据
    const formattedPackages = packages.map(pkg => ({
      ...pkg,
      // 总积分数（基础积分 + 赠送积分）
      total_points: pkg.points_amount + pkg.bonus_points,
      // 计算节省金额
      savings: pkg.original_price - pkg.discount_price,
      // 计算折扣百分比
      discount_percent: pkg.original_price > 0 ? 
        Math.round((1 - pkg.discount_price / pkg.original_price) * 100) : 0,
      // 计算每积分价格（分）
      price_per_point: pkg.discount_price > 0 ? 
        ((pkg.discount_price * 100) / (pkg.points_amount + pkg.bonus_points)).toFixed(2) : 0,
      // 是否有赠送积分
      has_bonus: pkg.bonus_points > 0
    }));

    return NextResponse.json({
      success: true,
      data: {
        enabled: true,
        packages: formattedPackages
      }
    });

  } catch (error) {
    console.error('获取积分套餐失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
