/**
 * 格式化有效期显示的工具函数
 */

/**
 * 格式化有效期天数为用户友好的显示文本
 * @param days 有效期天数
 * @returns 格式化后的显示文本
 */
export const formatValidityDays = (days: number): string => {
  // 永久有效（999999天或更多）
  if (days >= 999999) {
    return "永久有效";
  }
  
  // 超过365天，显示为年
  if (days >= 365) {
    const years = Math.floor(days / 365);
    const remainingDays = days % 365;
    
    if (remainingDays === 0) {
      return `${years}年`;
    } else if (remainingDays < 30) {
      return `${years}年${remainingDays}天`;
    } else {
      const months = Math.floor(remainingDays / 30);
      return `${years}年${months}个月`;
    }
  }
  
  // 超过30天，显示为月
  if (days >= 30) {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    
    if (remainingDays === 0) {
      return `${months}个月`;
    } else {
      return `${months}个月${remainingDays}天`;
    }
  }
  
  // 少于30天，直接显示天数
  return `${days}天`;
};

/**
 * 格式化有效期显示（带颜色样式信息，支持深色模式）
 * @param days 有效期天数
 * @returns 包含文本和样式类的对象
 */
export const formatValidityWithStyle = (days: number): { text: string; colorClass: string } => {
  const text = formatValidityDays(days);

  // 永久有效 - 绿色
  if (days >= 999999) {
    return { text, colorClass: "text-green-600 dark:text-green-400" };
  }

  // 超过1年 - 蓝色
  if (days >= 365) {
    return { text, colorClass: "text-blue-600 dark:text-blue-400" };
  }

  // 超过3个月 - 紫色
  if (days >= 90) {
    return { text, colorClass: "text-purple-600 dark:text-purple-400" };
  }

  // 超过1个月 - 橙色
  if (days >= 30) {
    return { text, colorClass: "text-orange-600 dark:text-orange-400" };
  }

  // 少于1个月 - 红色（提醒较短）
  return { text, colorClass: "text-red-600 dark:text-red-400" };
};

/**
 * 检查是否为永久有效
 * @param days 有效期天数
 * @returns 是否永久有效
 */
export const isPermanentValidity = (days: number): boolean => {
  return days >= 999999;
};

/**
 * 获取有效期的描述文本
 * @param days 有效期天数
 * @returns 描述文本
 */
export const getValidityDescription = (days: number): string => {
  if (days >= 999999) {
    return "积分永久有效，无过期时间";
  }
  
  if (days >= 365) {
    return "长期有效积分";
  }
  
  if (days >= 90) {
    return "中期有效积分";
  }
  
  if (days >= 30) {
    return "短期有效积分";
  }
  
  return "临时有效积分";
};

/**
 * 格式化管理后台显示的有效期选项
 * @param days 有效期天数
 * @returns 管理后台显示文本
 */
export const formatAdminValidityOption = (days: number): string => {
  if (days >= 999999) {
    return "永久";
  }
  
  if (days >= 365) {
    const years = Math.floor(days / 365);
    return `${years}年`;
  }
  
  return `${days}天`;
};
