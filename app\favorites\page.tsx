"use client";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { CommunityCard } from "@/components/community/community-card";
import { useFavorites } from "@/loomrunhooks/useFavorites";
import { Header } from "@/components/editor/header";
import { Loader2, RefreshCw, Heart } from "lucide-react";

interface FavoriteProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  favoritedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount: number;
  isFavorited: boolean;
}

// CommunityCard 期望的项目类型
interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount?: number;
  isFavorited?: boolean;
}

export default function FavoritesPage() {
  const router = useRouter();
  const { favorites, isLoading, error, fetchFavorites, toggleFavorite, clearFavorites } = useFavorites();
  const [currentOffset, setCurrentOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // 预览和打开项目的状态
  const [previewProject, setPreviewProject] = useState<FavoriteProject | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // 初始加载收藏列表
  useEffect(() => {
    loadFavorites(true);
  }, []);

  // 加载收藏列表
  const loadFavorites = useCallback(async (isInitial = false) => {
    try {
      if (isInitial) {
        clearFavorites();
        setCurrentOffset(0);
      }

      const result = await fetchFavorites(20, currentOffset);
      setHasMore(result.pagination.hasMore);
      setCurrentOffset(prev => prev + result.favorites.length);
    } catch (error) {
      console.error('加载收藏列表失败:', error);
    }
  }, [currentOffset, fetchFavorites, clearFavorites]);

  // 加载更多
  const handleLoadMore = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;
    
    setIsLoadingMore(true);
    try {
      const result = await fetchFavorites(20, currentOffset);
      setHasMore(result.pagination.hasMore);
      setCurrentOffset(prev => prev + result.favorites.length);
    } catch (error) {
      console.error('加载更多失败:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [currentOffset, isLoadingMore, hasMore, fetchFavorites]);

  // 处理收藏操作
  const handleToggleFavorite = useCallback(async (projectId: number, isFavorited: boolean) => {
    try {
      const result = await toggleFavorite(projectId, isFavorited);
      return result;
    } catch (error) {
      console.error('收藏操作失败:', error);
      throw error;
    }
  }, [toggleFavorite]);

  // 预览项目
  const handlePreview = useCallback((project: CommunityProject) => {
    // 查找对应的完整项目信息
    const fullProject = favorites.find(fav => fav.id === project.id);
    if (fullProject) {
      setPreviewProject(fullProject);
      setIsPreviewMode(true);
    }
  }, [favorites]);

  // 打开项目
  const handleOpen = useCallback((project: CommunityProject) => {
    const fullProject = favorites.find(fav => fav.id === project.id);
    if (fullProject) {
      // 在新窗口中打开项目
      const newWindow = window.open("", "_blank");
      if (newWindow) {
        newWindow.document.write(fullProject.htmlContent);
        newWindow.document.title = fullProject.title || "LoomRun Project";
      }
    }
  }, [favorites]);

  // 关闭预览
  const handleClosePreview = useCallback(() => {
    setIsPreviewMode(false);
    setPreviewProject(null);
  }, []);

  // Header Logo点击处理
  const handleLogoClick = useCallback(() => {
    router.push('/projects/new');
  }, [router]);

  // 优化的刷新处理函数
  const handleRefresh = useCallback(async () => {
    if (isLoading) return; // 防止重复请求
    
    try {
      setCurrentOffset(0);
      await loadFavorites(true);
    } catch (error) {
      console.error('刷新收藏列表失败:', error);
    }
  }, [isLoading, loadFavorites]);

  // 渲染加载状态
  const renderLoadingGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: 6 }, (_, index) => (
        <div key={`loading-${index}`} className="bg-neutral-800 rounded-lg aspect-video animate-pulse">
          <div className="w-full h-full bg-neutral-700 rounded-lg flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-neutral-600 border-t-neutral-400 rounded-full animate-spin"></div>
          </div>
        </div>
      ))}
    </div>
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="text-center py-16 text-neutral-500">
      <div className="w-20 h-20 mx-auto mb-6 opacity-40">
        <Heart className="w-full h-full" />
      </div>
      <h3 className="text-xl font-medium mb-3">暂无收藏项目</h3>
      <p className="text-sm text-neutral-600 mb-6">您还没有收藏任何项目，快去社区探索吧！</p>
      <Button
        onClick={() => router.push('/projects')}
        className="bg-blue-600 hover:bg-blue-700 text-white"
      >
        探索社区项目
      </Button>
    </div>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <div className="text-center py-16 text-neutral-500">
      <div className="w-20 h-20 mx-auto mb-6 opacity-40">
        <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 className="text-xl font-medium mb-3">加载失败</h3>
      <p className="text-sm text-neutral-600 mb-6">{error}</p>
      <Button
        onClick={() => loadFavorites(true)}
        variant="outline"
        className="bg-neutral-800 border-neutral-600 text-white hover:bg-neutral-700"
      >
        <RefreshCw className="w-4 h-4 mr-2" />
        重试
      </Button>
    </div>
  );

  return (
    <div className="h-screen bg-neutral-950 flex flex-col">
      {/* 系统顶部栏 - 使用Header组件 */}
      <Header onLogoClick={handleLogoClick} />

      {/* 收藏项目页面标题栏 - 进一步缩小高度 */}
      <div className="bg-neutral-900/95 backdrop-blur-sm border-b border-neutral-800 flex-shrink-0">
        <div className="flex items-center justify-between px-4 py-1.5">
          <div className="flex items-center gap-2.5">
            <Button
              onClick={() => router.back()}
              variant="ghost"
              size="sm"
              className="text-neutral-400 hover:text-white hover:bg-neutral-800/50 p-1 transition-all duration-200"
              title="返回上一页"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </Button>
            <Heart className="w-4 h-4 text-red-500" />
            <span className="text-sm font-medium text-white">
              收藏项目 ({favorites.length})
            </span>
          </div>
          
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="ghost"
            size="sm"
            className="text-neutral-400 hover:text-white hover:bg-neutral-800/50 p-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="刷新收藏列表"
          >
            <RefreshCw className={`w-3.5 h-3.5 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* 主要内容区域 - 修复滚动 */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-6 py-6">
          {isLoading && favorites.length === 0 ? (
            renderLoadingGrid()
          ) : error && favorites.length === 0 ? (
            renderErrorState()
          ) : favorites.length === 0 ? (
            renderEmptyState()
          ) : (
            <div className="space-y-6">
              {/* 项目卡片网格 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {favorites.map((project) => {
                  // 转换数据格式以匹配CommunityCard期望的接口
                  const communityProject: CommunityProject = {
                    id: project.id,
                    originalProjectId: project.originalProjectId,
                    userId: project.userId,
                    title: project.title,
                    htmlContent: project.htmlContent,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    author: {
                      name: project.author.name,
                      email: project.author.email || project.author.name + '@example.com',
                      avatar_url: project.author.avatar_url
                    },
                    favoritesCount: project.favoritesCount,
                    isFavorited: project.isFavorited
                  };

                  return (
                    <CommunityCard
                      key={project.id}
                      project={communityProject}
                      onPreview={handlePreview}
                      onOpen={handleOpen}
                      onToggleFavorite={handleToggleFavorite}
                    />
                  );
                })}
              </div>

              {/* 加载更多按钮 */}
              {hasMore && (
                <div className="flex justify-center pt-4">
                  <Button
                    onClick={handleLoadMore}
                    disabled={isLoadingMore}
                    className="bg-neutral-800 hover:bg-neutral-700 text-white border border-neutral-600 px-8 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        加载中...
                      </>
                    ) : (
                      '加载更多'
                    )}
                  </Button>
                </div>
              )}

              {/* 已加载完全部内容提示 */}
              {!hasMore && favorites.length > 0 && (
                <div className="text-center py-8 text-neutral-500">
                  <p className="text-sm">
                    已显示全部 {favorites.length} 个收藏项目
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 预览模态框 */}
      {isPreviewMode && previewProject && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="bg-neutral-900 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">项目预览</h3>
              <Button
                onClick={handleClosePreview}
                variant="ghost"
                size="sm"
                className="text-neutral-400 hover:text-white"
              >
                ✕
              </Button>
            </div>
            <div className="aspect-video bg-neutral-950 rounded-lg overflow-hidden">
              <iframe
                srcDoc={previewProject.htmlContent}
                className="w-full h-full"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <img 
                  src={previewProject.author.avatar_url || '/default-avatar.png'}
                  alt={previewProject.author.name}
                  className="w-8 h-8 rounded-full"
                />
                <span className="text-white">{previewProject.author.name}</span>
              </div>
              <Button
                onClick={() => handleOpen({
                  id: previewProject.id,
                  originalProjectId: previewProject.originalProjectId,
                  userId: previewProject.userId,
                  title: previewProject.title,
                  htmlContent: previewProject.htmlContent,
                  createdAt: previewProject.createdAt,
                  updatedAt: previewProject.updatedAt,
                  author: {
                    name: previewProject.author.name,
                    email: previewProject.author.email || '',
                    avatar_url: previewProject.author.avatar_url
                  },
                  favoritesCount: previewProject.favoritesCount,
                  isFavorited: previewProject.isFavorited
                })}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                打开项目
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 