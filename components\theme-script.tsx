export function ThemeScript() {
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          (function() {
            // 防止水合错误：只在客户端运行，且推迟到下一个tick
            if (typeof window === 'undefined') return;
            
            function applyTheme(theme) {
              const root = document.documentElement;
              const body = document.body;
              
              // 移除所有主题类
              root.classList.remove('light', 'dark');
              
              // 添加新主题类
              root.classList.add(theme);
              
              // 更新body背景色
              if (theme === 'dark') {
                body.style.background = 'linear-gradient(135deg, #0a0a0f 0%, #111118 25%, #0d0d14 50%, #161620 75%, #0f0f16 100%)';
              } else {
                body.style.background = 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 25%, #ffffff 50%, #f8f8f8 75%, #fafafa 100%)';
              }
              
              // 更新meta标签（移动端状态栏）
              const metaTheme = document.querySelector('meta[name="theme-color"]');
              if (metaTheme) {
                metaTheme.setAttribute('content', theme === 'dark' ? '#0a0a0f' : '#ffffff');
              }
            }
            
            // 推迟到下一个tick执行，避免水合冲突
            setTimeout(() => {
              try {
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
                  applyTheme(savedTheme);
                } else {
                  // 默认使用深色主题
                  applyTheme('dark');
                  localStorage.setItem('theme', 'dark');
                  
                  // 同时设置到 loomrun_settings
                  const settingsKey = 'loomrun_settings';
                  try {
                    const currentSettings = localStorage.getItem(settingsKey);
                    if (currentSettings) {
                      const settings = JSON.parse(currentSettings);
                      settings.theme = 'dark';
                      localStorage.setItem(settingsKey, JSON.stringify(settings));
                    } else {
                      localStorage.setItem(settingsKey, JSON.stringify({
                        dataOptimization: true,
                        language: 'zh',
                        theme: 'dark'
                      }));
                    }
                  } catch (settingsError) {
                    console.warn('Failed to set default theme in settings:', settingsError);
                  }
                }
              } catch (error) {
                console.warn('Theme initialization failed:', error);
                // 出错时使用深色主题
                applyTheme('dark');
              }
            }, 0);
          })();
        `,
      }}
    />
  );
} 