import { NextRequest, NextResponse } from 'next/server';
import { expireOldOrders } from '@/lib/order-service';

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源（可以添加密钥验证）
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET_TOKEN || 'your-secret-token';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      );
    }

    console.log('🕒 定时任务开始: 处理过期订单');
    
    const result = await expireOldOrders();
    
    console.log('✅ 定时任务完成: 处理过期订单', result);
    
    return NextResponse.json({
      success: true,
      message: '过期订单处理完成',
      data: result
    });

  } catch (error) {
    console.error('❌ 定时任务失败: 处理过期订单', error);
    return NextResponse.json(
      { success: false, message: '定时任务执行失败', error: error.message },
      { status: 500 }
    );
  }
}

// 支持 POST 请求（某些定时任务服务可能使用 POST）
export async function POST(request: NextRequest) {
  return GET(request);
}
