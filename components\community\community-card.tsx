"use client";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Eye, ExternalLink, Bookmark, BookmarkCheck, Code, FileText } from "lucide-react";
import { formatNumber } from "@/lib/utils";
import { useUser } from "@/loomrunhooks/useUser";

interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount?: number;
  isFavorited?: boolean;
  // 新增预览图相关字段
  previewImageUrl?: string;
  previewImageType?: 'auto' | 'manual' | 'upload' | 'static';
  previewMetadata?: any;
  customTitle?: string;
}

interface CommunityCardProps {
  project: CommunityProject;
  onPreview: (project: CommunityProject) => void;
  onOpen: (project: CommunityProject) => void;
  onToggleFavorite?: (projectId: number, isFavorited: boolean) => Promise<any>;
}

// 🎯 生成项目预览图的简化函数
const generatePreviewThumbnail = (htmlContent: string): string => {
  // 🎨 基于HTML内容生成一个美观的预览缩略图
  const doc = new DOMParser().parseFromString(htmlContent, 'text/html');
  
  // 提取关键信息
  const title = doc.title || doc.querySelector('h1, h2, h3')?.textContent || '';
  const hasImages = doc.querySelectorAll('img').length > 0;
  const hasButtons = doc.querySelectorAll('button, .btn, [role="button"]').length > 0;
  const hasCards = doc.querySelectorAll('.card, [class*="card"]').length > 0;
  const hasDashboard = htmlContent.toLowerCase().includes('dashboard') || 
                      htmlContent.toLowerCase().includes('chart') ||
                      htmlContent.toLowerCase().includes('data');
  const hasForm = doc.querySelectorAll('form, input, textarea').length > 0;
  
  // 检测主题色彩（从Tailwind类中提取）
  const colorClasses = htmlContent.match(/(?:bg-|text-|border-)(red|blue|green|yellow|purple|pink|indigo|gray|cyan|emerald|orange|teal|lime|amber|violet|fuchsia|rose|slate|stone|neutral|zinc|sky|pink)-\d+/g) || [];
  const primaryColor = colorClasses.length > 0 ? (colorClasses[0]?.split('-')[1] || 'blue') : 'blue';
  
  // 🎨 根据内容类型生成不同的预览图
  let previewType = 'default';
  if (hasDashboard) previewType = 'dashboard';
  else if (hasForm) previewType = 'form';
  else if (hasCards) previewType = 'cards';
  else if (hasImages) previewType = 'gallery';
  
  // 🌈 颜色映射
  const colorMap: Record<string, string> = {
    blue: 'from-blue-400 to-blue-600',
    red: 'from-red-400 to-red-600',
    green: 'from-green-400 to-green-600',
    purple: 'from-purple-400 to-purple-600',
    pink: 'from-pink-400 to-pink-600',
    yellow: 'from-yellow-400 to-orange-500',
    indigo: 'from-indigo-400 to-indigo-600',
    cyan: 'from-cyan-400 to-cyan-600',
    emerald: 'from-emerald-400 to-emerald-600',
    orange: 'from-orange-400 to-orange-600',
    teal: 'from-teal-400 to-teal-600'
  };
  
  const gradientClass = colorMap[primaryColor] || 'from-blue-400 to-blue-600';
  
  return JSON.stringify({
    type: previewType,
    title: title.slice(0, 20),
    hasImages,
    hasButtons,
    hasCards,
    hasDashboard,
    hasForm,
    primaryColor,
    gradientClass,
    contentLength: htmlContent.length
  });
};

// 🎯 静态预览图组件
const StaticPreview = ({ project, className }: { project: CommunityProject; className?: string }) => {
  const previewData = useMemo(() => {
    try {
      return JSON.parse(generatePreviewThumbnail(project.htmlContent));
    } catch {
      return {
        type: 'default',
        title: project.title.slice(0, 20),
        hasImages: false,
        hasButtons: false,
        hasCards: false,
        hasDashboard: false,
        hasForm: false,
        primaryColor: 'blue',
        gradientClass: 'from-blue-400 to-blue-600',
        contentLength: project.htmlContent.length
      };
    }
  }, [project.htmlContent, project.title]);

  // 🎨 根据类型渲染不同的预览图
  const renderPreviewContent = () => {
    const { type, gradientClass, hasButtons } = previewData;
    
    const baseClasses = `w-full h-full bg-gradient-to-br ${gradientClass} relative overflow-hidden`;
    
    switch (type) {
      case 'dashboard':
        return (
          <div className={baseClasses}>
            {/* 仪表板预览 */}
            <div className="absolute inset-0 p-4 opacity-90">
              <div className="w-full h-6 bg-white/20 rounded mb-3"></div>
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className="h-8 bg-white/15 rounded"></div>
                <div className="h-8 bg-white/15 rounded"></div>
                <div className="h-8 bg-white/15 rounded"></div>
              </div>
              <div className="h-16 bg-white/10 rounded mb-2"></div>
              <div className="h-12 bg-white/10 rounded"></div>
            </div>
            <div className="absolute top-2 right-2">
              <div className="w-2 h-2 bg-white/40 rounded-full"></div>
            </div>
          </div>
        );
        
      case 'form':
        return (
          <div className={baseClasses}>
            {/* 表单预览 */}
            <div className="absolute inset-0 p-4 opacity-90">
              <div className="w-3/4 h-4 bg-white/20 rounded mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-white/15 rounded w-full"></div>
                <div className="h-3 bg-white/15 rounded w-5/6"></div>
                <div className="h-3 bg-white/15 rounded w-4/6"></div>
              </div>
              <div className="mt-4 w-20 h-6 bg-white/25 rounded"></div>
            </div>
            <FileText className="absolute bottom-2 right-2 w-4 h-4 text-white/40" />
          </div>
        );
        
      case 'cards':
        return (
          <div className={baseClasses}>
            {/* 卡片布局预览 */}
            <div className="absolute inset-0 p-3 opacity-90">
              <div className="grid grid-cols-2 gap-2 h-full">
                <div className="bg-white/15 rounded p-2">
                  <div className="w-full h-2 bg-white/20 rounded mb-1"></div>
                  <div className="w-3/4 h-2 bg-white/15 rounded"></div>
                </div>
                <div className="bg-white/15 rounded p-2">
                  <div className="w-full h-2 bg-white/20 rounded mb-1"></div>
                  <div className="w-3/4 h-2 bg-white/15 rounded"></div>
                </div>
                <div className="bg-white/15 rounded p-2">
                  <div className="w-full h-2 bg-white/20 rounded mb-1"></div>
                  <div className="w-3/4 h-2 bg-white/15 rounded"></div>
                </div>
                <div className="bg-white/15 rounded p-2">
                  <div className="w-full h-2 bg-white/20 rounded mb-1"></div>
                  <div className="w-3/4 h-2 bg-white/15 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'gallery':
        return (
          <div className={baseClasses}>
            {/* 图片画廊预览 */}
            <div className="absolute inset-0 p-2 opacity-90">
              <div className="grid grid-cols-3 gap-1 h-full">
                <div className="bg-white/20 rounded"></div>
                <div className="bg-white/15 rounded"></div>
                <div className="bg-white/20 rounded"></div>
                <div className="bg-white/15 rounded"></div>
                <div className="bg-white/20 rounded"></div>
                <div className="bg-white/15 rounded"></div>
              </div>
            </div>
            <Eye className="absolute bottom-2 right-2 w-4 h-4 text-white/40" />
          </div>
        );
        
      default:
        return (
          <div className={baseClasses}>
            {/* 默认预览 */}
            <div className="absolute inset-0 p-4 opacity-90">
              <div className="w-2/3 h-5 bg-white/20 rounded mb-3"></div>
              <div className="space-y-2">
                <div className="h-2 bg-white/15 rounded w-full"></div>
                <div className="h-2 bg-white/15 rounded w-5/6"></div>
                <div className="h-2 bg-white/15 rounded w-4/6"></div>
                <div className="h-2 bg-white/15 rounded w-3/6"></div>
              </div>
              {hasButtons && (
                <div className="mt-3 flex gap-1">
                  <div className="w-8 h-4 bg-white/25 rounded"></div>
                  <div className="w-8 h-4 bg-white/20 rounded"></div>
                </div>
              )}
            </div>
            <Code className="absolute bottom-2 right-2 w-4 h-4 text-white/40" />
          </div>
        );
    }
  };

  return (
    <div className={className}>
      {renderPreviewContent()}
    </div>
  );
};

// 🎯 记忆化的作者头像组件
const AuthorAvatar = ({ author }: { author: CommunityProject['author'] }) => {
  const avatarContent = useMemo(() => {
    if (author.avatar_url) {
      return (
        <img 
          src={author.avatar_url} 
          alt={author.name}
          className="w-full h-full object-cover"
          loading="lazy"
          onError={(e) => {
            // 图片加载失败时显示默认头像
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.nextElementSibling?.classList.remove('hidden');
          }}
        />
      );
    }
    return null;
  }, [author.avatar_url, author.name]);

  return (
    <div className="w-6 h-6 rounded-full bg-muted overflow-hidden flex-shrink-0">
      {avatarContent}
      <div className={`w-full h-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-sm font-medium text-white ${author.avatar_url ? 'hidden' : ''}`}>
        {author.name.charAt(0).toUpperCase()}
      </div>
    </div>
  );
};

export function CommunityCard({ project, onPreview, onOpen, onToggleFavorite }: CommunityCardProps) {
  const { user } = useUser();
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorited, setIsFavorited] = useState(project.isFavorited || false);
  const [favoritesCount, setFavoritesCount] = useState(project.favoritesCount || 0);
  const [isToggling, setIsToggling] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  
  const cardRef = useRef<HTMLDivElement>(null);

  // 🎯 Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { 
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // 🎯 优化的收藏处理
  const handleToggleFavorite = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isToggling || !onToggleFavorite) return;

    // 🔐 检查用户登录状态
    if (!user) {
      console.log('⚠️ CommunityCard: 用户未登录，显示登录弹窗');
      // 触发登录弹窗
      window.dispatchEvent(new CustomEvent('show-login-modal'));
      return;
    }

    setIsToggling(true);
    const newIsFavorited = !isFavorited;

    // 🔄 乐观更新
    setIsFavorited(newIsFavorited);
    setFavoritesCount(prev => newIsFavorited ? prev + 1 : Math.max(0, prev - 1));

    try {
      await onToggleFavorite(project.id, newIsFavorited);
    } catch (error) {
      // 🔄 失败时回滚
      setIsFavorited(!newIsFavorited);
      setFavoritesCount(prev => newIsFavorited ? Math.max(0, prev - 1) : prev + 1);
      console.error('收藏操作失败:', error);
    } finally {
      setIsToggling(false);
    }
  }, [isToggling, isFavorited, onToggleFavorite, project.id, user]);

  // 🎯 预览处理
  const handlePreview = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview(project);
  }, [onPreview, project]);

  // 🎯 打开处理
  const handleOpen = useCallback(() => {
    onOpen(project);
  }, [onOpen, project]);

  // 🎯 记忆化的操作按钮 - 专业设计
  const actionButtons = useMemo(() => (
    <div
      className={`absolute inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center gap-4 transition-all duration-300 ${
        isHovered ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
    >
      <Button
        onClick={handlePreview}
        size="sm"
        variant="secondary"
        className="bg-white/20 hover:bg-white/30 text-white border-white/20 backdrop-blur-sm flex items-center gap-2 px-4 py-2 rounded-lg font-medium"
      >
        <Eye className="w-4 h-4" />
        预览
      </Button>
      <Button
        onClick={handleOpen}
        size="sm"
        className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-lg font-medium shadow-lg"
      >
        <ExternalLink className="w-4 h-4" />
        打开
      </Button>
    </div>
  ), [isHovered, handlePreview, handleOpen]);

  // 🎯 记忆化的收藏按钮 - 紧凑设计
  const favoriteButton = useMemo(() => (
    <Button
      onClick={handleToggleFavorite}
      variant="ghost"
      size="sm"
      disabled={isToggling}
      className={`flex items-center gap-1 transition-all duration-200 h-6 px-2 ${
        isFavorited
          ? 'text-orange-500 hover:text-orange-600'
          : 'text-muted-foreground hover:text-foreground'
      }`}
    >
      {isFavorited ? (
        <BookmarkCheck className="w-3 h-3" />
      ) : (
        <Bookmark className="w-3 h-3" />
      )}
      <span className="text-xs">
        {formatNumber(favoritesCount)}
      </span>
    </Button>
  ), [handleToggleFavorite, isToggling, isFavorited, favoritesCount]);

  return (
    <div
      ref={cardRef}
      className="community-card relative bg-card rounded-xl border border-border overflow-hidden hover:border-ring hover:shadow-lg transition-all duration-300 group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 🎯 项目预览区域 - 微调纵向高度 */}
      <div
        className="bg-muted relative overflow-hidden"
        style={{
          aspectRatio: '16/8.8' // 稍微减少纵向高度
        }}
      >
        {/* 加载占位符 */}
        {!isVisible && (
          <div className="absolute inset-0 bg-muted flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin opacity-50" />
          </div>
        )}
        
        {/* 🖼️ 真实预览图优先，静态预览图作为备选 */}
        {isVisible && (
          <>
            {project.previewImageUrl ? (
              // 使用真实的预览图
              <div className="absolute inset-0 w-full h-full">
                <img
                  src={project.previewImageUrl}
                  alt={project.title}
                  className="w-full h-full object-cover transition-opacity duration-300"
                  loading="lazy"
                  onError={(e) => {
                    // 图片加载失败时隐藏并显示静态预览
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) {
                      fallback.style.display = 'block';
                    }
                  }}
                />

                {/* 静态预览图作为备选 */}
                <div style={{ display: 'none' }}>
                  <StaticPreview 
                    project={project} 
                    className="w-full h-full" 
                  />
                </div>
              </div>
            ) : (
              // 使用静态生成的预览图
              <StaticPreview 
                project={project} 
                className="absolute inset-0 w-full h-full transition-opacity duration-300 opacity-100" 
              />
            )}
          </>
        )}
        
        {/* 操作按钮覆盖层 */}
        {actionButtons}
      </div>

      {/* 🎯 项目信息区域 - 水平布局减少纵向高度，再次微调 */}
      <div className="px-4" style={{ paddingTop: '6px', paddingBottom: '6px' }}>
        {/* 单行布局：头像 + 项目名称 + 收藏按钮 */}
        <div className="flex items-center justify-between gap-3">
          {/* 左侧：头像 + 项目名称 */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <AuthorAvatar author={project.author} />
            <h3 className="font-semibold text-foreground truncate text-sm">
              {project.title}
            </h3>
          </div>

          {/* 右侧：收藏按钮 */}
          <div onClick={(e) => e.stopPropagation()}>
            {favoriteButton}
          </div>
        </div>
      </div>
    </div>
  );
} 