import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

export async function POST(request: NextRequest) {
  try {
    const { htmlContent, projectName } = await request.json();

    if (!htmlContent || !projectName) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 清理项目名称
    const cleanProjectName = projectName
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50);

        const finalProjectName = cleanProjectName.startsWith('loomrun-')
      ? cleanProjectName
      : `loomrun-${cleanProjectName}`;

    console.log('开始 Surge.sh 部署...');

    // 检查 DNS 是否已配置（暂时禁用自定义域名，直到 DNS 配置完成）
    const customDomain = process.env.CUSTOM_DOMAIN;
    const enableCustomDomain = process.env.ENABLE_CUSTOM_DOMAIN === 'true';
    
    // 生成唯一域名（使用随机字母替代时间戳）
    
    let domain: string;
    
    // SSL 证书考虑：Surge.sh 自定义域名 SSL 配置复杂，优先使用默认域名确保 HTTPS 正常
    const useCustomDomain = enableCustomDomain && customDomain && process.env.FORCE_CUSTOM_DOMAIN === 'true';
    
    // 生成随机字母组合确保域名唯一性
    function generateRandomLetters(length: number): string {
      const letters = 'abcdefghijklmnopqrstuvwxyz';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += letters.charAt(Math.floor(Math.random() * letters.length));
      }
      return result;
    }

    if (useCustomDomain) {
      // 使用自定义域名格式：loomrun-项目名-随机字母
      const cleanName = finalProjectName.replace('loomrun-', '');
      const randomSuffix = generateRandomLetters(2);
      domain = `loomrun-${cleanName}-${randomSuffix}.${customDomain}`;
      console.log('使用 LoomRun 专属域名:', domain);
    } else {
      // 使用默认的 surge.sh 域名
              const cleanName = finalProjectName.replace('loomrun-', '');
        const randomSuffix = generateRandomLetters(2);
        domain = `loomrun-${cleanName}-${randomSuffix}.surge.sh`;
        console.log('使用 LoomRun 域名:', domain);
    }

    // 直接使用 Surge.sh 部署
    const surgeResult = await deployToSurge(htmlContent, finalProjectName, domain) as { success: boolean; url: string };
    
    return NextResponse.json({
      success: true,
      url: surgeResult.url,
      provider: 'surge',
      message: '部署到 Surge.sh 成功！国内访问友好',
      features: [
        '✅ 全球 CDN 加速，访问速度快',
        '✅ 支持 HTTPS 安全访问',
        '✅ 国内访问相对稳定',
        '✅ 支持自定义域名',
        '✅ 完全免费使用'
      ],
      chinaAccess: {
        accessible: true,
        note: '通过浏览器访问速度良好，但微信内可能被限制访问'
      },
      wechatSharing: {
        hasCustomDomain: domain.includes('walleyxai.top'),
        domain: domain.includes('walleyxai.top') ? 'walleyxai.top' : 'surge.sh',
        sslWarning: domain.includes('walleyxai.top') ? {
          issue: 'SSL 证书警告',
          description: '自定义域名可能显示"连接不是私密连接"',
          solution: '点击"高级"→"继续前往网站"，或使用默认域名'
        } : null,
        solutions: domain.includes('walleyxai.top') ? [
          {
            title: '✅ 自定义域名已启用',
            desc: '域名可访问，但需手动信任 SSL 证书',
            cost: '免费',
            steps: [
              '✅ DNS 解析正常，域名可访问',
              '⚠️ 遇到 SSL 警告时，点击"高级"→"继续前往"',
              '🔒 或者使用默认 .surge.sh 域名（自动 HTTPS）',
              '🎉 微信分享完全正常，无屏蔽风险！'
            ]
          },
          {
            title: '🔒 使用安全的默认域名',
            desc: '如不想看到 SSL 警告，可使用默认域名',
            cost: '免费',
            steps: [
              '默认域名自动配置 HTTPS 证书',
              '无需手动信任，直接安全访问',
              '微信分享可能被限制（surge.sh 域名）'
            ]
          }
        ] : [
          {
            title: '🎯 启用自定义域名 walleyxai.top',
            desc: '你的 DNS 已配置，可启用自定义域名',
            cost: '免费（域名已有）',
            steps: [
              '设置环境变量：FORCE_CUSTOM_DOMAIN=true',
              '重新部署即可使用自定义域名',
              '注意：需要手动信任 SSL 证书'
            ]
          },
          {
            title: '🔒 当前使用安全域名',
            desc: '默认 surge.sh 域名，自动 HTTPS，无 SSL 问题',
            cost: '免费',
            steps: [
              '✅ 自动 HTTPS 证书，安全访问',
              '✅ 无需手动信任证书',
              '⚠️ 微信分享可能被限制'
            ]
          }
        ]
      },
      nextSteps: [
        '🌐 立即在浏览器中访问您的网站',
        '📱 测试在不同设备上的访问效果',
        '🔗 如需微信分享，建议配置自定义域名',
        '📊 可以添加网站统计代码跟踪访问量',
        '🔄 支持随时重新部署更新内容'
      ]
    });

  } catch (error: unknown) {
    console.error('Surge.sh 部署失败:', error);
    
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    
    return NextResponse.json({
      success: false,
      error: 'Surge.sh 部署失败',
      details: errorMessage,
      manualSolution: {
        title: 'Surge.sh 手动部署方案',
        description: '如果自动部署失败，请按以下步骤手动部署：',
        steps: [
          '1. 全局安装 Surge: npm install -g surge',
          '2. 创建一个文件夹，保存 HTML 内容为 index.html',
          '3. 在文件夹中运行: surge',
          '4. 按提示输入域名（如：your-project.surge.sh）',
          '5. 部署完成后会显示网站链接'
        ],
        tips: [
          '• Surge.sh 支持自定义域名',
          '• 国内访问速度良好',
          '• 完全免费使用',
          '• 支持 HTTPS'
        ],
        url: 'https://surge.sh/help/getting-started-with-surge'
      }
    }, { status: 500 });
  }
}

// Surge.sh 部署函数
async function deployToSurge(htmlContent: string, projectName: string, domain: string) {
  const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'surge-deploy-'));
  const htmlFile = path.join(tempDir, 'index.html');
  
  try {
    // 写入 HTML 文件
    fs.writeFileSync(htmlFile, htmlContent, 'utf8');
    
    // 创建 CNAME 文件（用于自定义域名）
    if (domain.includes('walleyxai.top')) {
      const cnameFile = path.join(tempDir, 'CNAME');
      fs.writeFileSync(cnameFile, domain, 'utf8');
    }
    
    // 创建 _headers 文件（强制 HTTPS）
    const headersFile = path.join(tempDir, '_headers');
    const headersContent = `/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=()`;
    fs.writeFileSync(headersFile, headersContent, 'utf8');
    
    console.log('准备部署到域名:', domain);
    
    // 首先尝试使用预设的 Surge 账户
          const surgeEmail = process.env.SURGE_EMAIL || '<EMAIL>';
      const surgePassword = process.env.SURGE_PASSWORD || 'loomrun123456';
    
    // 使用 spawn 来处理 Surge 部署
    return new Promise((resolve, reject) => {
      const surgeProcess = spawn('npx', ['surge', tempDir, domain], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        env: { ...process.env }
      });
      
      let stdout = '';
      let stderr = '';
      let step = 0; // 0: 等待邮箱, 1: 等待密码, 2: 部署中
      
      surgeProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        stdout += output;
        console.log('Surge 输出:', output);
        
        // 根据输出内容判断当前步骤
        if (output.includes('email:') && step === 0) {
          console.log('输入邮箱:', surgeEmail);
          surgeProcess.stdin.write(surgeEmail + '\n');
          step = 1;
        } else if (output.includes('password:') && step === 1) {
          console.log('输入密码');
          surgeProcess.stdin.write(surgePassword + '\n');
          step = 2;
        } else if (output.includes('Success!') || output.includes('project uploaded')) {
          console.log('部署成功！');
          setTimeout(() => {
            resolve({
              success: true,
              url: `https://${domain}`
            });
          }, 2000);
        } else if (output.includes('Aborted') || output.includes('Error')) {
          reject(new Error(`Surge 部署失败: ${output}`));
        }
      });
      
      surgeProcess.stderr?.on('data', (data: Buffer) => {
        const output = data.toString();
        stderr += output;
        console.log('Surge 错误输出:', output);
      });
      
      surgeProcess.on('close', (code: number | null) => {
        console.log('Surge 进程结束，退出码:', code);
        
        // 检查是否成功部署
        if (stdout.includes('Success!') || stdout.includes('project uploaded') || code === 0) {
          resolve({
            success: true,
            url: `https://${domain}`
          });
        } else {
          reject(new Error(`Surge 部署失败: ${stderr || stdout || '未知错误'}`));
        }
      });
      
      surgeProcess.on('error', (error: Error) => {
        console.error('Surge 进程错误:', error);
        reject(error);
      });
      
      // 设置超时
      setTimeout(() => {
        if (!surgeProcess.killed) {
          console.log('部署超时，终止进程');
          surgeProcess.kill();
          reject(new Error('Surge 部署超时'));
        }
      }, 120000);
    });
    
  } finally {
    // 延迟清理临时文件，确保部署完成
    setTimeout(() => {
      if (fs.existsSync(tempDir)) {
        try {
          fs.rmSync(tempDir, { recursive: true, force: true });
          console.log('临时文件清理完成');
        } catch (cleanupError) {
          console.log('清理临时文件失败:', cleanupError);
        }
      }
    }, 10000); // 延迟10秒清理
  }
} 