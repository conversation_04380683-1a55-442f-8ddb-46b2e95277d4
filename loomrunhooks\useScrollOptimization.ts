import { useEffect, useRef, useCallback } from 'react';
import { communityCardCache } from '@/lib/performance-cache';

interface ScrollOptimizationOptions {
  containerId?: string;
  throttleMs?: number;
  enableCache?: boolean;
}

/**
 * 🚀 滚动性能优化Hook
 * 专门针对社区卡片的滚动优化
 */
export function useScrollOptimization(options: ScrollOptimizationOptions = {}) {
  const {
    containerId = 'community-container',
    throttleMs = 16, // 60fps
    enableCache = true
  } = options;

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const lastScrollTime = useRef<number>(0);
  const scrollDirection = useRef<'up' | 'down'>('down');
  const lastScrollTop = useRef<number>(0);
  const isScrolling = useRef<boolean>(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);

  // 🎯 节流滚动处理
  const throttledScrollHandler = useCallback((event: Event) => {
    const now = Date.now();
    if (now - lastScrollTime.current < throttleMs) {
      return;
    }
    lastScrollTime.current = now;

    const target = event.target as HTMLElement;
    const currentScrollTop = target.scrollTop;

    // 检测滚动方向
    if (currentScrollTop > lastScrollTop.current) {
      scrollDirection.current = 'down';
    } else if (currentScrollTop < lastScrollTop.current) {
      scrollDirection.current = 'up';
    }

    lastScrollTop.current = currentScrollTop;
    isScrolling.current = true;

    // 缓存滚动位置
    if (enableCache) {
      communityCardCache.cacheScrollPosition(containerId, currentScrollTop);
    }

    // 清除滚动结束定时器
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    // 设置滚动结束定时器
    scrollTimeout.current = setTimeout(() => {
      isScrolling.current = false;
      
      // 滚动结束后清理缓存
      if (enableCache) {
        communityCardCache.cleanup();
        }
    }, 150);
  }, [containerId, throttleMs, enableCache]);

  // 🎯 优化滚动性能
  const optimizeScrollPerformance = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 应用硬件加速
    container.style.transform = 'translateZ(0)';
    container.style.willChange = 'scroll-position';
    container.style.contain = 'layout style paint';

    // 优化滚动行为
    container.style.scrollBehavior = 'auto';
    container.style.overflowAnchor = 'none';

    // 添加滚动监听
    container.addEventListener('scroll', throttledScrollHandler, { passive: true });
    
    return () => {
      container.removeEventListener('scroll', throttledScrollHandler);
      container.style.willChange = 'auto';
    };
  }, [throttledScrollHandler]);

  // 🎯 恢复滚动位置
  const restoreScrollPosition = useCallback(() => {
    if (!enableCache) return;

    const container = scrollContainerRef.current;
    if (!container) return;

    const cachedPosition = communityCardCache.getCachedScrollPosition(containerId);
    if (cachedPosition !== null) {
      container.scrollTop = cachedPosition;
    }
  }, [containerId, enableCache]);

  // 🎯 获取滚动信息
  const getScrollInfo = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) {
      return {
        scrollTop: 0,
        scrollHeight: 0,
        clientHeight: 0,
        direction: scrollDirection.current,
        isScrolling: isScrolling.current
      };
    }

    return {
      scrollTop: container.scrollTop,
      scrollHeight: container.scrollHeight,
      clientHeight: container.clientHeight,
      direction: scrollDirection.current,
      isScrolling: isScrolling.current
    };
  }, []);

  // 🎯 平滑滚动到指定位置
  const smoothScrollTo = useCallback((top: number) => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 使用requestAnimationFrame实现平滑滚动
    const startTop = container.scrollTop;
    const distance = top - startTop;
    const duration = 300;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用easeInOutCubic缓动函数
      const easeProgress = progress < 0.5 
        ? 4 * progress * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      container.scrollTop = startTop + distance * easeProgress;

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }, []);

  // 🎯 初始化优化
  useEffect(() => {
    const cleanup = optimizeScrollPerformance();
    restoreScrollPosition();

    return cleanup;
  }, [optimizeScrollPerformance, restoreScrollPosition]);

  // 🎯 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, []);

  return {
    scrollContainerRef,
    getScrollInfo,
    smoothScrollTo,
    restoreScrollPosition,
    isScrolling: isScrolling.current,
    scrollDirection: scrollDirection.current
  };
}

/**
 * 🎯 iframe滚动优化钩子
 * 专门针对iframe内部滚动的性能优化
 */
export function useIframeScrollOptimization(
  iframeRef: React.RefObject<HTMLIFrameElement | null>,
  enabled: boolean = true
) {
  const rafIdRef = useRef<number | null>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTime = useRef<number>(0);

  // 🎯 iframe内部滚动处理
  const handleIframeScroll = useCallback((event: Event) => {
    if (!enabled) return;

    const now = performance.now();
    
    // 🔧 双重节流：timeout + RAF
    if (now - lastScrollTime.current < 8) {
      return;
    }

    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }

    throttleTimeoutRef.current = setTimeout(() => {
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }

      rafIdRef.current = requestAnimationFrame(() => {
        // 🚀 iframe滚动优化逻辑
        const target = event.target as HTMLElement;
        const scrollTop = target.scrollTop || 0;
        
        // 🎯 性能监控 - 仅开发环境
        if (process.env.NODE_ENV === 'development') {
          console.log(`📜 iframe滚动: ${scrollTop}px`);
        }
        
        lastScrollTime.current = now;
      });
    }, 4); // 4ms延迟，平衡性能与响应性
  }, [enabled]);

  // 🎛️ 设置iframe滚动监听
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe || !enabled) return;

    const setupScrollListener = () => {
      const iframeDocument = iframe.contentDocument;
      const iframeWindow = iframe.contentWindow;
      
      if (iframeDocument && iframeWindow) {
        // 🚀 监听iframe内部滚动
        iframeWindow.addEventListener('scroll', handleIframeScroll, { 
          passive: true 
        });
        
        // 🎯 监听iframe内部元素滚动
        iframeDocument.addEventListener('scroll', handleIframeScroll, { 
          passive: true 
        });

        return () => {
          iframeWindow.removeEventListener('scroll', handleIframeScroll);
          iframeDocument.removeEventListener('scroll', handleIframeScroll);
        };
      }
    };

    // 🔄 iframe加载完成后设置监听器
    const handleLoad = () => {
      setTimeout(setupScrollListener, 100); // 延迟确保iframe完全加载
    };

    iframe.addEventListener('load', handleLoad);
    
    // 🚀 如果iframe已经加载，立即设置
    if (iframe.contentDocument?.readyState === 'complete') {
      handleLoad();
    }

    return () => {
      iframe.removeEventListener('load', handleLoad);
      
      // 🧹 清理定时器和RAF
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, [iframeRef, enabled, handleIframeScroll]);

  return {
    isScrolling: rafIdRef.current !== null,
  };
} 