{"name": "loomrun", "version": "1.0.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev --turbopack -p 3141", "build": "next build", "start": "next start", "lint": "next lint", "db:optimize": "node scripts/optimize-database.js", "db:init": "node scripts/init-db.js", "test:sms": "node scripts/test-sms.js", "verify:config": "node scripts/verify-config.js", "export:ghost": "node scripts/export-ghost-icon.js"}, "dependencies": {"@alicloud/credentials": "^2.4.4", "@alicloud/dysmsapi20170525": "^4.1.2", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-typescript": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.78.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.20.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "monaco-editor": "^0.52.2", "mongoose": "^8.15.1", "mysql2": "^3.14.1", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-use": "^17.6.0", "redaxios": "^0.5.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.57"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.17.6", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "autoprefixer": "^10.4.20", "canvas": "^2.11.2", "eslint": "^9.15.0", "eslint-config-next": "15.3.3", "file-loader": "^6.2.0", "globals": "^15.12.0", "postcss": "^8.5.1", "tailwindcss": "^4.0.0", "typescript": "^5.7.2", "typescript-eslint": "^8.15.0", "url-loader": "^4.1.1"}}