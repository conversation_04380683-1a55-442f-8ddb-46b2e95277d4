import { useEffect, useState } from 'react';

export type Language = 'zh' | 'en' | 'ug';

// 语言包定义
const translations = {
  zh: {
    // 通用
    'common.loading': '加载中...',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.close': '关闭',
    'common.success': '成功',
    'common.error': '错误',
    
    // 导航
    'nav.newProject': '新建项目',
    'nav.projects': '项目',
    'nav.settings': '设置',
    
    // 系统设置
    'settings.title': '系统设置',
    'settings.general': '通用设置',
    'settings.account': '账号管理',
    'settings.payment': '支付设置',
    'settings.legal': '服务协议',
    
    'settings.language': '语言',
    'settings.language.desc': '选择您的界面语言',
    'settings.theme': '主题',
    'settings.theme.desc': '选择您喜欢的界面主题',
    'settings.theme.light': '浅色',
    'settings.theme.dark': '深色',
    'settings.dataOptimization': '数据用于优化体验',
    'settings.dataOptimization.desc': '允许我们将你的对话内容用于优化 LoomRun 的使用体验。我们保障你的数据隐私安全。',
    
    // 提示信息
    'toast.settings.saved': '设置已保存',
    'toast.settings.failed': '保存失败，请重试',
    'toast.dataOptimization.enabled': '已启用数据优化',
    'toast.dataOptimization.disabled': '已关闭数据优化',
    'toast.language.changed': '语言设置已保存',
    'toast.theme.changed': '主题设置已保存',
  },
  
  en: {
    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.close': 'Close',
    'common.success': 'Success',
    'common.error': 'Error',
    
    // Navigation
    'nav.newProject': 'New Project',
    'nav.projects': 'Projects',
    'nav.settings': 'Settings',
    
    // System Settings
    'settings.title': 'Settings',
    'settings.general': 'General',
    'settings.account': 'Account',
    'settings.payment': 'Billing',
    'settings.legal': 'Legal',
    
    'settings.language': 'Language',
    'settings.language.desc': 'Choose your interface language',
    'settings.theme': 'Theme',
    'settings.theme.desc': 'Choose your preferred interface theme',
    'settings.theme.light': 'Light',
    'settings.theme.dark': 'Dark',
    'settings.dataOptimization': 'Data for Experience Optimization',
    'settings.dataOptimization.desc': 'Allow us to use your conversation content to optimize LoomRun experience. We protect your data privacy and security.',
    
    // Toast Messages
    'toast.settings.saved': 'Settings saved successfully',
    'toast.settings.failed': 'Failed to save settings, please try again',
    'toast.dataOptimization.enabled': 'Data optimization enabled',
    'toast.dataOptimization.disabled': 'Data optimization disabled',
    'toast.language.changed': 'Language settings saved',
    'toast.theme.changed': 'Theme settings saved',
  },

  ug: {
    // 通用
    'common.loading': 'يۈكلىنىۋاتىدۇ...',
    'common.save': 'ساقلاش',
    'common.cancel': 'بىكار قىلىش',
    'common.confirm': 'جەزملەشتۈرۈش',
    'common.close': 'يېپىش',
    'common.success': 'مۇۋەپپەقىيەت',
    'common.error': 'خاتالىق',
    
    // 导航
    'nav.newProject': 'يېڭى تۈر',
    'nav.projects': 'تۈرلەر',
    'nav.settings': 'تەڭشەكلەر',
    
    // 系统设置
    'settings.title': 'سىستېما تەڭشىكى',
    'settings.general': 'ئومۇمي تەڭشەك',
    'settings.account': 'ھېسابات تەڭشىكى',
    'settings.payment': 'پۇل تۆلەش تەڭشىكى',
    'settings.legal': 'قانۇنىي كېلىشىم',
    
    'settings.language': 'تىل',
    'settings.language.desc': 'كۆرۈنۈش تىلىنى تاللاڭ',
    'settings.theme': 'ئۇسلۇب',
    'settings.theme.desc': 'ياقتۇرغان كۆرۈنۈش ئۇسلۇبىنى تاللاڭ',
    'settings.theme.light': 'ئاچۇق',
    'settings.theme.dark': 'قارا',
    'settings.dataOptimization': 'سانلىق مەلۇماتنى ئۈنۈملۈكلىك ئۈچۈن ئىشلىتىش',
    'settings.dataOptimization.desc': 'LoomRun خىزمىتىنى ياخشىلاش ئۈچۈن، سۆھبەت مەزمۇنىڭىزنى ئىشلىتىشكە ئىجازەت بېرىڭ. سىزنىڭ مەخپىيەتلىكىڭىزنى قاتتىق قوغدايمىز.',
     
    // 提示信息
    'toast.settings.saved': 'تەڭشەكلەر ساقلاندى',
    'toast.settings.failed': 'ساقلاش مەغلۇپ بولدى، قايتا سىناڭ',
    'toast.dataOptimization.enabled': 'سانلىق مەلۇمات ئەھۋالنى ياخشىلاش قوزغىتىلدى',
    'toast.dataOptimization.disabled': 'سانلىق مەلۇمات ئەھۋالنى ياخشىلاش تاقالدى',
    'toast.language.changed': 'تىل تەڭشىكى ساقلاندى',
    'toast.theme.changed': 'ئۇسلۇب تەڭشىكى ساقلاندى',
  },

};

export function useLanguage() {
  const [language, setLanguage] = useState<Language>('zh');

  // 获取翻译文本
  const t = (key: string): string => {
    const keys = key.split('.');
    let value: Record<string, unknown> | string = translations[language];
    
    for (const k of keys) {
      if (typeof value === 'object' && value !== null) {
        value = (value as Record<string, unknown>)[k] as Record<string, unknown> | string;
      } else {
        break;
      }
    }
    
    return (typeof value === 'string' ? value : key);
  };

  // 初始化语言
  useEffect(() => {
    // 从localStorage读取保存的语言
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['zh', 'en', 'ug'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
    } else {
      // 检测浏览器语言，默认中文
      const browserLanguage = navigator.language.toLowerCase();
      if (browserLanguage.startsWith('zh')) {
        setLanguage('zh');
      } else {
        setLanguage('zh'); // 默认中文
      }
    }
  }, []);

  // 监听设置变更事件
  useEffect(() => {
    const handleSettingsChange = (event: CustomEvent) => {
      const newLanguage = event.detail.language;
      if (newLanguage && newLanguage !== language) {
        setLanguage(newLanguage);
        localStorage.setItem('language', newLanguage);
      }
    };

    const handleSettingsLoaded = (event: CustomEvent) => {
      const loadedLanguage = event.detail.language;
      if (loadedLanguage && loadedLanguage !== language) {
        setLanguage(loadedLanguage);
        localStorage.setItem('language', loadedLanguage);
      }
    };

    window.addEventListener('settingsChanged', handleSettingsChange as EventListener);
    window.addEventListener('settingsLoaded', handleSettingsLoaded as EventListener);
    return () => {
      window.removeEventListener('settingsChanged', handleSettingsChange as EventListener);
      window.removeEventListener('settingsLoaded', handleSettingsLoaded as EventListener);
    };
  }, [language]);

  // 切换语言
  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
    
    // 强制触发重新渲染
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language: newLanguage } 
    }));
  };

  return {
    language,
    t,
    changeLanguage,
  };
} 