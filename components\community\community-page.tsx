"use client";
import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Loader2, Grid, List, TrendingUp, Clock, Star, Code, Smartphone, Globe, BookOpen, Gamepad2, Settings, ArrowLeft, ChevronDown, Filter, SlidersHorizontal } from "lucide-react";
import { CommunityCard } from "./community-card";
import { PreviewModal } from "./preview-modal";
import { useFavorites } from "@/loomrunhooks/useFavorites";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface CommunityPageProps {
  onClose: () => void;
  onOpenProject: (project: CommunityProject) => void;
  onPreviewProject: (project: CommunityProject) => void;
  // 新增：是否需要避开系统顶部栏（在欢迎页面中为true，在编辑器中为false）
  avoidSystemHeader?: boolean;
}

// 分类数据 - 只保留指定的6个分类
const categories = [
  { id: 'sites', name: '网站', icon: Globe, count: 0 },
  { id: 'games', name: '游戏', icon: Gamepad2, count: 0 },
  { id: 'ppt', name: 'PPT', icon: BookOpen, count: 0 },
  { id: 'apps', name: '手机应用', icon: Smartphone, count: 0 },
  { id: 'tools', name: '工具', icon: Settings, count: 0 },
  { id: 'system', name: '系统', icon: Code, count: 0 },
];

// 排序选项
const sortOptions = [
  { id: 'trending', name: '热门', icon: TrendingUp },
  { id: 'recent', name: '最新', icon: Clock },
  { id: 'popular', name: '流行', icon: Star },
];

export function CommunityPage({ onClose, onOpenProject, onPreviewProject, avoidSystemHeader = true }: CommunityPageProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState('sites');
  const [selectedSort, setSelectedSort] = useState('trending');
  const [projects, setProjects] = useState<CommunityProject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 🎯 内部预览状态管理
  const [previewProject, setPreviewProject] = useState<CommunityProject | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  
  const { toggleFavorite } = useFavorites();

  // 🎯 内部预览处理函数
  const handleInternalPreview = useCallback((project: CommunityProject) => {
    setPreviewProject(project);
    setIsPreviewOpen(true);
  }, []);

  const handleClosePreview = useCallback(() => {
    setIsPreviewOpen(false);
    setPreviewProject(null);
  }, []);

  // 处理收藏操作
  const handleToggleFavorite = useCallback(async (projectId: number, isFavorited: boolean) => {
    try {
      const result = await toggleFavorite(projectId, isFavorited);
      return result;
    } catch (error) {
      console.error('收藏操作失败:', error);
      throw error;
    }
  }, [toggleFavorite]);

  // 搜索项目
  const searchProjects = useCallback(async (search: string = "", category: string = 'sites', sort: string = 'trending') => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        search: search,
        category: category,
        sort: sort,
        limit: '50'
      });
      
      const response = await fetch(`/api/community/projects?${params}`);
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      }
    } catch (error) {
      console.error('搜索社区项目失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      searchProjects(searchTerm, selectedCategory, selectedSort);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, selectedCategory, selectedSort, searchProjects]);

  // 初始加载
  useEffect(() => {
    searchProjects();
  }, [searchProjects]);

  // 获取当前选中的分类和排序信息
  const currentCategory = categories.find(cat => cat.id === selectedCategory);
  const currentSort = sortOptions.find(sort => sort.id === selectedSort);

  return (
    <div
      className="flex flex-col bg-background"
      style={avoidSystemHeader ? { height: 'calc(100vh - 44px)' } : { height: '100%' }}
    >
      {/* 🎯 精简的社区页面头部 - 紧贴系统顶部栏 */}
      <div className="flex-shrink-0 bg-card/95 backdrop-blur-sm border-b border-border shadow-sm">
        {/* 精简导航栏 - 只保留返回按钮、标题和视图控制 */}
        <div className="h-12 flex items-center justify-between px-6 gap-4">
          {/* 左侧：简洁返回按钮 */}
          <div className="flex items-center flex-shrink-0">
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="group flex items-center gap-2 px-3 py-2 h-9 text-muted-foreground hover:text-foreground hover:bg-secondary/80 transition-all duration-200 rounded-lg border border-transparent hover:border-border"
            >
              <ArrowLeft className="w-4 h-4 transition-transform duration-200 group-hover:-translate-x-0.5" />
              <span className="text-sm font-medium">返回</span>
            </Button>
          </div>

          {/* 右侧：筛选和视图控制 */}
          <div className="flex items-center gap-3 flex-shrink-0">
            {/* 分类筛选 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-3 bg-background border-border text-foreground hover:bg-secondary transition-all duration-200"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {currentCategory?.name || '分类'}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48 bg-popover border-border shadow-lg" align="end">
                {categories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <DropdownMenuItem
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`flex items-center gap-3 px-3 py-2 cursor-pointer transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-primary text-primary-foreground'
                          : 'text-foreground hover:bg-secondary'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{category.name}</span>
                      {category.count > 0 && (
                        <span className="ml-auto text-xs bg-muted px-2 py-1 rounded-full">
                          {category.count}
                        </span>
                      )}
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* 排序筛选 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-3 bg-background border-border text-foreground hover:bg-secondary transition-all duration-200"
                >
                  <SlidersHorizontal className="w-4 h-4 mr-2" />
                  {currentSort?.name || '排序'}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-32 bg-popover border-border shadow-lg" align="end">
                {sortOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <DropdownMenuItem
                      key={option.id}
                      onClick={() => setSelectedSort(option.id)}
                      className={`flex items-center gap-3 px-3 py-2 cursor-pointer transition-colors ${
                        selectedSort === option.id
                          ? 'bg-primary text-primary-foreground'
                          : 'text-foreground hover:bg-secondary'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{option.name}</span>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* 专业视图切换 */}
            <div className="flex items-center bg-secondary/50 border border-border rounded-lg p-1">
              <Button
                onClick={() => setViewMode('grid')}
                variant="ghost"
                size="sm"
                className={`w-8 h-8 p-0 transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background'
                }`}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setViewMode('list')}
                variant="ghost"
                size="sm"
                className={`w-8 h-8 p-0 transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background'
                }`}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 🎯 主内容区域 - 优化布局 */}
      <div className="flex-1 overflow-y-auto bg-background">
        {/* 专业头部横幅 */}
        <div className="bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 border-b border-border">
          <div className="max-w-7xl mx-auto px-6 py-12">
            <div className="text-center mb-10">
              <h2 className="text-4xl font-bold text-foreground mb-4">
                发现无限创意，构建未来应用
              </h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                精选全球顶尖开发者作品，从创意灵感到商业应用，一站式探索数字化创新的无限可能。
              </p>
            </div>

            {/* 🎯 专业搜索区域 - 替代统计卡片 */}
            <div className="max-w-3xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" />
                <Input
                  placeholder="搜索应用、组件、模板"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-14 pr-12 h-14 bg-background border-border text-foreground placeholder-muted-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200 rounded-2xl text-lg shadow-lg"
                />
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm('')}
                    variant="ghost"
                    size="sm"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 p-0 text-muted-foreground hover:text-foreground rounded-full hover:bg-secondary"
                  >
                    ×
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 🎯 项目展示区域 - 优化宽度利用 */}
        <div className="max-w-[1400px] mx-auto px-8 py-8">
          {/* 项目列表 */}
          {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                  <span className="ml-3 text-muted-foreground">正在加载...</span>
                </div>
              ) : projects.length > 0 ? (
                <div className={`grid gap-6 ${
                  viewMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1 max-w-5xl mx-auto'
                }`}>
                          {projects.map((project, index) => (
          <CommunityCard
            key={`${project.id}-${index}`}
                      project={project}
                      onPreview={handleInternalPreview}
                      onOpen={onOpenProject}
                   onToggleFavorite={handleToggleFavorite}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-20">
                  <div className="w-24 h-24 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center">
                    <Search className="w-12 h-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-2xl font-semibold text-foreground mb-3">
                    {searchTerm ? `没有找到匹配 "${searchTerm}" 的项目` : '暂无相关项目'}
                  </h3>
                  <p className="text-muted-foreground text-lg max-w-md mx-auto mb-8">
                    {searchTerm ? '尝试其他关键词或调整筛选条件' : '当前分类下暂无项目，请尝试其他分类'}
                  </p>
                  <Button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('sites');
                      setSelectedSort('trending');
                    }}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200"
                  >
                    {searchTerm ? '清除搜索' : '浏览全部'}
                  </Button>
                </div>
              )}
        </div>
      </div>

      {/* 🎯 内部预览模态框 */}
      <PreviewModal
        project={previewProject}
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        onOpen={onOpenProject}
      />
    </div>
  );
}