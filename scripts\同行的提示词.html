你是一名出色的前端html+javascript+tailwindCss开发工程师。
你的目标是分析需求, 使用html、 tailwindCss、js按照需求修改给你的模板，按格式要求输出正确的代码。
你输出的行号、原始代码和修改后的代码。我们会在后续脚本中提取出相应的代码,然后按照你输出的原始代码进行字符串匹配替换为修改后的代码。所以请按照模板上的每行代码的原格式进行输出,否则匹配不到对应的原始代码, 会导致替换不了修改后的代码。

技术使用：
1.使用技术：html、javascript、tailwindCss
2.排版布局请使用html原生标签（head、div等）。
3.只能使用原生 JavaScript 进行交互逻辑的编写，JavaScript不能使用以下前端框架代码（jquery、vue、react等）。
4.编写 JavaScript DOM 语法时请参考JavaScript DOM API 语法，避免使用错误的JavaScript 属性​, 例如应该写出正确的属性：var row = document.createElement('tr'); row.innerHTML = "";,而不是编写row.inner = "";这种错误JavaScript 属性。避免出现类似语法错误。

使用的依赖版本如下
使用的"echarts" 推荐src引用 https://unpkg.com/echarts@5.4.3/dist/echarts.min.js。
使用的"tailwindCss" 时只能使用<script>标签引入, 因为需要支持动态生成样式, 保证组件上class中的样式生效, 只能输出这个：<script src="https://cdn.tailwindcss.com"></script>, 请不要使用link标签引入tailwindcss。
使用的"font awesome" 推荐src引用 https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css。

设计要求
1.排版布局要求
编写排版布局组件的class时请使用tailwind css 编写。

2.图表设计要求
如果需求中提及需要通过图表对数据进行展示，则使用echarts，提升页面美观度。
如果需求中没有提及需要通过图表对数据进行展示，则不需要生成图表。
echarts图表显示的div上需要添加合适的高度和宽度, 否则图表显示不出来。

3.icon设计要求
推荐使用font awesome图标库。
可以这样使用font awesome图标库: <i class="fas fa-plus"></i>

4.图片设计要求
图片使用规范
  1. img组件尽量使用自适应大小的class 例如：class="w-full h-full object-cover",保证加载后的图片展示的完整性。
  2. 需要使用图片的地方自动生成占位url, 后面会调用这些站位url完成图片生成或展示。
  、、、
  url生成规则：
  Base URL: ./api/searchImage
  Query Parameters:
  query: 需要你根据图片所在上下文，设计并用英文描述该图片的内容。图片描述构成： 图片具体描述内容, 图片背景颜色、设计风格。例如:premium gaming laptop with aggressive design elements floating in pure white background, professional product photography style, dramatic lighting showing gaming aesthetics.
  width: 图像的宽度。
  height: 图像的高度。

  例如:
  https://design.gemcoder.com/staticResource/echoAiSystemImages/20f9818112121e0135759bbbc92ffdac.png

5.herf跳转链接要求
请不要使用<a href="#"> 这种空链接写法, 请使用<a href=”javascript:void(0);”>。

6.功能要求
如果界面上需要交互类业务逻辑,需要生成完整的交互代码.添加script代码后需要同步修改html的代码,需要保证script代码和html的代码能正常处理业务逻辑并保证页面可以正常使用

7.代码规范
1. 推荐这样的页面初始化代码:
// 页面加载时初始化渲染
document.addEventListener('DOMContentLoaded', () => {
  xxx();
});
2. 禁止生成这样的代码: document.addEventListener('DOMContentLoaded', xxx); 这样实际上等价于这样调用xxx(undefined)。最终会导致页面初始化失败。

编写代码规范
1.请编写完整的工程级代码，避免使用注释代替代码或使用“其他xxx类似，省略...”等不完整的表达方式。请确保提供的代码片段完整无误，因为用户会直接使用它。请严格按照代码编写流程和UI设计要求，完成前端界面的开发。
2.需求及功能没有说明页面大小的，页面大小为自适应大小。
3.如果没有相关生成图表的需求描述，请不要生成图表。
4.可以用图标代替的图片，请使用图标。
5.使用图片时,请根据上下文语义,按照格式生成对应的图片信息。
6.当img组件上这样设置 class="w-full h-48 object-cover " , 对应的图片尺寸的宽应该为192。 -->
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>高级数学学习小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      /* 自定义滚动条样式 */
      ::-webkit-scrollbar {
          width: 8px;
      }
      ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 10px;
      }
      ::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 10px;
      }
      ::-webkit-scrollbar-thumb:hover {
          background: #555;
      }
    </style>
  </head>
  <body class="bg-gray-100 font-sans antialiased">
    <div class="min-h-screen flex flex-col">
      <!-- 顶部导航栏 -->
      <header
        class="bg-white shadow-md py-4 px-6 flex items-center justify-between sticky top-0 z-50"
      >
        <div class="flex items-center space-x-4">
          <i class="fas fa-calculator text-purple-600 text-3xl"> </i>
          <h1 class="text-3xl font-bold text-gray-800">高级数学学习</h1>
        </div>
        <nav class="hidden md:flex space-x-8">
          <a
            class="text-gray-600 hover:text-purple-600 text-lg font-medium transition duration-300"
            href="javascript:void(0);"
          >
            首页
          </a>
          <a
            class="text-gray-600 hover:text-purple-600 text-lg font-medium transition duration-300"
            href="javascript:void(0);"
          >
            课程
          </a>
          <a
            class="text-gray-600 hover:text-purple-600 text-lg font-medium transition duration-300"
            href="javascript:void(0);"
          >
            练习
          </a>
          <a
            class="text-gray-600 hover:text-purple-600 text-lg font-medium transition duration-300"
            href="javascript:void(0);"
          >
            社区
          </a>
          <a
            class="text-gray-600 hover:text-purple-600 text-lg font-medium transition duration-300"
            href="javascript:void(0);"
          >
            关于我们
          </a>
        </nav>
        <div class="flex items-center space-x-4">
          <button
            class="bg-purple-600 text-white px-6 py-2 rounded-full hover:bg-purple-700 transition duration-300 shadow-lg"
          >
            <i class="fas fa-user-circle mr-2"> </i>
            登录/注册
          </button>
        </div>
      </header>
      <!-- 主内容区域 -->
      <main class="flex-1 container mx-auto px-6 py-10">
        <!-- 英雄区/介绍区 -->
        <section
          class="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-3xl shadow-xl p-12 mb-12 flex flex-col md:flex-row items-center justify-between text-white relative overflow-hidden"
        >
          <div class="absolute inset-0 opacity-10">
            <img
              alt="Background Pattern"
              class="w-full h-full object-cover"
              src="https://design.gemcoder.com/staticResource/echoAiSystemImages/1b884f2ada68ef63b21c5383bcd2e5d7.png"
            />
          </div>
          <div
            class="relative z-10 md:w-1/2 text-center md:text-left mb-8 md:mb-0"
          >
            <h2 class="text-5xl font-extrabold leading-tight mb-6">
              探索数学的无限魅力
            </h2>
            <p class="text-xl mb-8 opacity-90">
              从基础概念到高级理论，我们助您掌握数学的精髓，开启智慧之门。
            </p>
            <div
              class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center md:justify-start"
            >
              <a
                class="bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition duration-300 shadow-lg"
                href="javascript:void(0);"
              >
                开始学习
                <i class="fas fa-arrow-right ml-2"> </i>
              </a>
              <a
                class="border border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition duration-300 shadow-lg"
                href="javascript:void(0);"
              >
                了解更多
              </a>
            </div>
          </div>
          <div class="relative z-10 md:w-1/2 flex justify-center">
            <img
              alt="Mathematical Symbols"
              class="w-full max-w-md h-auto object-contain animate-pulse-slow"
              src="https://design.gemcoder.com/staticResource/echoAiSystemImages/720fb74063a2cf5917e569b2ee51b3fd.png"
            />
          </div>
        </section>
        <!-- 特色课程区 -->
        <section class="mb-12">
          <h3 class="text-4xl font-bold text-gray-800 text-center mb-10">
            <i class="fas fa-book-open text-purple-600 mr-3"> </i>
            特色课程
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 课程卡片 1 -->
            <div
              class="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:scale-105 transition duration-300 ease-in-out"
            >
              <img
                alt="高等数学"
                class="w-full h-48 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/4aca2149fc5b3fe7c80ddfd797643b17.png"
              />
              <div class="p-6">
                <h4 class="text-2xl font-semibold text-gray-800 mb-3">
                  高等数学基础
                </h4>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  深入浅出地讲解微积分、线性代数等核心概念，为您的数学学习打下坚实基础。
                </p>
                <div class="flex items-center justify-between">
                  <span class="text-purple-600 font-bold text-xl"> 免费 </span>
                  <a
                    class="text-purple-600 hover:text-purple-800 font-medium flex items-center"
                    href="javascript:void(0);"
                  >
                    立即学习
                    <i class="fas fa-chevron-right ml-2 text-sm"> </i>
                  </a>
                </div>
              </div>
            </div>
            <!-- 课程卡片 2 -->
            <div
              class="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:scale-105 transition duration-300 ease-in-out"
            >
              <img
                alt="离散数学"
                class="w-full h-48 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/c4ea52f57884a6b11b0839c8450bbe71.png"
              />
              <div class="p-6">
                <h4 class="text-2xl font-semibold text-gray-800 mb-3">
                  离散数学与应用
                </h4>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  探索集合论、图论、数理逻辑等，培养计算思维和问题解决能力。
                </p>
                <div class="flex items-center justify-between">
                  <span class="text-purple-600 font-bold text-xl">
                    $49.99
                  </span>
                  <a
                    class="text-purple-600 hover:text-purple-800 font-medium flex items-center"
                    href="javascript:void(0);"
                  >
                    立即学习
                    <i class="fas fa-chevron-right ml-2 text-sm"> </i>
                  </a>
                </div>
              </div>
            </div>
            <!-- 课程卡片 3 -->
            <div
              class="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:scale-105 transition duration-300 ease-in-out"
            >
              <img
                alt="概率统计"
                class="w-full h-48 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/f088b3d6c7596c6389071c811f5c7f4c.png"
              />
              <div class="p-6">
                <h4 class="text-2xl font-semibold text-gray-800 mb-3">
                  概率论与数理统计
                </h4>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  掌握概率分布、假设检验、回归分析等，应用于数据科学和机器学习。
                </p>
                <div class="flex items-center justify-between">
                  <span class="text-purple-600 font-bold text-xl">
                    $69.99
                  </span>
                  <a
                    class="text-purple-600 hover:text-purple-800 font-medium flex items-center"
                    href="javascript:void(0);"
                  >
                    立即学习
                    <i class="fas fa-chevron-right ml-2 text-sm"> </i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- 学习进度/统计区 (使用ECharts) -->
        <section class="mb-12 bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-4xl font-bold text-gray-800 text-center mb-8">
            <i class="fas fa-chart-line text-purple-600 mr-3"> </i>
            我的学习概览
          </h3>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-2xl font-semibold text-gray-700 mb-4">
                学习时长统计
              </h4>
              <div class="w-full h-80" id="studyTimeChart"></div>
            </div>
            <div>
              <h4 class="text-2xl font-semibold text-gray-700 mb-4">
                课程完成度
              </h4>
              <div class="w-full h-80" id="courseCompletionChart"></div>
            </div>
          </div>
        </section>
        <!-- 用户评价/推荐区 -->
        <section class="mb-12">
          <h3 class="text-4xl font-bold text-gray-800 text-center mb-10">
            <i class="fas fa-comments text-purple-600 mr-3"> </i>
            用户评价
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 评价卡片 1 -->
            <div
              class="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center transform hover:translate-y-[-5px] transition duration-300"
            >
              <img
                alt="用户头像"
                class="w-20 h-20 rounded-full object-cover mb-4 border-4 border-purple-200"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/fda456420402e04cdcfac5961247bcb7.png"
              />
              <p class="text-gray-700 italic mb-4">
                “这个平台让数学学习变得如此有趣和高效！课程内容组织得非常好，老师讲解清晰易懂。”
              </p>
              <div class="text-yellow-500 mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
              </div>
              <p class="font-semibold text-gray-800">- 张华, 大学生</p>
            </div>
            <!-- 评价卡片 2 -->
            <div
              class="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center transform hover:translate-y-[-5px] transition duration-300"
            >
              <img
                alt="用户头像"
                class="w-20 h-20 rounded-full object-cover mb-4 border-4 border-purple-200"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/bd82824a33a80416a1fe2710a25dd756.png"
              />
              <p class="text-gray-700 italic mb-4">
                “我一直对数学感到头疼，但这里的练习和互动让我茅塞顿开，进步神速！”
              </p>
              <div class="text-yellow-500 mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star-half-alt"> </i>
              </div>
              <p class="font-semibold text-gray-800">- 李明, 工程师</p>
            </div>
            <!-- 评价卡片 3 -->
            <div
              class="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center transform hover:translate-y-[-5px] transition duration-300"
            >
              <img
                alt="用户头像"
                class="w-20 h-20 rounded-full object-cover mb-4 border-4 border-purple-200"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/d71391ba41bf6bdaa11aa9d486de704c.png"
              />
              <p class="text-gray-700 italic mb-4">
                “界面设计简洁美观，功能强大，是学习数学不可多得的好帮手！”
              </p>
              <div class="text-yellow-500 mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
              </div>
              <p class="font-semibold text-gray-800">- 王丽, 高中生</p>
            </div>
          </div>
        </section>
        <!-- CTA 区块 -->
        <section
          class="bg-purple-700 rounded-3xl shadow-xl p-12 text-white text-center mb-12"
        >
          <h3 class="text-4xl font-bold mb-6">准备好开启您的数学之旅了吗？</h3>
          <p class="text-xl mb-8 opacity-90">
            立即加入我们，解锁无限的数学潜能！
          </p>
          <a
            class="bg-white text-purple-700 px-10 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition duration-300 shadow-lg"
            href="javascript:void(0);"
          >
            免费注册
            <i class="fas fa-user-plus ml-2"> </i>
          </a>
        </section>
      </main>
      <!-- 底部 -->
      <footer class="bg-gray-800 text-white py-10 px-6">
        <div class="container mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h4 class="text-2xl font-bold mb-4">高级数学学习</h4>
            <p class="text-gray-400">
              致力于提供高质量的数学教育资源，帮助全球学生和爱好者掌握数学知识。
            </p>
          </div>
          <div>
            <h4 class="text-xl font-bold mb-4">快速链接</h4>
            <ul class="space-y-2">
              <li>
                <a
                  class="text-gray-400 hover:text-white transition duration-300"
                  href="javascript:void(0);"
                >
                  所有课程
                </a>
              </li>
              <li>
                <a
                  class="text-gray-400 hover:text-white transition duration-300"
                  href="javascript:void(0);"
                >
                  常见问题
                </a>
              </li>
              <li>
                <a
                  class="text-gray-400 hover:text-white transition duration-300"
                  href="javascript:void(0);"
                >
                  隐私政策
                </a>
              </li>
              <li>
                <a
                  class="text-gray-400 hover:text-white transition duration-300"
                  href="javascript:void(0);"
                >
                  服务条款
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 class="text-xl font-bold mb-4">联系我们</h4>
            <p class="text-gray-400 mb-2">
              <i class="fas fa-envelope mr-2"> </i>
              <EMAIL>
            </p>
            <p class="text-gray-400 mb-2">
              <i class="fas fa-phone mr-2"> </i>
              +****************
            </p>
            <div class="flex space-x-4 mt-4">
              <a
                class="text-gray-400 hover:text-white text-2xl transition duration-300"
                href="javascript:void(0);"
              >
                <i class="fab fa-facebook"> </i>
              </a>
              <a
                class="text-gray-400 hover:text-white text-2xl transition duration-300"
                href="javascript:void(0);"
              >
                <i class="fab fa-twitter"> </i>
              </a>
              <a
                class="text-gray-400 hover:text-white text-2xl transition duration-300"
                href="javascript:void(0);"
              >
                <i class="fab fa-linkedin"> </i>
              </a>
              <a
                class="text-gray-400 hover:text-white text-2xl transition duration-300"
                href="javascript:void(0);"
              >
                <i class="fab fa-instagram"> </i>
              </a>
            </div>
          </div>
        </div>
        <div
          class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-500"
        >
          © 2023 高级数学学习. 保留所有权利.
        </div>
      </footer>
    </div>
    <script>
      // 保存原生方法
      var nativeElementQuerySelector = Element.prototype.querySelector;
      var nativeDocumentQuerySelector = Document.prototype.querySelector;
      function ytCustomQuerySelector(selector) {
        // 第二步：尝试用选择器获取DOM元素
        // 执行原生选择器查询
        var foundElement = this === document ? nativeDocumentQuerySelector.call(this, selector) : nativeElementQuerySelector.call(this, selector);
        if (foundElement) {
          // 设置属性
          if (!foundElement.hasAttribute('data-selectorname')) {
            foundElement.setAttribute('data-selectorname', selector);
          }
          // 第三步：直接返回找到的元素
          return foundElement;
        }

        // 如果通过选择器没找到，尝试通过data-selectorName属性查找
        var allElements = document.querySelectorAll('[data-selectorname]');
        for (var i = 0; i < allElements.length; i++) {
          if (allElements[i].getAttribute('data-selectorname') === selector) {
            return allElements[i];
          }
        }

        // 如果都没找到，返回null
        return null;
      }

      // 如果需要也重写querySelectorAll，可以类似实现
      // 重写原生的querySelector
      Document.prototype.querySelector = ytCustomQuerySelector;
      Element.prototype.querySelector = ytCustomQuerySelector;
      var nativeElementInsertBefore = Element.prototype.insertBefore;
      function ytCustomInsertBefore(newNode, referenceNode) {
        // 当前元素作为默认父元素
        var defaultParentNode = this;

        // 检查参考节点是否存在
        if (!referenceNode) {
          // 如果没有提供参考节点，直接添加到末尾
          return nativeElementInsertBefore.call(defaultParentNode, newNode, null);
        }

        // 检查参考节点是否仍然是父节点的直接子节点
        if (referenceNode.parentNode === defaultParentNode) {
          // 正常情况：参考节点仍在父节点下，直接插入
          return nativeElementInsertBefore.call(defaultParentNode, newNode, referenceNode);
        }

        // 检查参考节点是否有 data-ytparentvalue 属性（被移动出去的节点）
        var referenceParentValue = referenceNode.getAttribute('data-ytparentvalue');
        if (referenceParentValue) {
          // 查找具有匹配 data-ytextravalue 的父元素
          var actualParentNode = document.querySelector('[data-ytextravalue="' + referenceParentValue + '"]');
          if (actualParentNode) {
            // 获取参考节点原来的索引位置
            var originalIndex = referenceNode.getAttribute('data-ytoriginindex');
            if (originalIndex !== null && !isNaN(originalIndex)) {
              // 获取实际父节点当前的所有子节点
              var children = Array.from(actualParentNode.children);

              // 查找应该插入的位置
              for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var childOriginalIndex = child.getAttribute('data-ytoriginindex');

                // 如果子节点有原始索引，并且比参考节点的原始索引大
                if (childOriginalIndex !== null && !isNaN(childOriginalIndex)) {
                  if (parseInt(childOriginalIndex) > parseInt(originalIndex)) {
                    // 找到第一个索引更大的节点，插入到它前面
                    return nativeElementInsertBefore.call(actualParentNode, newNode, child);
                  }
                }
              }

              // 如果没有找到更大的索引，插入到最后
              return nativeElementInsertBefore.call(actualParentNode, newNode, null);
            }

            // 没有原始索引信息，插入到实际父元素的最后
            return nativeElementInsertBefore.call(actualParentNode, newNode, null);
          }
        }

        // 默认情况：插入到当前父元素的最后
        return nativeElementInsertBefore.call(defaultParentNode, newNode, null);
      }

      // 重写原生 insertBefore 方法
      Element.prototype.insertBefore = ytCustomInsertBefore;

      // 页面加载时初始化渲染
      document.addEventListener('DOMContentLoaded', function () {
        renderStudyTimeChart();
        renderCourseCompletionChart();
      });
      function renderStudyTimeChart() {
        var chartDom = document.getElementById('studyTimeChart');
        if (!chartDom) {
          console.error("ECharts DOM element 'studyTimeChart' not found.");
          return;
        }
        var myChart = echarts.init(chartDom);
        var option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            axisLabel: {
              color: '#666'
            }
          },
          yAxis: {
            type: 'value',
            name: '学习时长 (小时)',
            axisLabel: {
              color: '#666'
            }
          },
          series: [{
            name: '学习时长',
            type: 'bar',
            data: [2.5, 3.2, 2.8, 4.0, 3.5, 5.0, 1.5],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#8e44ad'
              },
              // Start color (purple)
              {
                offset: 1,
                color: '#3498db'
              } // End color (blue)
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        myChart.setOption(option);
        window.addEventListener('resize', function () {
          return myChart.resize();
        });
      }
      function renderCourseCompletionChart() {
        var chartDom = document.getElementById('courseCompletionChart');
        if (!chartDom) {
          console.error("ECharts DOM element 'courseCompletionChart' not found.");
          return;
        }
        var myChart = echarts.init(chartDom);
        var option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {d}%'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: ['高等数学基础', '离散数学与应用', '概率论与数理统计', '其他课程'],
            textStyle: {
              color: '#666'
            }
          },
          series: [{
            name: '课程完成度',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold',
                formatter: '{b}\n{d}%'
              }
            },
            labelLine: {
              show: false
            },
            data: [{
              value: 75,
              name: '高等数学基础',
              itemStyle: {
                color: '#9b59b6'
              }
            },
            // Purple
            {
              value: 40,
              name: '离散数学与应用',
              itemStyle: {
                color: '#3498db'
              }
            },
            // Blue
            {
              value: 60,
              name: '概率论与数理统计',
              itemStyle: {
                color: '#2ecc71'
              }
            },
            // Green
            {
              value: 25,
              name: '其他课程',
              itemStyle: {
                color: '#f1c40f'
              }
            } // Yellow
            ]
          }]
        };
        myChart.setOption(option);
        window.addEventListener('resize', function () {
          return myChart.resize();
        });
      }
    </script>
  </body>
</html>
