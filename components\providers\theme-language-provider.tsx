'use client';

import { useTheme } from '@/loomrunhooks/useTheme';
import { useLanguage } from '@/loomrunhooks/useLanguage';
import { createContext, useContext, ReactNode } from 'react';

interface ThemeLanguageContextType {
  theme: 'light' | 'dark';
  resolvedTheme: 'light' | 'dark';
  changeTheme: (theme: 'light' | 'dark') => void;
  language: 'zh' | 'en' | 'ug';
  t: (key: string) => string;
  changeLanguage: (language: 'zh' | 'en' | 'ug') => void;
}

const ThemeLanguageContext = createContext<ThemeLanguageContextType | undefined>(undefined);

export function useThemeLanguage() {
  const context = useContext(ThemeLanguageContext);
  if (!context) {
    throw new Error('useThemeLanguage must be used within ThemeLanguageProvider');
  }
  return context;
}

interface ThemeLanguageProviderProps {
  children: ReactNode;
}

export function ThemeLanguageProvider({ children }: ThemeLanguageProviderProps) {
  const { theme, resolvedTheme, changeTheme } = useTheme();
  const { language, t, changeLanguage } = useLanguage();

  return (
    <ThemeLanguageContext.Provider
      value={{
        theme,
        resolvedTheme,
        changeTheme,
        language,
        t,
        changeLanguage,
      }}
    >
      {children}
    </ThemeLanguageContext.Provider>
  );
} 