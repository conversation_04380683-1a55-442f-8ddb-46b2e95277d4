import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { 
  getInvitationSettings, 
  getUserInvitationStats, 
  generateInvitationLink,
  validateInviteCode 
} from "@/lib/invitation-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    console.log(`🧪 开始测试邀请系统 - 用户: ${user.id} (${user.nickname})`);

    // 1. 测试获取邀请设置
    console.log('📋 1. 测试获取邀请设置...');
    const settings = await getInvitationSettings();
    console.log('邀请设置:', settings);

    // 2. 测试获取用户邀请统计
    console.log('📊 2. 测试获取用户邀请统计...');
    const stats = await getUserInvitationStats(user.id);
    console.log('邀请统计:', stats);

    // 3. 测试生成邀请链接
    console.log('🔗 3. 测试生成邀请链接...');
    const linkResult = await generateInvitationLink(user.id);
    console.log('邀请链接结果:', linkResult);

    // 4. 测试验证邀请码（使用用户自己的邀请码）
    if (user.invite_code) {
      console.log('✅ 4. 测试验证邀请码...');
      const validationResult = await validateInviteCode(user.invite_code);
      console.log('邀请码验证结果:', validationResult);
    }

    return NextResponse.json({
      success: true,
      message: "邀请系统测试完成",
      data: {
        user: {
          id: user.id,
          nickname: user.nickname,
          invite_code: user.invite_code,
          invitation_count: user.invitation_count
        },
        settings,
        stats,
        linkResult,
        validationResult: user.invite_code ? await validateInviteCode(user.invite_code) : null
      }
    });

  } catch (error) {
    console.error("邀请系统测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
