import { useEffect, useState } from 'react';

export type Theme = 'light' | 'dark';

export function useTheme() {
  const [theme, setTheme] = useState<Theme>('dark'); // 默认深色模式
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('dark');

  // 应用主题到DOM
  const applyTheme = (newTheme: Theme) => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    
    // 移除所有主题类
    root.classList.remove('light', 'dark');
    
    // 添加新主题类
    root.classList.add(newTheme);
    
    // 更新body背景色
    const body = document.body;
    if (newTheme === 'dark') {
      body.style.background = 'linear-gradient(135deg, #0a0a0f 0%, #111118 25%, #0d0d14 50%, #161620 75%, #0f0f16 100%)';
    } else {
      body.style.background = 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 25%, #ffffff 50%, #f8f8f8 75%, #fafafa 100%)';
    }
    
    // 更新meta标签（移动端状态栏）
    const metaTheme = document.querySelector('meta[name="theme-color"]');
    if (metaTheme) {
      metaTheme.setAttribute('content', newTheme === 'dark' ? '#0a0a0f' : '#ffffff');
    }

    setResolvedTheme(newTheme);
  };

  // 初始化主题
  useEffect(() => {
    // 从localStorage读取保存的主题
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      setTheme(savedTheme);
      applyTheme(savedTheme);
    } else {
      // 默认使用深色主题
      setTheme('dark');
      applyTheme('dark');
      localStorage.setItem('theme', 'dark');
    }
  }, []);

  // 监听设置变更事件
  useEffect(() => {
    const handleSettingsChange = (event: CustomEvent) => {
      const newTheme = event.detail.theme;
      if (newTheme && newTheme !== theme) {
        setTheme(newTheme);
        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
      }
    };

    const handleSettingsLoaded = (event: CustomEvent) => {
      const loadedTheme = event.detail.theme;
      if (loadedTheme && loadedTheme !== theme) {
        setTheme(loadedTheme);
        applyTheme(loadedTheme);
        localStorage.setItem('theme', loadedTheme);
      }
    };

    window.addEventListener('settingsChange', handleSettingsChange as EventListener);
    window.addEventListener('settingsChanged', handleSettingsChange as EventListener);
    window.addEventListener('settingsLoaded', handleSettingsLoaded as EventListener);
    return () => {
      window.removeEventListener('settingsChange', handleSettingsChange as EventListener);
      window.removeEventListener('settingsChanged', handleSettingsChange as EventListener);
      window.removeEventListener('settingsLoaded', handleSettingsLoaded as EventListener);
    };
  }, [theme]);

  // 切换主题
  const changeTheme = (newTheme: Theme) => {
    setTheme(newTheme);
    applyTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    
    // 同步更新设置存储
    try {
      const settingsKey = 'loomrun_settings';
      const currentSettings = localStorage.getItem(settingsKey);
      if (currentSettings) {
        const settings = JSON.parse(currentSettings);
        settings.theme = newTheme;
        localStorage.setItem(settingsKey, JSON.stringify(settings));
      }
    } catch (error) {
      console.warn('Failed to sync theme to settings:', error);
    }
    
    // 广播主题变更事件
    window.dispatchEvent(new CustomEvent('settingsChange', {
      detail: { theme: newTheme }
    }));
    window.dispatchEvent(new CustomEvent('settingsChanged', {
      detail: { theme: newTheme }
    }));
  };

  // 切换主题（在当前主题之间切换）
  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    changeTheme(newTheme);
  };

  return {
    theme,
    resolvedTheme,
    changeTheme,
    toggleTheme,
  };
} 