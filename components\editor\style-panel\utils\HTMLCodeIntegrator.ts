import { StyleManager } from './StyleManager';
import { TailwindCleaner } from './TailwindCleaner';

// HTML代码集成器 - 将自定义样式整合到HTML中，防止Tailwind污染
export class HTMLCodeIntegrator {
  
  // 将自定义CSS整合到HTML代码中
  static integrateCustomStyles(htmlContent: string): string {
    const customCSS = StyleManager.exportCustomCSS();
    
    if (!customCSS.trim()) {
      return htmlContent;
    }

    // 解析HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // 查找现有的style标签
    let styleTag = doc.querySelector('style#loomrun-custom-styles') as HTMLStyleElement;
    
    if (styleTag) {
      // 更新现有的style标签
      styleTag.textContent = customCSS;
    } else {
      // 查找head标签或创建一个
      let head = doc.querySelector('head');
      if (!head) {
        head = doc.createElement('head');
        doc.documentElement.insertBefore(head, doc.body);
      }
      
      // 创建新的style标签
      styleTag = doc.createElement('style') as HTMLStyleElement;
      styleTag.id = 'loomrun-custom-styles';
      styleTag.type = 'text/css';
      styleTag.textContent = customCSS;
      
      // 插入到head的末尾，确保优先级
      head.appendChild(styleTag);
    }
    
    // 返回更新后的HTML
    return this.formatHTML(doc.documentElement.outerHTML);
  }

  // 清理HTML中的临时样式和类
  static cleanupHTML(htmlContent: string): string {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // 移除所有临时的内联样式属性（保留原有的）
    const elementsWithInlineStyles = doc.querySelectorAll('[style]');
    elementsWithInlineStyles.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;
      
      // 只移除我们添加的样式属性，保留原有的
      const stylesToRemove = [
        'color', 'background-color', 'font-size', 'font-weight',
        'text-align', 'padding', 'margin', 'border-radius', 'border', 'opacity'
      ];
      
      let hasRemainingStyles = false;
      const remainingStyles: string[] = [];
      
      // 检查哪些样式需要保留
      for (let i = 0; i < style.length; i++) {
        const prop = style[i];
        if (!stylesToRemove.includes(prop)) {
          remainingStyles.push(`${prop}: ${style.getPropertyValue(prop)}`);
          hasRemainingStyles = true;
        }
      }
      
      // 更新style属性
      if (hasRemainingStyles) {
        htmlElement.setAttribute('style', remainingStyles.join('; '));
      } else {
        htmlElement.removeAttribute('style');
      }
    });
    
    return this.formatHTML(doc.documentElement.outerHTML);
  }

  // 🔧 修正：格式化HTML代码（使用统一的结构化格式化）
  private static formatHTML(html: string): string {
    // 使用TailwindCleaner的统一格式化方法
    return TailwindCleaner.formatHTMLStructure(html);
  }

  // 🔧 新增：清理动态生成的DOM元素
  private static cleanDynamicElements(doc: Document): void {
    console.log('🧹 开始清理动态生成的DOM元素...');

    // 1. 清理明显的动态生成元素
    const dynamicSelectors = [
      // 心形动画元素（包含随机位置和动画时长）
      '.floating-heart[style*="left:"][style*="animation-duration"]',
      '.heart[style*="left:"][style*="animation-duration"]',
      // 任何包含随机动画属性的元素
      '[style*="animation-duration"][style*="font-size"][style*="left:"]',
      // 明确标记的动态元素
      '[data-dynamic="true"]',
      '[data-generated="true"]'
    ];

    let removedCount = 0;

    dynamicSelectors.forEach(selector => {
      const elements = doc.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        const style = htmlElement.style;

        // 检查是否是动态生成的元素
        const isDynamic = this.isDynamicElement(htmlElement);

        if (isDynamic) {
          console.log('🗑️ 移除动态生成的元素:', {
            tag: htmlElement.tagName,
            class: htmlElement.className,
            style: style.cssText.substring(0, 100) + '...'
          });
          htmlElement.remove();
          removedCount++;
        }
      });
    });

    // 2. 清理包含大量动态子元素的容器
    this.cleanDynamicContainers(doc);

    console.log(`✅ 动态元素清理完成，移除了 ${removedCount} 个元素`);
  }

  // 🔧 判断元素是否为动态生成
  private static isDynamicElement(element: HTMLElement): boolean {
    const style = element.style;
    const cssText = style.cssText;

    // 检查动态特征
    const dynamicFeatures = [
      // 包含随机的vw单位位置
      /left:\s*\d+\.\d+vw/,
      // 包含随机的动画时长（小数点）
      /animation-duration:\s*\d+\.\d+s/,
      // 包含随机的字体大小（小数点）
      /font-size:\s*\d+\.\d+rem/,
      // 同时包含left、animation-duration和font-size
      /left:.*animation-duration.*font-size/
    ];

    // 如果匹配多个动态特征，很可能是动态生成的
    const matchCount = dynamicFeatures.filter(pattern => pattern.test(cssText)).length;

    // 特殊检查：心形元素且包含随机属性
    const isHeartWithRandomProps = (
      (element.classList.contains('floating-heart') || element.classList.contains('heart')) &&
      cssText.includes('left:') &&
      cssText.includes('animation-duration:') &&
      /\d+\.\d+/.test(cssText) // 包含小数点（随机值的特征）
    );

    return matchCount >= 2 || isHeartWithRandomProps;
  }

  // 🔧 清理包含动态子元素的容器
  private static cleanDynamicContainers(doc: Document): void {
    // 查找可能包含动态元素的容器
    const containers = doc.querySelectorAll('#floatingHearts, #hearts-container, .floating-hearts');

    containers.forEach(container => {
      const children = Array.from(container.children);
      let dynamicChildCount = 0;

      // 统计动态子元素数量
      children.forEach(child => {
        if (this.isDynamicElement(child as HTMLElement)) {
          dynamicChildCount++;
        }
      });

      // 如果容器中大部分都是动态元素，清空容器内容
      if (dynamicChildCount > 0 && dynamicChildCount / children.length > 0.5) {
        console.log(`🧹 清理动态容器: ${container.id || container.className}, 移除 ${dynamicChildCount} 个动态子元素`);

        // 只移除动态元素，保留静态元素
        children.forEach(child => {
          if (this.isDynamicElement(child as HTMLElement)) {
            child.remove();
          }
        });
      }
    });
  }

  // 🔧 新增：清理临时属性和类
  private static cleanTemporaryAttributes(doc: Document): void {
    console.log('🧹 开始清理临时属性和类...');

    // 1. 清理所有元素的临时类
    const allElements = doc.querySelectorAll('*');
    let cleanedCount = 0;

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const classes = Array.from(htmlElement.classList);

      // 定义需要清理的临时类
      const temporaryClasses = [
        'hovered-element',
        'selected-element',
        'selected-element-highlight', // 🎯 关键修复：添加选中元素高亮类
        'editing-element',
        'highlighted',
        'temp-class'
      ];

      // 过滤掉临时类，保留有用的类
      const filteredClasses = classes.filter(cls => {
        // 保留自定义样式类
        if (cls.startsWith('custom-')) return true;
        // 移除临时类
        if (temporaryClasses.includes(cls)) return false;
        // 保留其他有意义的类（非空且不是临时类）
        return cls.trim() !== '';
      });

      // 🔧 修复：安全地更新class属性（处理SVG元素）
      try {
        if (filteredClasses.length > 0) {
          // 对于SVG元素，使用setAttribute方法
          if (htmlElement instanceof SVGElement) {
            htmlElement.setAttribute('class', filteredClasses.join(' '));
          } else {
            htmlElement.className = filteredClasses.join(' ');
          }
        } else {
          // 如果没有有效的类，移除class属性
          htmlElement.removeAttribute('class');
        }
      } catch (error) {
        // 如果设置className失败，尝试使用setAttribute
        console.warn('设置className失败，使用setAttribute:', error);
        if (filteredClasses.length > 0) {
          htmlElement.setAttribute('class', filteredClasses.join(' '));
        } else {
          htmlElement.removeAttribute('class');
        }
      }

      if (classes.length !== filteredClasses.length) {
        cleanedCount++;
      }
    });

    // 2. 清理空的class属性
    const elementsWithEmptyClass = doc.querySelectorAll('[class=""]');
    elementsWithEmptyClass.forEach(element => {
      element.removeAttribute('class');
      cleanedCount++;
    });

    // 🎯 新增：清理空的style属性
    const elementsWithEmptyStyle = doc.querySelectorAll('[style=""]');
    elementsWithEmptyStyle.forEach(element => {
      element.removeAttribute('style');
      cleanedCount++;
      console.log('🧹 移除空的style属性:', element.tagName);
    });

    console.log(`✅ 临时属性清理完成，处理了 ${cleanedCount} 个元素`);
  }

  // 🎯 新增：清理编辑模式的临时脚本和样式
  private static cleanEditModeElements(doc: Document): void {
    console.log('🧹 开始清理编辑模式的临时元素...');

    let removedCount = 0;

    // 1. 清理编辑模式脚本
    const editModeScripts = doc.querySelectorAll('script#edit-mode-script');
    editModeScripts.forEach(script => {
      console.log('🗑️ 移除编辑模式脚本:', script.id);
      script.remove();
      removedCount++;
    });

    // 2. 清理选中元素样式
    const selectedElementStyles = doc.querySelectorAll('style#selected-element-style');
    selectedElementStyles.forEach(style => {
      console.log('🗑️ 移除选中元素样式:', style.id);
      style.remove();
      removedCount++;
    });

    // 3. 清理编辑模式样式
    const editModeStyles = doc.querySelectorAll('style#edit-mode-style');
    editModeStyles.forEach(style => {
      console.log('🗑️ 移除编辑模式样式:', style.id);
      style.remove();
      removedCount++;
    });

    // 4. 清理任何包含编辑模式相关内容的脚本或样式
    const allScripts = doc.querySelectorAll('script');
    allScripts.forEach(script => {
      const content = script.textContent || '';
      if (content.includes('__editModeCleanup') ||
          content.includes('编辑模式：注入事件阻止脚本') ||
          content.includes('__editModeListeners')) {
        console.log('🗑️ 移除包含编辑模式内容的脚本');
        script.remove();
        removedCount++;
      }
    });

    const allStyles = doc.querySelectorAll('style');
    allStyles.forEach(style => {
      const content = style.textContent || '';
      if (content.includes('selected-element-highlight') &&
          style.id !== 'loomrun-custom-styles') {
        console.log('🗑️ 移除包含选中元素高亮的样式');
        style.remove();
        removedCount++;
      }
    });

    console.log(`✅ 编辑模式元素清理完成，移除了 ${removedCount} 个元素`);
  }

  // 🔧 新增：清理动态注入的Tailwind样式
  private static cleanDynamicTailwindStyles(doc: Document): void {
    console.log('🧹 开始清理动态注入的Tailwind样式...');

    // 查找所有style标签
    const styleElements = Array.from(doc.querySelectorAll('style'));
    let removedCount = 0;

    styleElements.forEach((styleElement, index) => {
      const content = styleElement.textContent || '';

      // 🔧 跳过用户自定义样式和系统标记的样式
      if (styleElement.id === 'loomrun-custom-styles' ||
          styleElement.hasAttribute('data-system-inserted')) {
        return;
      }

      // 检查是否是动态注入的Tailwind样式
      if (this.isDynamicTailwindStyle(content)) {
        console.log(`🗑️ 移除动态注入的Tailwind样式 ${index + 1}:`, {
          id: styleElement.id || 'no-id',
          length: content.length,
          preview: content.substring(0, 100) + '...'
        });
        styleElement.remove();
        removedCount++;
      }
    });

    console.log(`✅ 动态Tailwind样式清理完成，移除了 ${removedCount} 个样式块`);
  }

  // 🔧 判断是否为动态注入的Tailwind样式
  private static isDynamicTailwindStyle(content: string): boolean {
    // 🔧 强化检测：更严格的Tailwind特征识别
    const tailwindFeatures = [
      // Tailwind版本信息
      /tailwindcss v\d+\.\d+\.\d+/,
      // MIT许可证
      /MIT License.*https:\/\/tailwindcss\.com/,
      // 🔧 强化：压缩的Tailwind变量（实际出现的模式）
      /\*,\s*::before,\s*::after\{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0/,
      // 🔧 强化：检测开头的Tailwind变量模式
      /^\*,\s*::before,\s*::after\{--tw-/,
      // 大量连续的--tw-变量
      /(--tw-[a-z-]+:\s*[^;]+;\s*){10,}/,
      // Tailwind的container类定义
      /\.container\{width:100%\}@media\s*\(min-width:\s*640px\)/,
      // 🔧 新增：检测Tailwind的媒体查询模式
      /@media\s*\(min-width:\s*\d+px\)\{[^}]*\{[^}]*\}\}/,
      // 🔧 新增：检测Tailwind的响应式类
      /\.(sm|md|lg|xl|2xl)\\:/
    ];

    // 检查是否匹配Tailwind特征
    const matchCount = tailwindFeatures.filter(pattern => pattern.test(content)).length;

    // 🔧 强化：检查样式块大小和--tw-变量密度
    const twVariableCount = (content.match(/--tw-/g) || []).length;
    const isLargeTailwindBlock = content.length > 1000 && twVariableCount > 10; // 降低阈值

    // 🔧 强化：检测以Tailwind变量开头的样式块
    const startsWithTailwindVars = /^\*,\s*::before,\s*::after\{--tw-/.test(content.trim());

    // 🔧 强化：检测是否包含用户自定义内容
    const hasUserContent = (
      // 用户CSS类（排除Tailwind的工具类）
      /\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/.test(content) &&
      !/\.(container|sm:|md:|lg:|xl:|2xl:)/.test(content) ||
      // 导入语句
      /@import\s+url/.test(content) ||
      // 具体颜色值（但排除Tailwind的默认颜色）
      /:\s*(?:rgb|rgba|#[0-9a-fA-F]{3,6})/.test(content) &&
      !/rgb\(59 130 246/.test(content) || // 排除Tailwind蓝色
      // 复杂CSS属性
      /(?:background|gradient|transform|transition|animation):\s*[^;]+/.test(content) &&
      !/-webkit-tap-highlight-color/.test(content) // 排除Tailwind的默认属性
    );

    // 🔧 更严格的判断逻辑
    const isTailwindStyle = (
      startsWithTailwindVars || // 以Tailwind变量开头
      matchCount >= 2 || // 匹配多个特征
      isLargeTailwindBlock // 大型Tailwind块
    );

    // 只有确定是Tailwind且不包含用户内容才返回true
    return isTailwindStyle && !hasUserContent;
  }

  // 🔧 新增：保留原始HTML结构的提取方法
  private static extractCleanHTML(targetDoc: Document): string {
    // 获取原始HTML内容（不包含动态生成的元素）
    const doctype = targetDoc.doctype;
    let doctypeString = '';

    if (doctype) {
      doctypeString = `<!DOCTYPE ${doctype.name}`;
      if (doctype.publicId) {
        doctypeString += ` PUBLIC "${doctype.publicId}"`;
      }
      if (doctype.systemId) {
        doctypeString += ` "${doctype.systemId}"`;
      }
      doctypeString += '>\n';
    }

    // 克隆文档以避免修改原始内容
    const clonedDoc = targetDoc.cloneNode(true) as Document;

    // 清理动态生成的元素
    this.cleanDynamicElements(clonedDoc);

    // 获取HTML元素的outerHTML
    const htmlContent = clonedDoc.documentElement.outerHTML;

    return doctypeString + htmlContent;
  }

  // 提取当前页面的完整HTML（包括自定义样式）- 修复版本，防止Tailwind污染
  static extractCurrentHTML(): string {
    // 优先从iframe中提取HTML（如果存在）
    const iframe = document.querySelector('iframe');
    let targetDoc = document;

    if (iframe && iframe.contentDocument) {
      targetDoc = iframe.contentDocument;
      console.log('📄 从iframe中提取HTML内容');
    } else {
      console.log('📄 从主文档中提取HTML内容');
    }

    // 🔧 在提取前先清理动态注入的Tailwind样式
    this.cleanDynamicTailwindStyles(targetDoc);

    // 🔧 使用新的清理方法提取HTML
    let rawHTML = this.extractCleanHTML(targetDoc);
    
    // 🔧 解析HTML以便进行样式处理
    const parser = new DOMParser();
    const parsedDoc = parser.parseFromString(rawHTML, 'text/html');

    // 确保自定义样式被包含
    const customCSS = StyleManager.exportCustomCSS();
    if (customCSS.trim()) {
      let styleTag = parsedDoc.querySelector('style#loomrun-custom-styles') as HTMLStyleElement;

      if (!styleTag) {
        styleTag = parsedDoc.createElement('style') as HTMLStyleElement;
        styleTag.id = 'loomrun-custom-styles';
        styleTag.type = 'text/css';

        let head = parsedDoc.querySelector('head');
        if (!head) {
          head = parsedDoc.createElement('head');
          parsedDoc.documentElement.insertBefore(head, parsedDoc.body);
        }
        head.appendChild(styleTag);
      }

      styleTag.textContent = customCSS;
      console.log('✅ 自定义样式已添加到HTML中', { cssLength: customCSS.length });
    }

    // 🔧 增强的临时属性和类清理
    this.cleanTemporaryAttributes(parsedDoc);

    // 🎯 关键修复：清理编辑模式的临时脚本和样式
    this.cleanEditModeElements(parsedDoc);

    // 🔧 重新构建完整的HTML（保留DOCTYPE）
    const doctype = parsedDoc.doctype;
    let finalHTML = '';

    if (doctype) {
      finalHTML += `<!DOCTYPE ${doctype.name}`;
      if (doctype.publicId) {
        finalHTML += ` PUBLIC "${doctype.publicId}"`;
      }
      if (doctype.systemId) {
        finalHTML += ` "${doctype.systemId}"`;
      }
      finalHTML += '>\n';
    }

    finalHTML += parsedDoc.documentElement.outerHTML;

    // 🔧 使用新的精准清理方法：只清理系统插入的内容
    finalHTML = TailwindCleaner.cleanSystemInsertedContent(finalHTML);

    // 🔧 新增：检测并修复错误的TailwindCSS引入方式
    const tailwindFix = TailwindCleaner.fixTailwindUsage(finalHTML);
    if (tailwindFix.wasFixed) {
      console.log('🔧 HTMLCodeIntegrator: 已修复TailwindCSS引入方式', tailwindFix.message);
      finalHTML = tailwindFix.html;
    }

    // 🔧 确保包含正确的Tailwind支持（添加带标记的CDN）
    if (!TailwindCleaner.hasTailwindSupport(finalHTML)) {
      finalHTML = TailwindCleaner.ensureSingleTailwindCDN(finalHTML);
    }

    // 🔧 统计自定义类的数量
    const elementsWithCustomClasses = parsedDoc.querySelectorAll('[class*="custom-"]');

    // 🔧 最终检查：强制确保关键标签不被压缩到一行
    finalHTML = this.ensureProperHTMLFormatting(finalHTML);

    console.log('📄 HTML提取和清理完成', {
      htmlLength: finalHTML.length,
      hasCustomStyles: customCSS.trim().length > 0,
      customClassCount: elementsWithCustomClasses.length,
      cleanedTailwind: true,
      preservedDoctype: !!doctype
    });

    return finalHTML;
  }

  // 🔧 完整的HTML格式化：确保结构正确和可读性
  private static ensureProperHTMLFormatting(html: string): string {
    // 🎯 关键修复：使用完整的结构化格式化，而不是简单的正则替换
    let formatted = TailwindCleaner.formatHTMLStructure(html);

    // 🎯 额外清理：移除空的style属性
    formatted = formatted.replace(/\s+style=""\s*/g, ' ');
    formatted = formatted.replace(/\s+style=""\s*>/g, '>');

    // 🎯 清理多余的空白和空行
    formatted = formatted
      .replace(/[ \t]+$/gm, '') // 移除行尾空白
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 最多保留一个空行
      .replace(/^\s+|\s+$/g, ''); // 移除开头和结尾的空白

    return formatted;
  }

  // 验证HTML结构完整性
  static validateHTMLStructure(html: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // 检查解析错误
      const parserErrors = doc.querySelectorAll('parsererror');
      if (parserErrors.length > 0) {
        errors.push('HTML解析错误');
      }
      
      // 检查基本结构
      if (!doc.querySelector('html')) {
        errors.push('缺少html标签');
      }
      
      if (!doc.querySelector('head')) {
        errors.push('缺少head标签');
      }
      
      if (!doc.querySelector('body')) {
        errors.push('缺少body标签');
      }
      
      // 检查自定义样式
      const customStyleTag = doc.querySelector('style#loomrun-custom-styles');
      if (customStyleTag && !customStyleTag.textContent?.trim()) {
        errors.push('自定义样式标签为空');
      }
      
    } catch (error) {
      errors.push(`HTML验证失败: ${error}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
} 