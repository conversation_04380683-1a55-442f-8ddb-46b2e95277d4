import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const debugInfo: any = {
      userId: user.id,
      userInfo: user,
      tests: []
    };

    // 1. 检查用户基本信息
    try {
      const userQuery = `
        SELECT 
          id, nickname, points, total_earned_points, total_spent_points, created_at
        FROM users 
        WHERE id = ?
      `;
      const userResult = await executeQuery(userQuery, [user.id]) as any[];
      
      debugInfo.tests.push({
        name: '用户基本信息',
        status: 'success',
        data: userResult[0] || null
      });
    } catch (error) {
      debugInfo.tests.push({
        name: '用户基本信息',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 2. 检查积分交易记录
    try {
      const transactionsQuery = `
        SELECT 
          id, transaction_type, points_amount, balance_before, balance_after,
          source_type, points_type, description, created_at
        FROM points_transactions 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
      `;
      const transactionsResult = await executeQuery(transactionsQuery, [user.id]) as any[];
      
      debugInfo.tests.push({
        name: '积分交易记录',
        status: 'success',
        count: transactionsResult.length,
        data: transactionsResult
      });
    } catch (error) {
      debugInfo.tests.push({
        name: '积分交易记录',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 3. 检查积分余额记录
    try {
      const balanceQuery = `
        SELECT 
          id, points_type, points_amount, expires_at, is_active, created_at
        FROM user_points_balance 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
      `;
      const balanceResult = await executeQuery(balanceQuery, [user.id]) as any[];
      
      debugInfo.tests.push({
        name: '积分余额记录',
        status: 'success',
        count: balanceResult.length,
        data: balanceResult
      });
    } catch (error) {
      debugInfo.tests.push({
        name: '积分余额记录',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 4. 检查系统设置
    try {
      const settingsQuery = `
        SELECT setting_key, setting_value, is_active
        FROM system_settings 
        WHERE setting_key LIKE '%points%' OR setting_key LIKE '%user%'
      `;
      const settingsResult = await executeQuery(settingsQuery, []) as any[];
      
      debugInfo.tests.push({
        name: '系统设置',
        status: 'success',
        count: settingsResult.length,
        data: settingsResult
      });
    } catch (error) {
      debugInfo.tests.push({
        name: '系统设置',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 5. 检查注册日志
    try {
      const registrationQuery = `
        SELECT 
          id, registration_type, points_awarded, created_at
        FROM registration_logs 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
      `;
      const registrationResult = await executeQuery(registrationQuery, [user.id]) as any[];
      
      debugInfo.tests.push({
        name: '注册日志',
        status: 'success',
        count: registrationResult.length,
        data: registrationResult
      });
    } catch (error) {
      debugInfo.tests.push({
        name: '注册日志',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    return NextResponse.json({
      success: true,
      data: debugInfo
    });

  } catch (error) {
    console.error("积分调试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
