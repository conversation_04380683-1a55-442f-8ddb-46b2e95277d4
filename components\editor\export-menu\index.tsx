"use client";

import React, { useState, useCallback } from 'react';
import { 
  Download, 
  ChevronDown, 
  Figma, 
  Image, 
  FileText, 
  Presentation,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import FigmaIntegration from '@/components/editor/figma-integration';

interface ExportMenuProps {
  htmlContent: string;
  projectTitle?: string;
  projectId?: number;
}

interface ExportItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  action: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export default function ExportMenu({ 
  htmlContent, 
  projectTitle = "LoomRun项目"
  // projectId暂时未使用
}: ExportMenuProps) {
  const [isFigmaModalOpen, setIsFigmaModalOpen] = useState(false);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  // 设置加载状态
  const setLoading = useCallback((itemId: string, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [itemId]: loading }));
  }, []);

  // 导出到Figma
  const handleExportToFigma = useCallback(() => {
    setIsFigmaModalOpen(true);
  }, []);

  // 导出为图片 (PNG/JPG)
  const handleExportToImage = useCallback(async () => {
    if (!htmlContent || htmlContent.trim().length === 0) {
      toast.error("没有可导出的内容");
      return;
    }

    setLoading('image', true);
    try {
      // 使用html2canvas或类似库导出为图片
      toast.info("图片导出功能开发中...");
      
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 实际实现时应该使用html2canvas
      // const canvas = await html2canvas(element);
      // const dataURL = canvas.toDataURL('image/png');
      // downloadFile(dataURL, `${projectTitle}.png`);
      
      toast.success("图片导出完成！");
    } catch (error) {
      console.error('图片导出失败:', error);
      toast.error("图片导出失败，请重试");
    } finally {
      setLoading('image', false);
    }
  }, [htmlContent, projectTitle]);

  // 导出为HTML文件
  const handleExportToHTML = useCallback(() => {
    if (!htmlContent || htmlContent.trim().length === 0) {
      toast.error("没有可导出的HTML内容");
      return;
    }

    try {
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${projectTitle}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("HTML文件导出成功！");
    } catch (error) {
      console.error('HTML导出失败:', error);
      toast.error("HTML导出失败，请重试");
    }
  }, [htmlContent, projectTitle]);

  // 导出为PPTX
  const handleExportToPPTX = useCallback(async () => {
    if (!htmlContent || htmlContent.trim().length === 0) {
      toast.error("没有可导出的内容");
      return;
    }

    setLoading('pptx', true);
    try {
      toast.info("PPTX导出功能开发中...");
      
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 实际实现时应该调用API转换为PPTX
      // const response = await fetch('/api/export/pptx', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ htmlContent, projectTitle })
      // });
      
      toast.success("PPTX导出完成！");
    } catch (error) {
      console.error('PPTX导出失败:', error);
      toast.error("PPTX导出失败，请重试");
    } finally {
      setLoading('pptx', false);
    }
  }, [htmlContent, projectTitle]);

  // 导出菜单项配置
  const exportItems: ExportItem[] = [
    {
      id: 'figma',
      label: '导出到 Figma',
      icon: <Figma className="w-4 h-4" />,
      description: '导出为Figma设计文件',
      action: handleExportToFigma,
      loading: loadingStates.figma,
    },
    {
      id: 'image',
      label: '导出为图片',
      icon: <Image className="w-4 h-4" />,
      description: '保存为PNG/JPG图片',
      action: handleExportToImage,
      loading: loadingStates.image,
    },
    {
      id: 'html',
      label: '导出为 HTML',
      icon: <FileText className="w-4 h-4" />,
      description: '下载HTML源码文件',
      action: handleExportToHTML,
      loading: loadingStates.html,
    },
    {
      id: 'pptx',
      label: '导出为 PPTX',
      icon: <Presentation className="w-4 h-4" />,
      description: '生成PowerPoint演示文稿',
      action: handleExportToPPTX,
      loading: loadingStates.pptx,
    },
  ];

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-7 px-3 text-xs bg-white dark:bg-neutral-800 border-gray-300 dark:border-neutral-600 text-gray-700 dark:text-neutral-300 hover:bg-gray-50 dark:hover:bg-neutral-700 hover:text-gray-900 dark:hover:text-white"
          >
            <Download className="w-3 h-3 mr-1.5" />
            导出
            <ChevronDown className="w-3 h-3 ml-1.5" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          align="end" 
          className="w-56 bg-white dark:bg-neutral-800 border-gray-300 dark:border-neutral-600"
        >
          <div className="px-2 py-1.5">
            <p className="text-xs font-medium text-gray-800 dark:text-neutral-200">导出选项</p>
            <p className="text-xs text-gray-600 dark:text-neutral-400">选择导出格式</p>
          </div>
          
          <DropdownMenuSeparator className="bg-gray-300 dark:bg-neutral-600" />
          
          {exportItems.map((item) => (
            <DropdownMenuItem
              key={item.id}
              onClick={item.action}
              disabled={item.disabled || item.loading}
              className="px-2 py-2 text-gray-800 dark:text-neutral-200 hover:bg-gray-100 dark:hover:bg-neutral-700 hover:text-gray-900 dark:hover:text-white cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex items-center gap-3 w-full">
                <div className="flex-shrink-0">
                  {item.loading ? (
                    <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                  ) : (
                    <div className="text-gray-600 dark:text-neutral-400">{item.icon}</div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium">{item.label}</div>
                  <div className="text-xs text-gray-600 dark:text-neutral-400 truncate">
                    {item.loading ? '处理中...' : item.description}
                  </div>
                </div>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Figma集成弹窗 - 简化版本 */}
      <FigmaIntegration
        isOpen={isFigmaModalOpen}
        onClose={() => setIsFigmaModalOpen(false)}
      />
    </>
  );
} 