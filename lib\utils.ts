import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const COLORS = [
  "red",
  "blue",
  "green",
  "yellow",
  "purple",
  "pink",
  "gray",
];

/**
 * 🚨 安全获取元素的className字符串
 * 处理DOMTokenList、SVGAnimatedString等不同类型的className
 * @param element HTML元素
 * @returns 安全的className字符串
 */
export function getSafeClassName(element: HTMLElement | Element): string {
  try {
    if (!element) return '';

    const className = element.className;

    // 如果已经是字符串，直接返回
    if (typeof className === 'string') {
      return className;
    }

    // 如果是DOMTokenList（现代浏览器）
    if (className && typeof className.toString === 'function') {
      return className.toString();
    }

    // 如果是SVGAnimatedString（SVG元素）
    if (className && typeof className === 'object' && 'baseVal' in className) {
      return (className as any).baseVal || '';
    }

    // 兜底方案
    return '';
  } catch (error) {
    console.warn('获取className失败:', error);
    return '';
  }
}

export const getPTag = (repoId: string) => {
  return `<p style="border-radius: 8px; text-align: center; font-size: 12px; color: #fff; margin-top: 16px;position: fixed; left: 8px; bottom: 8px; z-index: 10; background: rgba(0, 0, 0, 0.8); padding: 4px 8px;">Made with <img src="https://loomrun.top/favicon_io/favicon-32x32.png" alt="LoomRun Logo" style="width: 24px; height: 24px; vertical-align: middle;display:inline-block;margin-right:3px;filter:brightness(0) invert(1);"><a href="https://loomrun.top" style="color: #fff;text-decoration: underline;" target="_blank" >LoomRun</a> - 🚀 <a href="https://loomrun.top?remix=${repoId}" style="color: #fff;text-decoration: underline;" target="_blank" >Remix</a></p>`;
};

export const addFooter = (html: string, repoId: string) => {
  return `${html}<p style="position: fixed; bottom: 20px; right: 20px; margin: 0; z-index: 999999; font-size: 12px; color: white; background: linear-gradient(90deg, #1e3a8a, #7c3aed); padding: 8px 12px; border-radius: 20px; border: none; text-decoration: none; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">Made with <img src="https://loomrun.top/favicon_io/favicon-32x32.png" alt="LoomRun Logo" style="width: 24px; height: 24px; vertical-align: middle;display:inline-block;margin-right:3px;filter:brightness(0) invert(1);"><a href="https://loomrun.top" style="color: #fff;text-decoration: underline;" target="_blank" >LoomRun</a> - 🚀 <a href="https://loomrun.top?remix=${repoId}" style="color: #fff;text-decoration: underline;" target="_blank" >Remix</a></p>`;
};

/**
 * 格式化数字为专业显示格式
 * @param num 数字
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number): string {
  if (num < 1000) {
    return num.toString();
  } else if (num < 10000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  } else if (num < 100000000) {
    return (num / 10000).toFixed(1).replace(/\.0$/, '') + 'W';
  } else {
    return (num / 100000000).toFixed(1).replace(/\.0$/, '') + '亿';
  }
}

/**
 * 清理HTML内容中的危险代码，但保留必要的CSS框架
 * 🔧 修复：保留Tailwind CDN，只移除真正危险的代码
 */
export function sanitizeHtmlForProduction(html: string): string {
  let sanitized = html;
  
  // 1. 🔧 修复：保留Tailwind CDN，它对社区卡片预览是必需的
  // 不再移除Tailwind CDN，让样式正常工作
  
  // 2. 修复可能导致location重定义错误的代码
  sanitized = sanitized.replace(
    /Object\.defineProperty\s*\(\s*[^,]*location[^,]*,\s*["']location["']/gi,
    '// Object.defineProperty(location) - safely removed'
  );
  
  // 3. 移除可能导致重复定义的location操作
  sanitized = sanitized.replace(
    /window\.location\s*=\s*[^;]+;?/gi,
    '// window.location assignment removed'
  );
  
  // 4. 替换可能有问题的onclick事件，添加preventDefault
  sanitized = sanitized.replace(
    /onclick\s*=\s*["']([^"']*?)["']/gi,
    (match, content) => {
      if (!content.includes('preventDefault')) {
        return `onclick="event.preventDefault(); ${content}"`;
      }
      return match;
    }
  );
  
  // 5. 确保所有表单都有preventDefault
  sanitized = sanitized.replace(
    /<form([^>]*?)(?!onsubmit)/gi,
    '<form$1 onsubmit="event.preventDefault(); return false;"'
  );
  
  return sanitized;
}

/**
 * 为iframe内容添加安全防护脚本
 * �� 修复版本：解决跨域访问和重复定义问题，使用更安全的方式
 */
export function addIframeSafetyScript(html: string): string {
  const safetyScript = `
    <script>
      (function() {
        'use strict';
        
        // 🔧 修复：避免重复初始化
        if (window._loomrunSafetyInitialized) {
          console.log('🛡️ LoomRun安全脚本已初始化，跳过');
          return;
        }
        
        try {
          // 标记已初始化
          window._loomrunSafetyInitialized = true;
          
          // 🔧 安全地处理导航，避免重复定义错误
          const originalLocation = window.location.href;
          
          // 🔧 修复：检查属性是否可写，避免只读属性错误
          const safelyOverrideMethod = function(obj, prop, newFunc) {
            try {
              const descriptor = Object.getOwnPropertyDescriptor(obj, prop);
              if (descriptor && descriptor.writable === false) {
                console.log('⚠️ ' + prop + '是只读属性，跳过重写');
                return false;
              }
              
              if (typeof obj[prop] === 'function') {
                obj[prop] = newFunc;
                return true;
              }
            } catch(e) {
              console.log('⚠️ 无法重写' + prop + ':', e.message);
              return false;
            }
            return false;
          };
          
          // 安全地重写location方法
          if (window.location) {
            safelyOverrideMethod(window.location, 'assign', function() {
              console.log('🚫 location.assign被阻止');
              return false;
            });
            
            safelyOverrideMethod(window.location, 'replace', function() {
              console.log('🚫 location.replace被阻止');
              return false;
            });
            
            safelyOverrideMethod(window.location, 'reload', function() {
              console.log('🚫 location.reload被阻止');
              return false;
            });
          }
          
          // 阻止其他导航
          window.open = function() { 
            console.log('🚫 window.open被阻止');
            return null; 
          };
          
          // 安全地处理history API
          if (window.history) {
            const methods = ['pushState', 'replaceState', 'back', 'forward', 'go'];
            methods.forEach(function(method) {
              safelyOverrideMethod(window.history, method, function() {
                console.log('🚫 history.' + method + '被阻止');
                return false;
              });
            });
          }
          
          // 🔧 修复：使用更安全的事件处理方式
          document.addEventListener('click', function(e) {
            const target = e.target;
            const link = target.closest && target.closest('a');
            
            if (link) {
              const href = link.getAttribute('href');
              // 只允许锚点导航
              if (!href || href === '#' || href.startsWith('http') || href.startsWith('./') || href.startsWith('../')) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🚫 导航被阻止:', href);
                return false;
              }
            }
          }, { capture: true, passive: false });
          
          // 阻止表单提交
          document.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('🚫 表单提交被阻止');
            return false;
          }, { capture: true, passive: false });
          
          console.log('🛡️ LoomRun iframe安全防护已激活');
          
        } catch(e) {
          console.log('⚠️ 安全脚本初始化失败，但继续运行:', e.message);
        }
      })();
    </script>
  `;
  
  // 检查是否已经包含安全脚本
  if (html.includes('_loomrunSafetyInitialized')) {
    return html;
  }
  
  // 在</head>前插入安全脚本
  if (html.includes('</head>')) {
    return html.replace('</head>', safetyScript + '</head>');
  }
  
  // 如果没有</head>，在<body>前插入
  if (html.includes('<body')) {
    return html.replace('<body', safetyScript + '\n<body');
  }
  
  // 如果都没有，在开头插入
  return safetyScript + '\n' + html;
}
