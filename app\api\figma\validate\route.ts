import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 开始 Figma URL 验证...');
    
    // 解析请求数据
    const { url } = await request.json();
    
    if (!url || typeof url !== 'string') {
      console.error('❌ 无效的 Figma URL');
      return NextResponse.json(
        { 
          success: false,
          error: '请提供有效的 Figma URL' 
        },
        { status: 400 }
      );
    }

    console.log('🔍 验证 Figma URL:', url);

    // 验证 Figma URL 格式
    const figmaUrlPattern = /^https:\/\/(www\.)?figma\.com\/(file|proto|design)\/[a-zA-Z0-9]+/;
    if (!figmaUrlPattern.test(url)) {
      console.error('❌ URL 格式验证失败');
      return NextResponse.json(
        { 
          success: false,
          error: '无效的 Figma URL 格式。请确保 URL 来自 figma.com' 
        },
        { status: 400 }
      );
    }

    // 解析 URL 获取基本信息
    const urlMatch = url.match(/^https:\/\/(www\.)?figma\.com\/(file|proto|design)\/([a-zA-Z0-9]+)\/([^?]*)/);
    if (!urlMatch) {
      return NextResponse.json(
        { 
          success: false,
          error: '无法解析 Figma URL 信息' 
        },
        { status: 400 }
      );
    }

    const [, , urlType, fileId, fileName] = urlMatch;
    
    // 提取节点ID（如果有）
    const nodeIdMatch = url.match(/node-id=([^&]+)/);
    const nodeId = nodeIdMatch ? decodeURIComponent(nodeIdMatch[1]) : null;

    console.log('✅ URL 验证成功:', { fileId, nodeId, fileName, urlType });

    // 返回验证成功结果
    return NextResponse.json(
      {
        success: true,
        data: {
          fileId,
          nodeId,
          fileName: decodeURIComponent(fileName || 'Figma 设计'),
          urlType,
          originalUrl: url,
        },
        message: 'Figma URL 验证成功'
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('❌ Figma URL 验证失败:', error);
    
    let errorMessage = 'URL 验证失败，请重试';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
} 