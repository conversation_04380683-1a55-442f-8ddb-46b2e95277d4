"use client";
import { useUser } from "@/loomrunhooks/useUser";
import { useThemeLanguage } from "@/components/providers/theme-language-provider";
import { Project } from "@/types";
import { redirect } from "next/navigation";
import { ProjectCard } from "./project-card";
import { Plus, RefreshCw } from "lucide-react";
import Link from "next/link";
import { useEffect, useState, useCallback, useRef } from "react";
import { unifiedCache } from "@/lib/unified-cache";

interface MyProjectsProps {
  projects?: Project[]; // 🔧 改为可选，支持客户端动态加载
}

export function MyProjects({ projects: initialProjects = [] }: MyProjectsProps) {
  const { user } = useUser();
  const { language } = useThemeLanguage();
  const [projects, setProjects] = useState<Project[]>(initialProjects);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshing = useRef<boolean>(false);

  if (!user) {
    redirect("/");
  }

  // 🎯 高性能：智能项目列表获取函数
  const fetchProjects = useCallback(async (forceRefresh = false) => {
    if (isRefreshing.current && !forceRefresh) {
      console.log('📋 MyProjects: 正在刷新中，跳过重复请求');
      return;
    }

    const cacheKey = 'user-projects-list';
    
         // 🔧 优先从缓存获取，提升性能
     if (!forceRefresh && user?.id) {
       try {
         const cachedProjects = await unifiedCache.getProjectList(parseInt(user.id), false);
         if (cachedProjects && cachedProjects.length > 0) {
           console.log('✅ MyProjects: 从缓存加载项目列表', { count: cachedProjects.length });
           setProjects(cachedProjects as Project[]);
           return;
         }
       } catch (error) {
         console.log('⚠️ MyProjects: 缓存获取失败，继续网络请求', error);
       }
     }

    try {
      isRefreshing.current = true;
      setIsLoading(true);
      
      console.log('🚀 MyProjects: 获取项目列表', { forceRefresh, cacheKey });
      
      const response = await fetch('/api/me/projects', {
        headers: {
          'Cache-Control': forceRefresh ? 'no-cache' : 'max-age=30', // 30秒缓存
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const newProjects = data.projects || [];
      
      console.log('✅ MyProjects: 项目列表获取成功', { 
        count: newProjects.length,
        previousCount: projects.length,
        isNewProject: newProjects.length > projects.length
      });
      
      // 🔧 更新状态和缓存
      setProjects(newProjects);
      setLastRefresh(Date.now());
      
             // 🎯 高效缓存：项目列表缓存已在unifiedCache内部处理
      
    } catch (error) {
      console.error('❌ MyProjects: 获取项目列表失败:', error);
    } finally {
      setIsLoading(false);
      isRefreshing.current = false;
    }
  }, [projects.length]);

  // 🎯 智能刷新：监听项目创建事件
  const handleProjectCreated = useCallback(() => {
    console.log('📢 MyProjects: 监听到项目创建事件，准备刷新列表');
    
    // 🔧 防抖：延迟1秒刷新，避免重复调用
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    
    refreshTimeoutRef.current = setTimeout(() => {
      fetchProjects(true); // 强制刷新
    }, 1000);
  }, [fetchProjects]);

  // 🎯 立即添加新项目：不等待刷新，直接添加到列表
  const handleProjectCreatedInstant = useCallback((projectData: { projectId: string; title?: string; html_content?: string }) => {
    console.log('⚡ MyProjects: 立即添加新项目到列表', projectData);
    
    // 🔧 创建新项目对象（符合Project类型）
    const newProject: Project = {
      id: parseInt(projectData.projectId),
      _id: projectData.projectId,
      title: projectData.title || '新项目',
      html_content: projectData.html_content || '<div>正在生成...</div>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      prompts: [],
      user_id: parseInt(user?.id || '0')
    };
    
    // 🚀 立即添加到项目列表顶部
    setProjects(prevProjects => {
      // 检查是否已存在，避免重复添加
      const exists = prevProjects.some(p => p.id === newProject.id);
      if (exists) {
        return prevProjects;
      }
      return [newProject, ...prevProjects];
    });
    
    // 🔧 后台异步刷新完整数据
    setTimeout(() => {
      fetchProjects(true);
    }, 2000);
  }, [fetchProjects, user?.id]);

  // 🔧 组件挂载时加载项目列表
  useEffect(() => {
    if (initialProjects.length === 0) {
      fetchProjects(false);
    }
  }, [fetchProjects, initialProjects.length]);

  // 🎯 监听项目创建事件（多种方式确保可靠性）
  useEffect(() => {
    // 🔧 方式1：BroadcastChannel（现代浏览器）
    let channel: BroadcastChannel | null = null;
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      channel = new BroadcastChannel('project-updates');
      
      channel.addEventListener('message', (event) => {
        console.log('📡 MyProjects: 收到广播消息', event.data);
        if (event.data.type === 'project-created') {
          console.log('📡 MyProjects: 收到项目创建广播消息', event.data);
          handleProjectCreatedInstant(event.data);
          handleProjectCreated();
        }
      });
    }
    
    // 🔧 方式2：localStorage事件（兼容性更好）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'project-created-event' && e.newValue) {
        console.log('📡 MyProjects: 收到项目创建存储事件', e.newValue);
        try {
          const projectData = JSON.parse(e.newValue);
          handleProjectCreatedInstant(projectData);
        } catch (error) {
          console.error('❌ MyProjects: 解析项目数据失败', error);
        }
        handleProjectCreated();
        // 清除事件标记
        setTimeout(() => {
          localStorage.removeItem('project-created-event');
        }, 100);
      }
    };
    
    // 🔧 方式3：自定义事件（同页面通信）
    const handleCustomEvent = (event: CustomEvent) => {
      console.log('📡 MyProjects: 收到自定义刷新事件', event.detail);
      if (event.detail?.projectId) {
        handleProjectCreatedInstant(event.detail);
      }
      handleProjectCreated();
    };
    
    // 🔧 方式4：定时检查标记（兜底方案）
    const checkInterval = setInterval(() => {
      const refreshTrigger = localStorage.getItem('project-list-refresh-trigger');
      if (refreshTrigger) {
        console.log('📡 MyProjects: 检测到刷新触发标记', refreshTrigger);
        handleProjectCreated();
        localStorage.removeItem('project-list-refresh-trigger');
      }
      
      // 🔧 同时检查项目创建事件（localStorage事件在同页面内不会触发）
      const projectCreatedEvent = localStorage.getItem('project-created-event');
      if (projectCreatedEvent) {
        console.log('📡 MyProjects: 检测到项目创建事件', projectCreatedEvent);
        try {
          const projectData = JSON.parse(projectCreatedEvent);
          handleProjectCreatedInstant(projectData);
        } catch (error) {
          console.error('❌ MyProjects: 解析项目数据失败', error);
        }
        handleProjectCreated();
        localStorage.removeItem('project-created-event');
      }
    }, 500);
    
    // 添加事件监听
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('refreshProjectList', handleCustomEvent as EventListener);
    
    return () => {
      // 清理所有监听器
      if (channel) {
        channel.close();
      }
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('refreshProjectList', handleCustomEvent as EventListener);
      clearInterval(checkInterval);
    };
  }, [handleProjectCreated, handleProjectCreatedInstant]);

  // 🔧 定期刷新：每2分钟检查一次新项目
  useEffect(() => {
    const interval = setInterval(() => {
      const timeSinceLastRefresh = Date.now() - lastRefresh;
      if (timeSinceLastRefresh > 2 * 60 * 1000) { // 2分钟
        console.log('⏰ MyProjects: 定期刷新项目列表');
        fetchProjects(false);
      }
    }, 2 * 60 * 1000);

    return () => clearInterval(interval);
  }, [lastRefresh, fetchProjects]);

  // 🧹 清理定时器
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // 🎯 手动刷新按钮
  const handleManualRefresh = useCallback(() => {
    console.log('🔄 MyProjects: 手动刷新项目列表');
    fetchProjects(true);
  }, [fetchProjects]);

  return (
    <>
      <section className="max-w-[86rem] py-12 px-4 mx-auto">
        <div className="text-left">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">
                <span className="capitalize">{user.fullname}</span>&apos;s LoomRun
                Projects
              </h1>
              <p className="text-neutral-400 text-sm">
                Create, manage, and explore your LoomRun projects.
              </p>
            </div>
            
            {/* 🎯 刷新按钮 */}
            <button
              onClick={handleManualRefresh}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-neutral-800 hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed text-neutral-300 rounded-lg transition-all duration-200"
              title="刷新项目列表"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? '刷新中...' : '刷新'}
            </button>
          </div>
        </div>
        
        <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <Link
            href="/projects/new"
            className="bg-neutral-900 rounded-xl h-44 flex items-center justify-center text-neutral-300 border border-neutral-800 hover:brightness-110 transition-all duration-200"
          >
            <Plus className="size-5 mr-1.5" />
            {language === 'en' ? 'New Project' : '新建项目'}
          </Link>
          
          {/* 🔧 优化：使用key确保正确渲染 */}
          {projects.map((project: Project) => (
            <ProjectCard key={`${project._id}-${lastRefresh}`} project={project} />
          ))}
          
          {/* 🎯 加载状态指示器 */}
          {isLoading && projects.length === 0 && (
            <div className="col-span-full flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-neutral-400">
                <RefreshCw className="w-5 h-5 animate-spin" />
                <span>加载项目中...</span>
              </div>
            </div>
          )}
          
          {/* 🎯 空状态 */}
          {!isLoading && projects.length === 0 && (
            <div className="col-span-full flex items-center justify-center py-12">
              <div className="text-center text-neutral-400">
                <p className="text-lg mb-2">还没有项目</p>
                <p className="text-sm">点击&ldquo;新建项目&rdquo;开始创建您的第一个项目</p>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
}
