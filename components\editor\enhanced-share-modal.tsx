"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Share2, 
  Globe, 
  Users,
  Code, 
  Camera, 
  Loader2,
  Gamepad2,
  BookOpen,
  Smartphone,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { ScreenshotCapture, isScreenshotSupported } from "@/lib/screenshot-utils";

interface EnhancedShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  htmlContent: string;
  projectTitle: string;
}

// 项目分类选项
const PROJECT_CATEGORIES = [
  { value: 'sites', label: '网站', icon: Globe },
  { value: 'games', label: '游戏', icon: Gamepad2 },
  { value: 'ppt', label: 'PPT', icon: BookOpen },
  { value: 'apps', label: '手机应用', icon: Smartphone },
  { value: 'tools', label: '工具', icon: Settings },
  { value: 'system', label: '系统', icon: Code },
];

const EnhancedShareModal = React.memo(({ 
  isOpen, 
  onClose, 
  projectId, 
  htmlContent, 
  projectTitle 
}: EnhancedShareModalProps) => {
  const [isSharing, setIsSharing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('sites');
  const [isCapturing, setIsCapturing] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [screenshotCapture, setScreenshotCapture] = useState<ScreenshotCapture | null>(null);
  const [cachedHtmlHash, setCachedHtmlHash] = useState<string>('');

  // 在客户端初始化截图工具 - 性能优化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 🚀 延迟初始化，避免阻塞渲染
      const timer = setTimeout(() => {
        setScreenshotCapture(new ScreenshotCapture());
      }, 100);
      return () => clearTimeout(timer);
    }
  }, []);

  // 🚀 性能优化的智能截图
  const handleAutoCapture = useCallback(async () => {
    if (!screenshotCapture) {
      toast.error('截图工具尚未初始化');
      return;
    }

    if (!isScreenshotSupported()) {
      toast.error('您的浏览器不支持截图功能');
      return;
    }

    // 🔥 缓存机制：检查内容是否变化
    // 使用 TextEncoder 和 crypto API 创建 UTF-8 安全的哈希
    const encoder = new TextEncoder();
    const data = encoder.encode(htmlContent);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const currentHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('').slice(0, 32);
    
    if (cachedHtmlHash === currentHash && previewImageUrl) {
      toast.success('使用缓存的截图');
      return;
    }

    setIsCapturing(true);
    const startTime = performance.now();
    
    try {
      // 🔥 智能截图配置 - 自适应内容高度
      const config = {
        width: 1200,        // 提升分辨率确保清晰度
        height: undefined,   // 不限制高度，让内容自然扩展
        quality: 0.9,       // 提升质量确保清晰
        format: 'webp' as const, // 使用WebP格式，更小文件
        devicePixelRatio: 1.5,   // 保持良好的显示效果
        delay: 1200,        // 增加等待时间确保内容完全加载
        backgroundColor: '#ffffff',
        // 🚀 优化CSS，确保完整内容显示
        extraCSS: `
          * { 
            box-sizing: border-box !important;
            transition: none !important;
            animation: none !important;
          }
          body { 
            margin: 0 !important; 
            padding: 20px !important;
            font-family: system-ui, sans-serif !important;
            background: #ffffff !important;
            overflow: visible !important;
            min-height: auto !important;
            height: auto !important;
          }
          img { 
            max-width: 100% !important; 
            height: auto !important;
            object-fit: cover !important;
          }
          .container, .main, .content {
            max-width: 100% !important;
            margin: 0 auto !important;
            min-height: auto !important;
            height: auto !important;
          }
          html, body {
            width: 100% !important;
            height: auto !important;
            overflow: visible !important;
          }
        `
      };
      
      // 🚀 并行处理：预加载水印配置
      const watermarkConfig = { 
        position: 'bottom-right' as const, 
        opacity: 0.6,
        fontSize: 12,
        fontFamily: 'system-ui, sans-serif',
        color: '#888888',
        padding: 6
      };
      
      // 🎯 单次截图+水印处理
      const result = await screenshotCapture.captureFromHtml(htmlContent, config);
      
      if (result.dataUrl) {
        // 🚀 快速水印处理
        const watermarkedResult = await screenshotCapture.addWatermark(
          result.dataUrl,
          'LoomRun',
          watermarkConfig
        );
        
        setPreviewImageUrl(watermarkedResult.dataUrl);
        setCachedHtmlHash(currentHash); // 更新缓存
        
        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        console.log(`✅ 截图完成，耗时: ${duration}ms`);
        toast.success(`截图生成成功 (${duration}ms)`);
      } else {
        throw new Error('截图生成失败');
      }
    } catch (error) {
      console.error('智能截图失败:', error);
      toast.error(`截图失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsCapturing(false);
    }
  }, [htmlContent, screenshotCapture, cachedHtmlHash, previewImageUrl]);

  // 提取HTML中的标题
  useEffect(() => {
    if (isOpen && htmlContent) {
      handleAutoCapture();
    }
  }, [isOpen, htmlContent, handleAutoCapture]);

  // 分享到社区
  const handleShare = useCallback(async () => {
    if (!previewImageUrl) {
      toast.error('请等待系统自动截图完成');
      return;
    }

    if (!selectedCategory) {
      toast.error('请选择项目分类');
      return;
    }

    setIsSharing(true);
    try {
      const formData = new FormData();
      formData.append('projectId', projectId);
      formData.append('htmlContent', htmlContent);
      formData.append('title', projectTitle); // 使用系统自动读取的标题
      formData.append('category', selectedCategory);
      
      // 将截图转换为blob并添加到formData
      const response = await fetch(previewImageUrl);
      const blob = await response.blob();
      formData.append('previewImage', blob, 'preview.png');
      formData.append('previewImageType', 'auto');

      const shareResponse = await fetch('/api/community/share', {
        method: 'POST',
        body: formData,
      });

      if (shareResponse.ok) {
        toast.success('🎉 项目已成功分享到社区！');
        onClose();
        
        // 跳转到社区页面并高亮显示新项目
        const result = await shareResponse.json();
        if (result.communityProjectId) {
          setTimeout(() => {
            window.location.href = `/?tab=community&highlight=${result.communityProjectId}`;
          }, 1000);
        }
      } else {
        const error = await shareResponse.text();
        throw new Error(error || '分享失败');
      }
    } catch (error) {
      console.error('分享失败:', error);
      toast.error(error instanceof Error ? error.message : '分享失败，请稍后重试');
    } finally {
      setIsSharing(false);
    }
  }, [projectId, htmlContent, projectTitle, selectedCategory, previewImageUrl, onClose]);

  // 重新截图
  const handleRetryCapture = useCallback(() => {
    setPreviewImageUrl(null);
    setCachedHtmlHash(''); // 清除缓存
    handleAutoCapture();
  }, [handleAutoCapture]);

  return (
          <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-xl w-full max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5 text-blue-500" />
            分享到社区
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 项目信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">项目标题</label>
              <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                {projectTitle}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">分类</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_CATEGORIES.map((category) => {
                    const Icon = category.icon;
                    return (
                      <SelectItem key={category.value} value={category.value}>
                        <div className="flex items-center gap-2">
                          <Icon className="w-4 h-4" />
                          <span>{category.label}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 预览图 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">预览图</h3>
              {previewImageUrl && (
                <Button
                  onClick={handleRetryCapture}
                  variant="outline"
                  size="sm"
                  disabled={isCapturing}
                >
                  重新生成
                </Button>
              )}
            </div>
            
            <div className="border rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800 aspect-[5/3] flex items-center justify-center">
              {isCapturing ? (
                <div className="flex flex-col items-center gap-2 text-gray-500">
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <p className="text-sm">生成中...</p>
                </div>
              ) : previewImageUrl ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={previewImageUrl}
                  alt="预览图"
                  className="w-full h-full object-cover rounded"
                />
              ) : (
                <div className="flex flex-col items-center gap-2 text-gray-400">
                  <Camera className="w-6 h-6" />
                  <Button
                    onClick={handleAutoCapture}
                    variant="outline"
                    size="sm"
                  >
                    生成预览图
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* 提示 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-sm text-blue-700 dark:text-blue-300">
            分享后项目将在社区公开展示，请确保内容健康有价值
          </div>

          {/* 分享按钮 */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} disabled={isSharing}>
              取消
            </Button>
            <Button 
              onClick={handleShare} 
              disabled={isSharing || isCapturing || !previewImageUrl}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSharing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  分享中...
                </>
              ) : (
                <>
                  <Users className="w-4 h-4 mr-2" />
                  分享到社区
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

EnhancedShareModal.displayName = 'EnhancedShareModal';

export { EnhancedShareModal }; 