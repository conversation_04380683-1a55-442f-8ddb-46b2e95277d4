import { executeQuery } from '@/lib/mysql';
import { getNumberSetting } from '@/lib/points-service';

// 处理过期订单的定时任务
export const expireOldOrders = async (): Promise<{ 
  expiredSubscriptionOrders: number; 
  expiredRechargeOrders: number; 
}> => {
  try {
    // 获取订单过期时间设置
    const expireMinutes = await getNumberSetting('order_expire_minutes', 30);
    const expireTime = new Date(Date.now() - expireMinutes * 60 * 1000);

    console.log(`🕒 开始处理过期订单，过期时间: ${expireTime.toISOString()}`);

    // 处理过期的订阅订单
    const subscriptionResult = await executeQuery(
      `UPDATE membership_orders 
       SET status = 'expired', updated_at = NOW() 
       WHERE status = 'pending' AND created_at < ?`,
      [expireTime]
    ) as any;

    // 处理过期的充值订单
    const rechargeResult = await executeQuery(
      `UPDATE recharge_orders 
       SET status = 'expired', updated_at = NOW() 
       WHERE status = 'pending' AND created_at < ?`,
      [expireTime]
    ) as any;

    const expiredSubscriptionOrders = subscriptionResult.affectedRows || 0;
    const expiredRechargeOrders = rechargeResult.affectedRows || 0;

    if (expiredSubscriptionOrders > 0 || expiredRechargeOrders > 0) {
      console.log(`✅ 订单过期处理完成: 订阅订单 ${expiredSubscriptionOrders} 个, 充值订单 ${expiredRechargeOrders} 个`);
    }

    return {
      expiredSubscriptionOrders,
      expiredRechargeOrders
    };

  } catch (error) {
    console.error('❌ 处理过期订单失败:', error);
    throw error;
  }
};

// 获取订单详情
export const getOrderDetails = async (orderId: string, userId: number) => {
  try {
    // 先查找订阅订单
    const subscriptionOrders = await executeQuery(
      `SELECT mo.*, sp.plan_name, sp.points_included, sp.points_validity_days
       FROM membership_orders mo
       LEFT JOIN subscription_plans sp ON mo.plan_key = sp.plan_key
       WHERE mo.id = ? AND mo.user_id = ?`,
      [orderId, userId]
    ) as any[];

    if (subscriptionOrders.length > 0) {
      return {
        ...subscriptionOrders[0],
        orderType: 'subscription'
      };
    }

    // 再查找充值订单
    const rechargeOrders = await executeQuery(
      `SELECT ro.*, pp.package_name
       FROM recharge_orders ro
       LEFT JOIN points_packages pp ON ro.package_key = pp.package_key
       WHERE ro.id = ? AND ro.user_id = ?`,
      [orderId, userId]
    ) as any[];

    if (rechargeOrders.length > 0) {
      return {
        ...rechargeOrders[0],
        orderType: 'recharge'
      };
    }

    return null;
  } catch (error) {
    console.error('获取订单详情失败:', error);
    throw error;
  }
};

// 取消订单（用于重新下单时）
export const cancelOrder = async (orderId: string, userId: number, orderType: 'subscription' | 'recharge') => {
  try {
    let query: string;
    if (orderType === 'subscription') {
      query = `UPDATE membership_orders 
               SET status = 'cancelled', updated_at = NOW() 
               WHERE id = ? AND user_id = ? AND status = 'pending'`;
    } else {
      query = `UPDATE recharge_orders 
               SET status = 'cancelled', updated_at = NOW() 
               WHERE id = ? AND user_id = ? AND status = 'pending'`;
    }

    const result = await executeQuery(query, [orderId, userId]) as any;
    return result.affectedRows > 0;
  } catch (error) {
    console.error('取消订单失败:', error);
    throw error;
  }
};

// 检查订单状态
export const checkOrderStatus = async (orderId: string, userId: number) => {
  try {
    // 先查找订阅订单
    const subscriptionOrders = await executeQuery(
      'SELECT id, order_no, status, created_at, order_expires_at FROM membership_orders WHERE id = ? AND user_id = ?',
      [orderId, userId]
    ) as any[];

    if (subscriptionOrders.length > 0) {
      return {
        ...subscriptionOrders[0],
        orderType: 'subscription'
      };
    }

    // 再查找充值订单
    const rechargeOrders = await executeQuery(
      'SELECT id, order_no, status, created_at, order_expires_at FROM recharge_orders WHERE id = ? AND user_id = ?',
      [orderId, userId]
    ) as any[];

    if (rechargeOrders.length > 0) {
      return {
        ...rechargeOrders[0],
        orderType: 'recharge'
      };
    }

    return null;
  } catch (error) {
    console.error('检查订单状态失败:', error);
    throw error;
  }
};
