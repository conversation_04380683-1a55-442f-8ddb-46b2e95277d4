"use client";

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Loader2, ExternalLink, Download, CheckCircle, AlertCircle, Globe, Rocket } from 'lucide-react';

interface DeployModalProps {
  isOpen: boolean;
  onClose: () => void;
  htmlContent: string;
}

interface DeployResponse {
  success: boolean;
  url?: string;
  projectName?: string;
  platform?: string;
  message?: string;
  features?: string[];
  chinaAccess?: {
    accessible: boolean;
    note: string;
  };
  wechatSharing?: {
    hasCustomDomain: boolean;
    domain: string;
    sslWarning?: {
      issue: string;
      description: string;
      solution: string;
    } | null;
    solutions: Array<{
      title: string;
      desc: string;
      steps: string[];
      cost: string;
    }>;
    tips?: string[];
  };
  nextSteps?: string[];
  error?: string;
  errorDetails?: string;
  manualDeployment?: {
    title: string;
    description: string;
    downloadContent: string;
    platforms: Array<{
      name: string;
      steps: string[];
      url: string;
      features: string[];
    }>;
  };
  troubleshooting?: {
    title: string;
    commonIssues: Array<{
      issue: string;
      solution: string;
    }>;
  };
  setupInstructions?: {
    title: string;
    steps: string[];
  };
}

export default function DeployModal({ isOpen, onClose, htmlContent }: DeployModalProps) {
  const [projectName, setProjectName] = useState('');
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployResult, setDeployResult] = useState<DeployResponse | null>(null);

  const handleDeploy = async () => {
    if (!projectName.trim()) {
      toast.error("请输入项目名称");
      return;
    }

    setIsDeploying(true);
    
    try {
      const response = await fetch('/api/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent,
          projectName: projectName.trim(),
        }),
      });

      const result: DeployResponse = await response.json();
      setDeployResult(result);

                            if (result.success) {
        toast.success("网站发布成功，现在可以分享了");
      } else {
        toast.error(result.message || "发布失败，请查看详细信息");
      }
    } catch (error) {
      console.error('发布错误:', error);
      toast.error("网络错误，请检查网络连接后重试");
    } finally {
      setIsDeploying(false);
    }
  };

  const handleClose = () => {
    setDeployResult(null);
    setProjectName('');
    setIsDeploying(false);
    onClose();
  };

  const downloadHTML = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const validateProjectName = (name: string) => {
    // 检查是否包含中文或其他非ASCII字符
    const hasNonAscii = /[^\x00-\x7F]/.test(name);
    const hasValidChars = /^[a-zA-Z0-9\-\s]+$/.test(name);
    return !hasNonAscii && hasValidChars;
  };

  const getInputClassName = () => {
    if (!projectName) return '';
    return validateProjectName(projectName) 
      ? 'border-green-500 focus:border-green-500' 
      : 'border-red-500 focus:border-red-500';
  };



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-xs w-full max-h-[90vh] p-4 sm:p-5 rounded-3xl border-0 shadow-2xl bg-gradient-to-br from-orange-100 to-yellow-100 dark:from-orange-900 dark:to-yellow-900 overflow-hidden">
        <DialogTitle className="sr-only">发布您的应用</DialogTitle>
        <div className="text-center space-y-2 sm:space-y-3 h-full flex flex-col justify-center">
          {!deployResult && (
            <>
              {/* 标题 */}
              <div className="mb-1 sm:mb-2">
                <h1 className="text-xl sm:text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1 flex items-center justify-center gap-2">
                  <Rocket className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600 dark:text-orange-400" />
                  发布您的应用
                </h1>
              </div>

              {/* 中央图标 */}
              <div className="flex items-center justify-center mb-1 sm:mb-2">
                <div className="w-12 h-12 sm:w-14 sm:h-14">
                  <svg viewBox="0 0 100 100" className="w-full h-full text-orange-500 dark:text-orange-400">
                    <image href="/android-chrome-192x192.png" width="100" height="100" />
                  </svg>
                </div>
              </div>

              {/* 描述 */}
              <div className="space-y-1 mb-1 sm:mb-2">
                <p className="text-slate-700 dark:text-slate-200 text-xs sm:text-sm leading-relaxed px-1">
                  🌐 织就您的代码逻辑，一键运行至云端宇宙
                </p>
                <p className="text-slate-600 dark:text-slate-300 text-xs sm:text-sm leading-relaxed px-1">
                  与全宇宙更好地分享您的应用 ✨
                </p>
              </div>

              {/* 输入提示 */}
              <div className="mb-1">
                <p className="text-slate-800 dark:text-slate-200 text-sm sm:text-base font-semibold mb-1">
                  💫 给您的应用取一个帅气的英文名称
                </p>
              </div>

              {/* 输入框 */}
              <div className="space-y-1 mb-1">
                <Input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="awesome-app"
                  className={`${getInputClassName()} text-center border-2 border-orange-300 dark:border-orange-600 rounded-xl px-3 py-2 sm:py-2.5 text-sm sm:text-base bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200`}
                  disabled={isDeploying}
                />
                {projectName && !validateProjectName(projectName) && (
                  <p className="text-xs sm:text-sm text-red-600 flex items-center justify-center gap-1">
                    <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4" />
                    ⚠️ 仅英文、数字、连字符
                  </p>
                )}
              </div>

              {/* 提示文字 */}
              <p className="text-slate-700 dark:text-slate-300 text-sm sm:text-base font-semibold mb-1">
                🎯 那么现在开始发布吧！
              </p>

              {/* 部署按钮 */}
              <Button 
                onClick={handleDeploy} 
                disabled={isDeploying || !projectName.trim() || !validateProjectName(projectName)}
                className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white py-2.5 sm:py-3 rounded-xl font-bold text-sm sm:text-base shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                {isDeploying ? (
                  <>
                    <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 mr-2 animate-spin" />
                    🚀 发布中...
                  </>
                ) : (
                  <>
                    ⚡ 开始发布 <Rocket className="w-3 h-3 sm:w-4 sm:h-4 ml-2" />
                  </>
                )}
              </Button>
            </>
          )}

          {/* 部署结果显示 */}
          {deployResult && (
            <>
              {deployResult.success ? (
                // 成功状态
                <>
                  {/* 成功图标 */}
                  <div className="flex items-center justify-center mb-1 sm:mb-2">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 dark:from-emerald-500 dark:to-green-600 flex items-center justify-center shadow-lg">
                      <CheckCircle className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                    </div>
                  </div>

                  {/* 成功标题和描述 */}
                  <div className="space-y-1 mb-2">
                    <h2 className="text-lg sm:text-xl font-bold text-slate-900 dark:text-slate-100">
                      🎉 发布成功！
                    </h2>
                    <p className="text-slate-700 dark:text-slate-300 text-xs sm:text-sm leading-relaxed px-2">
                      ✨ 应用已上线，可以访问和分享了
                    </p>
                  </div>

                  {/* 域名展示区域 */}
                  <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl p-3 mb-2 border border-emerald-200 dark:border-emerald-700">
                    <div className="space-y-2 sm:space-y-0 sm:flex sm:items-center sm:justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">应用地址</p>
                        <p className="text-xs sm:text-sm font-mono text-slate-800 dark:text-slate-200 break-all sm:truncate">
                          {deployResult.url}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(deployResult.url || '');
                          toast.success('🎯 链接已复制，快分享给好友吧！');
                        }}
                        className="w-full sm:w-auto sm:ml-3 h-8 px-3 border-emerald-300 text-emerald-700 hover:bg-emerald-50 dark:border-emerald-600 dark:text-emerald-300 dark:hover:bg-emerald-900/20 transition-all duration-200 text-xs sm:text-sm"
                      >
                        📋 复制
                      </Button>
                    </div>
                  </div>

                  {/* 操作按钮 - 左右布局 */}
                  <div className="flex gap-2 sm:gap-3">
                    <Button
                      onClick={() => {
                        if (navigator.share) {
                          navigator.share({
                            title: '看看我创建的应用',
                            text: '我用LoomRun编织了一个数字作品，见证编织美学与暴力效率的完美融合！',
                            url: deployResult.url,
                          });
                        } else {
                          navigator.clipboard.writeText(deployResult.url || '');
                          toast.success('🎯 链接已复制成功，发给您的好友吧！');
                        }
                      }}
                      variant="outline"
                      className="flex-1 border-2 border-emerald-300 dark:border-emerald-600 text-emerald-700 dark:text-emerald-300 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 py-2.5 sm:py-3 rounded-xl font-bold text-sm sm:text-base backdrop-blur-sm transition-all duration-200"
                    >
                      <Globe className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                      📤 分享
                    </Button>
                    
                    <Button
                      onClick={() => window.open(deployResult.url, '_blank')}
                      className="flex-1 bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white py-2.5 sm:py-3 rounded-xl font-bold text-sm sm:text-base shadow-lg transform hover:scale-105 transition-all duration-200"
                    >
                      <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                      🌍 访问
                    </Button>
                  </div>
                </>
              ) : (
                // 失败状态
                <>
                  {/* 失败图标 */}
                  <div className="flex items-center justify-center mb-1 sm:mb-2">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl bg-gradient-to-br from-orange-400 to-red-500 dark:from-orange-500 dark:to-red-600 flex items-center justify-center shadow-lg">
                      <AlertCircle className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                    </div>
                  </div>

                  {/* 失败标题和描述 */}
                  <div className="space-y-1 mb-2">
                    <h2 className="text-lg sm:text-xl font-bold text-slate-900 dark:text-slate-100">
                      ⚠️ 发布失败
                    </h2>
                    <p className="text-slate-700 dark:text-slate-300 text-xs sm:text-sm leading-relaxed px-2">
                      🔧 {deployResult.message || '发布过程中出现问题，请重试'}
                    </p>
                  </div>

                  {/* 手动部署选项 */}
                  {deployResult.manualDeployment && (
                    <Button
                      onClick={() => downloadHTML(
                        deployResult.manualDeployment!.downloadContent,
                        `${projectName || 'app'}.html`
                      )}
                      className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white py-2.5 sm:py-3 rounded-xl font-bold text-sm sm:text-base shadow-lg transform hover:scale-105 transition-all duration-200"
                    >
                      <Download className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                      💾 下载应用文件
                    </Button>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}