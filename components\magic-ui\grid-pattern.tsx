import { useId } from "react";
import { cn } from "@/lib/utils";

interface GridPatternProps extends React.SVGProps<SVGSVGElement> {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  squares?: Array<[x: number, y: number]>;
  strokeDasharray?: string;
  className?: string;
  [key: string]: unknown;
}

export function GridPattern({
  width = 40,
  height = 40,
  x = -1,
  y = -1,
  strokeDasharray = "0",
  squares,
  className,
  ...props
}: GridPatternProps) {
  const id = useId();
  const gradientId = `gradient-${id}`;
  const glowId = `glow-${id}`;
  const lightGradientId = `light-gradient-${id}`;
  const lightGlowId = `light-glow-${id}`;

  return (
    <svg
      aria-hidden="true"
      className={cn(
        "pointer-events-none absolute inset-0 h-full w-full -z-[1] opacity-30 dark:opacity-30 opacity-20",
        className
      )}
      {...props}
    >
      <defs>
        {/* 深色模式渐变 */}
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{stopColor: "oklch(0.6 0.15 220)", stopOpacity: 0.3}} />
          <stop offset="50%" style={{stopColor: "oklch(0.7 0.12 180)", stopOpacity: 0.2}} />
          <stop offset="100%" style={{stopColor: "oklch(0.65 0.18 140)", stopOpacity: 0.25}} />
        </linearGradient>
        
        {/* 浅色模式渐变 */}
        <linearGradient id={lightGradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{stopColor: "oklch(0.4 0.2 220)", stopOpacity: 0.3}} />
          <stop offset="50%" style={{stopColor: "oklch(0.3 0.15 180)", stopOpacity: 0.2}} />
          <stop offset="100%" style={{stopColor: "oklch(0.35 0.22 140)", stopOpacity: 0.25}} />
        </linearGradient>
        
        {/* 深色模式发光效果 */}
        <filter id={glowId}>
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        
        {/* 浅色模式发光效果 */}
        <filter id={lightGlowId}>
          <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        
        {/* 深色模式网格图案 */}
        <pattern
          id={id}
          width={width}
          height={height}
          patternUnits="userSpaceOnUse"
          x={x}
          y={y}
        >
          {/* 主网格线 */}
          <path
            d={`M.5 ${height}V.5H${width}`}
            fill="none"
            stroke={`url(#${gradientId})`}
            strokeWidth="0.5"
            strokeDasharray={strokeDasharray}
            filter={`url(#${glowId})`}
            className="dark:block hidden"
          />
          
          {/* 交叉点高亮 */}
          <circle
            cx="0.5"
            cy="0.5"
            r="1"
            fill="oklch(0.8 0.2 200)"
            opacity="0.4"
            filter={`url(#${glowId})`}
            className="dark:block hidden"
          />
          
          {/* 对角线装饰 */}
          <path
            d={`M0 0L${width/4} ${height/4}`}
            stroke="oklch(0.75 0.15 160)"
            strokeWidth="0.25"
            opacity="0.3"
            strokeDasharray="2 4"
            className="dark:block hidden"
          />
        </pattern>
        
        {/* 浅色模式网格图案 */}
        <pattern
          id={`${id}-light`}
          width={width}
          height={height}
          patternUnits="userSpaceOnUse"
          x={x}
          y={y}
        >
          {/* 主网格线 - 浅色模式 */}
          <path
            d={`M.5 ${height}V.5H${width}`}
            fill="none"
            stroke={`url(#${lightGradientId})`}
            strokeWidth="0.5"
            strokeDasharray={strokeDasharray}
            filter={`url(#${lightGlowId})`}
            className="block dark:hidden"
          />
          
          {/* 交叉点高亮 - 浅色模式 */}
          <circle
            cx="0.5"
            cy="0.5"
            r="0.8"
            fill="oklch(0.2 0.3 220)"
            opacity="0.3"
            filter={`url(#${lightGlowId})`}
            className="block dark:hidden"
          />
          
          {/* 对角线装饰 - 浅色模式 */}
          <path
            d={`M0 0L${width/4} ${height/4}`}
            stroke="oklch(0.25 0.2 200)"
            strokeWidth="0.25"
            opacity="0.2"
            strokeDasharray="2 4"
            className="block dark:hidden"
          />
        </pattern>
        
        {/* 动态脉冲效果 */}
        <pattern
          id={`${id}-pulse`}
          width={width * 2}
          height={height * 2}
          patternUnits="userSpaceOnUse"
        >
          <circle
            cx={width}
            cy={height}
            r="2"
            fill="oklch(0.85 0.25 200)"
            opacity="0.6"
            className="dark:block hidden"
          >
            <animate
              attributeName="opacity"
              values="0.2;0.8;0.2"
              dur="3s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="r"
              values="1;3;1"
              dur="3s"
              repeatCount="indefinite"
            />
          </circle>
          
          {/* 浅色模式脉冲 */}
          <circle
            cx={width}
            cy={height}
            r="2"
            fill="oklch(0.15 0.4 220)"
            opacity="0.4"
            className="block dark:hidden"
          >
            <animate
              attributeName="opacity"
              values="0.2;0.5;0.2"
              dur="3s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="r"
              values="1;3;1"
              dur="3s"
              repeatCount="indefinite"
            />
          </circle>
        </pattern>
      </defs>
      
      {/* 基础网格 - 深色模式 */}
      <rect width="100%" height="100%" strokeWidth={0} fill={`url(#${id})`} className="dark:block hidden" />
      
      {/* 基础网格 - 浅色模式 */}
      <rect width="100%" height="100%" strokeWidth={0} fill={`url(#${id}-light)`} className="block dark:hidden" />
      
      {/* 脉冲层 */}
      <rect width="100%" height="100%" strokeWidth={0} fill={`url(#${id}-pulse)`} opacity="0.15" />
      
      {/* 高亮方块 */}
      {squares && (
        <svg x={x} y={y} className="overflow-visible">
          {squares.map(([x, y]) => (
            <g key={`${x}-${y}`}>
              {/* 深色模式高亮方块 */}
              <rect
                strokeWidth="0"
                width={width - 1}
                height={height - 1}
                x={x * width + 1}
                y={y * height + 1}
                fill="oklch(0.7 0.2 180)"
                opacity="0.4"
                filter={`url(#${glowId})`}
                className="dark:block hidden"
              />
              <rect
                strokeWidth="1"
                width={width - 3}
                height={height - 3}
                x={x * width + 2}
                y={y * height + 2}
                fill="none"
                stroke="oklch(0.8 0.25 200)"
                opacity="0.6"
                strokeDasharray="4 2"
                className="dark:block hidden"
              />
              
              {/* 浅色模式高亮方块 */}
              <rect
                strokeWidth="0"
                width={width - 1}
                height={height - 1}
                x={x * width + 1}
                y={y * height + 1}
                fill="oklch(0.3 0.3 200)"
                opacity="0.3"
                filter={`url(#${lightGlowId})`}
                className="block dark:hidden"
              />
              <rect
                strokeWidth="1"
                width={width - 3}
                height={height - 3}
                x={x * width + 2}
                y={y * height + 2}
                fill="none"
                stroke="oklch(0.2 0.4 220)"
                opacity="0.4"
                strokeDasharray="4 2"
                className="block dark:hidden"
              />
            </g>
          ))}
        </svg>
      )}
    </svg>
  );
}
