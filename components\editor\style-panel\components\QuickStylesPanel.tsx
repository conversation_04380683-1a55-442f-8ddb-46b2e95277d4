"use client";
import { useCallback } from "react";
import { ColorPicker } from "./ColorPicker";
import { NumberAdjuster } from "./NumberAdjuster";
import { AlignmentPanel } from "./AlignmentPanel";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface StyleProperty {
  key: string;
  label: string;
  type: 'color' | 'size' | 'select';
  options?: string[];
  unit?: string;
  presets?: string[];
}

interface QuickStylesPanelProps {
  selectedElement: HTMLElement | null;
  styleValues: Record<string, string>;
  onStyleChange: (key: string, value: string) => void;
}

export function QuickStylesPanel({ selectedElement, styleValues, onStyleChange }: QuickStylesPanelProps) {
  // 获取核心样式属性 - 简化版本
  const getCoreStyles = useCallback((element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    const isTextElement = ['p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a', 'button', 'label'].includes(tagName);
    
    // 基础颜色样式 - 所有元素都有
    const coreStyles = [
      { 
        key: 'color', 
        label: '文字', 
        type: 'color' as const, 
        presets: ['#000000', '#ffffff', '#3b82f6', '#ef4444', '#10b981', '#f59e0b'] 
      },
      { 
        key: 'background-color', 
        label: '背景', 
        type: 'color' as const, 
        presets: ['transparent', '#ffffff', '#f8fafc', '#3b82f6', '#ef4444', '#10b981'] 
      }
    ];

    // 文本元素添加字体大小
    if (isTextElement) {
      coreStyles.push({
        key: 'font-size', 
        label: '大小', 
        type: 'size' as const, 
        unit: 'px', 
        presets: ['14px', '16px', '18px', '20px', '24px', '32px'] 
      });
    }

    // 添加最常用的间距
    coreStyles.push(
      { 
        key: 'padding', 
        label: '内距', 
        type: 'size' as const, 
        unit: 'px', 
        presets: ['0', '8px', '12px', '16px', '20px', '24px'] 
      },
      { 
        key: 'border-radius', 
        label: '圆角', 
        type: 'size' as const, 
        unit: 'px', 
        presets: ['0', '4px', '8px', '12px', '16px', '20px'] 
      }
    );

    return coreStyles;
  }, []);

  // 渲染样式输入组件
  const renderStyleInput = useCallback((prop: StyleProperty) => {
    const value = styleValues[prop.key] || '';
    
    switch (prop.type) {
      case 'color':
        return (
          <ColorPicker
            value={value}
            onChange={(newValue) => onStyleChange(prop.key, newValue)}
            presets={prop.presets}
          />
        );
      
      case 'size':
        return (
          <NumberAdjuster
            value={value}
            onChange={(newValue) => onStyleChange(prop.key, newValue)}
            unit={prop.unit}
            presets={prop.presets}
          />
        );
      
      case 'select':
        return (
          <Select value={value} onValueChange={(newValue) => onStyleChange(prop.key, newValue)}>
            <SelectTrigger className="h-8 bg-background border-border">
              <SelectValue placeholder="选择..." />
            </SelectTrigger>
            <SelectContent>
              {prop.options?.map(option => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      default:
        return null;
    }
  }, [styleValues, onStyleChange]);

  if (!selectedElement) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* 颜色区域 */}
      <div className="space-y-3">
        <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">颜色</h3>
        <div className="grid grid-cols-2 gap-3">
          {getCoreStyles(selectedElement)
            .filter(prop => prop.type === 'color')
            .map(prop => (
              <div key={prop.key} className="space-y-2">
                <label className="text-xs font-medium text-foreground block">
                  {prop.label}
                </label>
                {renderStyleInput(prop)}
              </div>
            ))}
        </div>
      </div>

      {/* 对齐区域 - 移到颜色下面 */}
      <AlignmentPanel
        selectedElement={selectedElement}
        onStyleChange={onStyleChange}
      />

      {/* 尺寸区域 */}
      <div className="space-y-3">
        <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">尺寸</h3>
        <div className="space-y-3">
          {getCoreStyles(selectedElement)
            .filter(prop => prop.type === 'size')
            .map(prop => (
              <div key={prop.key} className="space-y-2">
                <label className="text-xs font-medium text-foreground block">
                  {prop.label}
                </label>
                {renderStyleInput(prop)}
              </div>
            ))}
        </div>
      </div>
    </div>
  );
} 