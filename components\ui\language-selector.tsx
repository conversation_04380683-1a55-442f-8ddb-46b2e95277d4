"use client"

import * as React from "react"
import { Globe, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useThemeLanguage } from "@/components/providers/theme-language-provider"
import { cn } from "@/lib/utils"

interface LanguageSelectorProps {
  className?: string;
  variant?: "default" | "outline" | "ghost" | "link" | "destructive" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  showLabel?: boolean;
}

const languages = [
  { code: 'zh', name: '简体中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ug', name: 'ئۇيغۇرچە', flag: '🌙' },
] as const;

export function LanguageSelector({ 
  className, 
  variant = "ghost", 
  size = "sm",
  showLabel = false 
}: LanguageSelectorProps) {
  const { language, changeLanguage } = useThemeLanguage()
  const [open, setOpen] = React.useState(false)

  const currentLanguage = languages.find(lang => lang.code === language)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn("justify-start gap-2", className)}
        >
          <Globe className="h-4 w-4" />
          {showLabel && (
            <span className="flex items-center gap-1.5">
              <span>{currentLanguage?.flag}</span>
              <span>{currentLanguage?.name}</span>
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-1" align="end">
        <div className="space-y-1">
          {languages.map((lang) => (
            <Button
              key={lang.code}
              variant={language === lang.code ? "secondary" : "ghost"}
              size="sm"
              className="w-full justify-start gap-2"
              onClick={() => {
                changeLanguage(lang.code)
                setOpen(false)
              }}
            >
              <span className="text-base">{lang.flag}</span>
              <span className="flex-1 text-left">{lang.name}</span>
              {language === lang.code && (
                <Check className="h-4 w-4" />
              )}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
} 