import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';
import { cookies } from 'next/headers';
import { getUserByToken } from '@/lib/auth-service';

// 🚀 企业级社区项目API - 高性能优化版本

// 定义数据库查询结果类型
interface CommunityProjectRow {
  id: number;
  original_project_id: number;
  user_id: number;
  title: string;
  html_content: string;
  category: string | null;
  preview_image_url: string | null;
  preview_image_type: string | null;
  preview_metadata: string | null;
  created_at: string;
  updated_at: string;
  author_name: string | null;
  author_phone: string | null;
  author_avatar_url: string | null;
  favorites_count: number;
  is_favorited: number;
}

export async function GET(request: NextRequest) {
  const startTime = performance.now();
  let currentUserId: number | null = null;
  
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);

    console.log('🚀 社区项目API调用 [高性能优化版]', { search, category, limit, offset });

    // 🔐 获取当前用户信息（用于判断收藏状态）
    try {
      const cookieStore = await cookies();
      const token = cookieStore.get("loomrun_token")?.value;
      if (token) {
        const user = await getUserByToken(token);
        if (user && user.id) {
          currentUserId = user.id;
          console.log('✅ 用户已登录:', { userId: currentUserId });
        }
      }
    } catch (error) {
      console.log('⚠️ 获取用户信息失败:', error);
    }

    // 确保 limit 和 offset 是有效的数字
    const safeLimit = Number.isInteger(limit) && limit > 0 ? limit : 20;
    const safeOffset = Number.isInteger(offset) && offset >= 0 ? offset : 0;

    // 🚀 单一优化查询 - 获取所有需要的数据
    let query: string;
    let params: (string | number)[] = [];

    // 尝试包含扩展字段，如果不存在则使用基础查询
    let baseQuery: string;
    
    try {
             baseQuery = `
         SELECT 
           cp.id,
           cp.original_project_id,
           cp.user_id,
           cp.title,
           cp.html_content,
           cp.category,
           cp.preview_image_url,
           cp.preview_image_type,
           cp.preview_metadata,
           cp.created_at,
           cp.updated_at,
           u.nickname as author_name,
           u.phone as author_phone,
           u.avatar_url as author_avatar_url,
           COALESCE(fav_count.count, 0) as favorites_count,
           COALESCE(user_fav.is_favorited, 0) as is_favorited
         FROM community_projects cp
         LEFT JOIN users u ON cp.user_id = u.id
         LEFT JOIN (
           SELECT project_id, COUNT(*) as count
           FROM project_favorites
           GROUP BY project_id
         ) fav_count ON cp.id = fav_count.project_id
         ${currentUserId ? `
         LEFT JOIN (
           SELECT project_id, 1 as is_favorited
           FROM project_favorites
           WHERE user_id = ${currentUserId}
         ) user_fav ON cp.id = user_fav.project_id
         ` : `
         LEFT JOIN (SELECT NULL as project_id, 0 as is_favorited WHERE 1=0) user_fav ON 1=0
         `}
       `;
    } catch (e) {
      // 如果扩展字段不存在，使用基础查询
      console.warn('使用基础查询字段:', e);
      baseQuery = `
        SELECT 
          cp.id,
          cp.original_project_id,
          cp.user_id,
          cp.title,
          cp.html_content,
          cp.category,
          cp.created_at,
          cp.updated_at,
          u.nickname as author_name,
          u.phone as author_phone,
          u.avatar_url as author_avatar_url,
          COALESCE(fav_count.count, 0) as favorites_count,
          COALESCE(user_fav.is_favorited, 0) as is_favorited
        FROM community_projects cp
        LEFT JOIN users u ON cp.user_id = u.id
        LEFT JOIN (
          SELECT project_id, COUNT(*) as count
          FROM project_favorites
          GROUP BY project_id
        ) fav_count ON cp.id = fav_count.project_id
        ${currentUserId ? `
        LEFT JOIN (
          SELECT project_id, 1 as is_favorited
          FROM project_favorites
          WHERE user_id = ${currentUserId}
        ) user_fav ON cp.id = user_fav.project_id
        ` : `
        LEFT JOIN (SELECT NULL as project_id, 0 as is_favorited WHERE 1=0) user_fav ON 1=0
        `}
      `;
    }

    // 构建WHERE条件
    const whereConditions: string[] = [];
    
    if (search) {
      whereConditions.push(`cp.title LIKE ?`);
      params.push(`%${search}%`);
    }
    
    if (category) {
      whereConditions.push(`cp.category = ?`);
      params.push(category);
    }
    
    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';
    
    if (whereConditions.length > 0) {
      query = baseQuery + `
        ${whereClause}
        ORDER BY cp.created_at DESC
        LIMIT ${safeLimit} OFFSET ${safeOffset}
      `;
    } else {
      query = baseQuery + `
        ORDER BY cp.created_at DESC
        LIMIT ${safeLimit} OFFSET ${safeOffset}
      `;
      params = [];
    }

    // 🔍 调试信息
    console.log('🔍 SQL调试信息:', {
      hasUser: !!currentUserId,
      userId: currentUserId,
      search: search,
      safeLimit,
      safeOffset,
      queryLength: query.length
    });

    // 🔥 执行优化查询
    const queryStartTime = performance.now();
    const projects = await executeQuery(query, params) as CommunityProjectRow[];
    const queryDuration = performance.now() - queryStartTime;

    // 🎯 获取总数（优化：只在第一页时查询）
    let total = 0;
    if (safeOffset === 0) {
      try {
        // 构建计数查询的WHERE条件
        const countWhereConditions: string[] = [];
        const countParams: (string | number)[] = [];
        
        if (search) {
          countWhereConditions.push('title LIKE ?');
          countParams.push(`%${search}%`);
        }
        
        if (category) {
          countWhereConditions.push('category = ?');
          countParams.push(category);
        }
        
        const countWhereClause = countWhereConditions.length > 0 
          ? `WHERE ${countWhereConditions.join(' AND ')}`
          : '';
          
        const countQuery = `SELECT COUNT(*) as total FROM community_projects ${countWhereClause}`;
        
        const totalResult = await executeQuery(countQuery, countParams) as { total: number }[];
        total = totalResult[0]?.total || 0;
      } catch (error) {
        console.warn('获取总数失败:', error);
      }
    }

         // 🔄 格式化返回数据
     const formattedProjects = projects.map(project => {
       // 解析预览图元数据（如果字段存在）
       let previewMetadata = null;
       if (project.preview_metadata) {
         try {
           previewMetadata = JSON.parse(project.preview_metadata);
         } catch (e) {
           console.warn('解析预览图元数据失败:', e);
         }
       }

       // 安全地访问可能不存在的字段
       const previewImageUrl = (project as any).preview_image_url;
       const previewImageType = (project as any).preview_image_type;

       return {
         id: project.id,
         originalProjectId: project.original_project_id,
         userId: project.user_id,
         title: project.title, // 直接使用title字段
         htmlContent: project.html_content,
         category: project.category || 'sites',
         previewImageUrl: previewImageUrl || null,
         previewImageType: (previewImageType as 'auto' | 'manual' | 'upload' | 'static') || 'static',
         previewMetadata,
         customTitle: project.title, // 将title作为customTitle返回，保持API兼容性
         createdAt: project.created_at,
         updatedAt: project.updated_at,
         author: {
           name: project.author_name || '匿名用户',
           email: project.author_phone || '',
           avatar_url: project.author_avatar_url
         },
         favoritesCount: project.favorites_count,
         isFavorited: Boolean(project.is_favorited)
       };
     });

    const result = {
      projects: formattedProjects,
      pagination: {
        total,
        limit: safeLimit,
        offset: safeOffset,
        hasMore: projects.length === safeLimit
      }
    };

    const totalDuration = performance.now() - startTime;

    console.log('✅ 社区项目查询成功 [高性能优化版]', { 
      count: projects.length, 
      search, 
      limit: safeLimit, 
      offset: safeOffset,
      hasUser: !!currentUserId,
      queryDuration: `${queryDuration.toFixed(1)}ms`,
      totalDuration: `${totalDuration.toFixed(1)}ms`
    });

    return NextResponse.json(result, {
      headers: {
        'Cache-Control': search ? 'private, max-age=60' : 'public, max-age=300, stale-while-revalidate=600',
        'X-Response-Time': `${totalDuration.toFixed(1)}ms`,
        'X-Query-Time': `${queryDuration.toFixed(1)}ms`,
        'X-Project-Count': projects.length.toString()
      }
    });

  } catch (error) {
    const duration = performance.now() - startTime;
    
    // Get searchParams for error response
    const { searchParams } = new URL(request.url);
    const errorLimit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const errorOffset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);
    
    console.error('❌ 社区项目API错误 [高性能优化版]:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      duration: `${duration.toFixed(1)}ms`,
      userId: currentUserId
    });

    return NextResponse.json({ 
      error: '获取社区项目失败',
      projects: [],
      pagination: {
        total: 0,
        limit: errorLimit,
        offset: errorOffset,
        hasMore: false
      }
    }, { 
      status: 500,
      headers: {
        'X-Response-Time': `${duration.toFixed(1)}ms`,
        'X-Error': 'true'
      }
    });
  }
} 