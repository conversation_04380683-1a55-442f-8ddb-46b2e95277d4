import { NextResponse } from "next/server";
import { formatValidityDays, formatValidityWithStyle, isPermanentValidity, getValidityDescription } from "@/lib/validity-display-utils";

export async function GET() {
  try {
    const testCases = [
      999999, // 永久
      730,    // 2年
      365,    // 1年
      180,    // 6个月
      90,     // 3个月
      60,     // 2个月
      30,     // 1个月
      15,     // 15天
      7,      // 7天
      1       // 1天
    ];

    const results = testCases.map(days => {
      const formatted = formatValidityDays(days);
      const withStyle = formatValidityWithStyle(days);
      const isPermanent = isPermanentValidity(days);
      const description = getValidityDescription(days);

      return {
        input_days: days,
        formatted_text: formatted,
        color_class: withStyle.colorClass,
        is_permanent: isPermanent,
        description: description,
        display_example: `积分为活动积分，${formatted}`
      };
    });

    return NextResponse.json({
      success: true,
      message: "有效期显示格式化测试",
      test_results: results,
      usage_examples: {
        "999999天": "永久有效 (绿色)",
        "730天": "2年 (蓝色)",
        "365天": "1年 (蓝色)", 
        "180天": "6个月 (紫色)",
        "90天": "3个月 (紫色)",
        "30天": "1个月 (橙色)",
        "15天": "15天 (红色)",
        "7天": "7天 (红色)"
      }
    });

  } catch (error) {
    console.error("有效期显示测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
