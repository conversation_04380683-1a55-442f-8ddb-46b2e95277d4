/**
 * 深色模式样式工具函数
 */

/**
 * 为深色模式调整颜色类名
 * @param lightColorClass 浅色模式的颜色类名
 * @returns 包含深色模式的完整类名
 */
export const adaptColorForDarkMode = (lightColorClass: string): string => {
  const colorMappings: Record<string, string> = {
    'text-green-600': 'text-green-600 dark:text-green-400',
    'text-blue-600': 'text-blue-600 dark:text-blue-400',
    'text-purple-600': 'text-purple-600 dark:text-purple-400',
    'text-orange-600': 'text-orange-600 dark:text-orange-400',
    'text-red-600': 'text-red-600 dark:text-red-400',
    'text-yellow-600': 'text-yellow-600 dark:text-yellow-400',
    'text-indigo-600': 'text-indigo-600 dark:text-indigo-400',
    'text-pink-600': 'text-pink-600 dark:text-pink-400',
    'text-gray-600': 'text-gray-600 dark:text-gray-400',
    'text-gray-700': 'text-gray-700 dark:text-gray-300',
    'text-gray-800': 'text-gray-800 dark:text-gray-200',
    'text-gray-900': 'text-gray-900 dark:text-gray-100',
  };

  return colorMappings[lightColorClass] || lightColorClass;
};

/**
 * 获取深色模式友好的背景类名
 * @param variant 背景变体类型
 * @returns 深色模式友好的背景类名
 */
export const getDarkModeBackground = (variant: 'card' | 'input' | 'button' | 'accent' | 'success' | 'warning' | 'error'): string => {
  const backgrounds = {
    card: 'bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700',
    input: 'bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600',
    button: 'bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600',
    accent: 'bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-100 dark:border-purple-800/30',
    success: 'bg-green-50 dark:bg-green-900/20 border-green-100 dark:border-green-800/30',
    warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-100 dark:border-yellow-800/30',
    error: 'bg-red-50 dark:bg-red-900/20 border-red-100 dark:border-red-800/30'
  };

  return backgrounds[variant];
};

/**
 * 获取深色模式友好的文本类名
 * @param variant 文本变体类型
 * @returns 深色模式友好的文本类名
 */
export const getDarkModeText = (variant: 'primary' | 'secondary' | 'muted' | 'accent' | 'success' | 'warning' | 'error'): string => {
  const textColors = {
    primary: 'text-gray-900 dark:text-gray-100',
    secondary: 'text-gray-700 dark:text-gray-300',
    muted: 'text-gray-600 dark:text-gray-400',
    accent: 'text-purple-600 dark:text-purple-400',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    error: 'text-red-600 dark:text-red-400'
  };

  return textColors[variant];
};

/**
 * 获取深色模式友好的边框类名
 * @param variant 边框变体类型
 * @returns 深色模式友好的边框类名
 */
export const getDarkModeBorder = (variant: 'default' | 'accent' | 'success' | 'warning' | 'error'): string => {
  const borders = {
    default: 'border-gray-200 dark:border-gray-700',
    accent: 'border-purple-200 dark:border-purple-700',
    success: 'border-green-200 dark:border-green-700',
    warning: 'border-yellow-200 dark:border-yellow-700',
    error: 'border-red-200 dark:border-red-700'
  };

  return borders[variant];
};

/**
 * 获取深色模式友好的阴影类名
 * @param variant 阴影变体类型
 * @returns 深色模式友好的阴影类名
 */
export const getDarkModeShadow = (variant: 'sm' | 'md' | 'lg' | 'xl'): string => {
  const shadows = {
    sm: 'shadow-sm dark:shadow-gray-900/20',
    md: 'shadow-md dark:shadow-gray-900/30',
    lg: 'shadow-lg dark:shadow-gray-900/40',
    xl: 'shadow-xl dark:shadow-gray-900/50'
  };

  return shadows[variant];
};

/**
 * 获取深色模式友好的按钮样式
 * @param variant 按钮变体类型
 * @returns 深色模式友好的按钮类名
 */
export const getDarkModeButton = (variant: 'primary' | 'secondary' | 'outline' | 'ghost'): string => {
  const buttons = {
    primary: 'bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 text-white border-0',
    secondary: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100',
    outline: 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 bg-white dark:bg-gray-900',
    ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
  };

  return buttons[variant];
};

/**
 * 获取深色模式友好的徽章样式
 * @param variant 徽章变体类型
 * @param isActive 是否为活跃状态
 * @returns 深色模式友好的徽章类名
 */
export const getDarkModeBadge = (variant: 'default' | 'secondary' | 'success' | 'warning' | 'error', isActive: boolean = true): string => {
  if (!isActive) {
    return 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400';
  }

  const badges = {
    default: 'bg-blue-600 dark:bg-blue-500 text-white',
    secondary: 'bg-gray-600 dark:bg-gray-500 text-white',
    success: 'bg-green-600 dark:bg-green-500 text-white',
    warning: 'bg-yellow-600 dark:bg-yellow-500 text-white',
    error: 'bg-red-600 dark:bg-red-500 text-white'
  };

  return badges[variant];
};

/**
 * 获取深色模式友好的输入框样式
 * @param hasError 是否有错误状态
 * @returns 深色模式友好的输入框类名
 */
export const getDarkModeInput = (hasError: boolean = false): string => {
  const baseClasses = 'px-3 py-2 rounded-md text-sm transition-colors duration-200';
  const normalClasses = 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent';
  const errorClasses = 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20 text-gray-900 dark:text-gray-100 placeholder-red-400 dark:placeholder-red-500 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:border-transparent';

  return `${baseClasses} ${hasError ? errorClasses : normalClasses}`;
};
