"use client";

import { useState, useEffect } from "react";
import { X, Gift, Users, Copy, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useCopyToClipboard } from "react-use";
import { formatValidityWithStyle } from "@/lib/validity-display-utils";

interface AnnouncementBannerProps {
  className?: string;
}

interface InvitationStats {
  total_invitations: number;
  successful_invitations: number;
  pending_invitations: number;
  total_points_earned: number;
  remaining_invitations: number;
}

interface AnnouncementSettings {
  show_invitation_banner: boolean;
  invitation_enabled: boolean;
  invitation_points_per_user: number;
  max_invitations_per_user: number;
  invitation_validity_days: number;
}

export function AnnouncementBanner({ className }: AnnouncementBannerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteUrl, setInviteUrl] = useState("");
  const [stats, setStats] = useState<InvitationStats | null>(null);
  const [settings, setSettings] = useState<AnnouncementSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [_, copyToClipboard] = useCopyToClipboard();

  // 检查公告显示设置
  useEffect(() => {
    const checkAnnouncementSettings = async () => {
      try {
        const response = await fetch('/api/system/announcement-settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
          setIsVisible(data.show_invitation_banner && data.invitation_enabled);
        }
      } catch (error) {
        console.error('获取公告设置失败:', error);
      }
    };

    checkAnnouncementSettings();
  }, []);

  // 获取邀请统计
  const fetchInvitationStats = async () => {
    try {
      const response = await fetch('/api/me/invitation/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('获取邀请统计失败:', error);
    }
  };

  // 生成邀请链接
  const generateInviteLink = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/me/invitation/generate-link', {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (data.success) {
        setInviteUrl(data.inviteUrl);
        await fetchInvitationStats();
      } else {
        toast.error(data.message || '生成邀请链接失败');
      }
    } catch (error) {
      toast.error('生成邀请链接失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理领取按钮点击
  const handleClaimClick = async () => {
    setShowInviteModal(true);
    await fetchInvitationStats();
    if (!inviteUrl) {
      await generateInviteLink();
    }
  };

  // 复制邀请链接
  const handleCopyLink = () => {
    if (inviteUrl) {
      copyToClipboard(inviteUrl);
      setCopied(true);
      toast.success('邀请链接已复制到剪贴板！');
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // 关闭公告
  const handleClose = () => {
    setIsVisible(false);
    // 可以在这里添加用户偏好设置，记住用户关闭了公告
  };

  if (!isVisible || !settings) {
    return null;
  }

  return (
    <>
      {/* 公告横幅 - 精致的居中胶囊样式 */}
      <div className={`flex justify-center ${className}`}>
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white pl-3 pr-8 py-1 rounded-full relative shadow-md border border-white/20 backdrop-blur-sm max-w-fit hover:shadow-lg transition-shadow duration-200">
          <div className="flex items-center space-x-2">
            <Gift className="w-3.5 h-3.5 animate-bounce flex-shrink-0" />
            <span className="text-xs font-medium whitespace-nowrap">
              🎉 邀请好友注册，每成功邀请1人获得 <strong>{settings.invitation_points_per_user}</strong> 积分！
            </span>
            <Button
              size="sm"
              variant="secondary"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30 text-xs px-2 py-0.5 h-5 rounded-full flex-shrink-0 transition-all duration-200"
              onClick={handleClaimClick}
            >
              立即参与
            </Button>
          </div>

          <button
            onClick={handleClose}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white/80 hover:text-white transition-all duration-200 hover:scale-105"
          >
            <X className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* 邀请详情弹窗 */}
      <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
        <DialogContent className="sm:max-w-md bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-gray-900 dark:text-gray-100">
              <Users className="w-5 h-5 text-purple-500 dark:text-purple-400" />
              <span>邀请好友赚积分</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* 活动说明 */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-lg border border-purple-100 dark:border-purple-800/30">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">活动规则</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• 每成功邀请1位新用户注册，获得 <strong className="text-purple-600 dark:text-purple-400">{settings.invitation_points_per_user}</strong> 积分</li>
                <li>• 每人最多可邀请 <strong className="text-purple-600 dark:text-purple-400">{settings.max_invitations_per_user}</strong> 位好友</li>
                <li>• 积分为活动积分，
                  {(() => {
                    const { text, colorClass } = formatValidityWithStyle(settings.invitation_validity_days);
                    return <strong className={colorClass}>{text}</strong>;
                  })()}
                </li>
                <li>• 被邀请用户必须是新注册用户</li>
              </ul>
            </div>

            {/* 邀请统计 */}
            {stats && (
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.successful_invitations}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">成功邀请</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.total_points_earned}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">已获积分</div>
                </div>
              </div>
            )}

            {/* 剩余邀请次数 */}
            {stats && (
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
                <span className="text-sm text-gray-600 dark:text-gray-400">剩余邀请次数</span>
                <Badge
                  variant={stats.remaining_invitations > 0 ? "default" : "secondary"}
                  className={stats.remaining_invitations > 0
                    ? "bg-blue-600 dark:bg-blue-500 text-white"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                  }
                >
                  {stats.remaining_invitations} 次
                </Badge>
              </div>
            )}

            {/* 邀请链接 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">您的专属邀请链接</label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inviteUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent"
                  placeholder={loading ? "生成中..." : "邀请链接"}
                />
                <Button
                  size="sm"
                  onClick={handleCopyLink}
                  disabled={!inviteUrl || loading}
                  className="px-3 bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 text-white border-0"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-2">
              <Button
                onClick={generateInviteLink}
                disabled={loading || (stats?.remaining_invitations || 0) <= 0}
                className="flex-1 bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 text-white border-0 disabled:bg-gray-400 dark:disabled:bg-gray-600"
              >
                {loading ? "生成中..." : "重新生成链接"}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowInviteModal(false)}
                className="flex-1 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 bg-white dark:bg-gray-900"
              >
                关闭
              </Button>
            </div>

            {/* 分享提示 */}
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
              💡 将邀请链接分享给好友，好友通过链接注册成功后，您将自动获得积分奖励
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
