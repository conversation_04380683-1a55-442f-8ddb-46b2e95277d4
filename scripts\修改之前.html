<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧物流管理系统</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* Active link styling */
        .nav-link.active {
            background-color: #3b82f6; /* blue-500 */
            color: white;
            border-radius: 0.375rem; /* rounded-md */
        }
        .nav-link.active svg, .nav-link.active span {
            color: white !important;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans antialiased flex h-screen overflow-hidden">

    <!-- 侧边栏 (Sidebar) -->
    <aside class="w-64 bg-gray-800 text-gray-200 flex flex-col p-4 shadow-lg overflow-y-auto">
        <div class="flex items-center justify-center mb-6 mt-2">
            <i class="fas fa-truck text-blue-400 text-3xl mr-2"></i>
            <h1 class="text-2xl font-semibold text-white">智慧物流</h1>
        </div>
        <nav class="flex-1 space-y-2">
            <a href="#dashboard" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700 active">
                <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                <span class="text-lg">仪表盘</span>
            </a>
            <a href="#shipments" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-box-open mr-3 text-lg"></i>
                <span class="text-lg">货物管理</span>
            </a>
            <a href="#orders" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-clipboard-list mr-3 text-lg"></i>
                <span class="text-lg">订单管理</span>
            </a>
            <a href="#vehicles" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-truck-moving mr-3 text-lg"></i>
                <span class="text-lg">车辆管理</span>
            </a>
            <a href="#drivers" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-users mr-3 text-lg"></i>
                <span class="text-lg">司机管理</span>
            </a>
            <a href="#warehouses" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-warehouse mr-3 text-lg"></i>
                <span class="text-lg">仓库管理</span>
            </a>
            <a href="#reports" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-chart-line mr-3 text-lg"></i>
                <span class="text-lg">数据报表</span>
            </a>
            <a href="#settings" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-cog mr-3 text-lg"></i>
                <span class="text-lg">系统设置</span>
            </a>
        </nav>
        <div class="mt-auto pt-4 border-t border-gray-700">
            <a href="#profile" class="nav-link flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-gray-700">
                <i class="fas fa-user-circle mr-3 text-lg"></i>
                <span class="text-lg">我的资料</span>
            </a>
            <button class="w-full flex items-center p-3 rounded-md transition-colors duration-200 hover:bg-red-600 text-red-400 hover:text-white mt-2">
                <i class="fas fa-sign-out-alt mr-3 text-lg"></i>
                <span class="text-lg">退出登录</span>
            </button>
        </div>
    </aside>

    <!-- 主内容区域 (Main Content) -->
    <main class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部导航栏 (Header) -->
        <header class="bg-white shadow-md p-4 flex items-center justify-between z-10">
            <h2 class="text-2xl font-semibold text-gray-800" id="page-title">仪表盘</h2>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <input type="text" placeholder="搜索..." class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-search absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button class="text-gray-600 hover:text-blue-500 transition-colors duration-200">
                    <i class="fas fa-bell text-xl"></i>
                </button>
                <div class="flex items-center space-x-2">
                    <img src="https://i.pravatar.cc/40" alt="User Avatar" class="w-10 h-10 rounded-full border-2 border-blue-400">
                    <span class="font-medium text-gray-700">管理员</span>
                </div>
            </div>
        </header>

        <!-- 内容区域 (Content Sections) -->
        <div class="flex-1 p-6 overflow-y-auto">

            <!-- 仪表盘 (Dashboard) Section -->
            <section id="dashboard" class="content-section active">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">系统概览</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Stat Card 1 -->
                    <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">总货物量</p>
                            <h4 class="text-3xl font-bold text-gray-900 mt-1">2,567</h4>
                        </div>
                        <div class="bg-blue-100 p-3 rounded-full">
                            <i class="fas fa-box-open text-blue-500 text-2xl"></i>
                        </div>
                    </div>
                    <!-- Stat Card 2 -->
                    <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">待处理订单</p>
                            <h4 class="text-3xl font-bold text-gray-900 mt-1">128</h4>
                        </div>
                        <div class="bg-yellow-100 p-3 rounded-full">
                            <i class="fas fa-hourglass-half text-yellow-500 text-2xl"></i>
                        </div>
                    </div>
                    <!-- Stat Card 3 -->
                    <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">已交付货物</p>
                            <h4 class="text-3xl font-bold text-gray-900 mt-1">2,105</h4>
                        </div>
                        <div class="bg-green-100 p-3 rounded-full">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                    </div>
                    <!-- Stat Card 4 -->
                    <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">在途车辆</p>
                            <h4 class="text-3xl font-bold text-gray-900 mt-1">35</h4>
                        </div>
                        <div class="bg-purple-100 p-3 rounded-full">
                            <i class="fas fa-truck-moving text-purple-500 text-2xl"></i>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Shipments -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h4 class="text-xl font-semibold text-gray-800 mb-4">最新货物状态</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">货物ID</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起点</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">终点</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预计送达</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001234</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">上海</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北京</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-26</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001233</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">深圳</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广州</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-25</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001232</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成都</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重庆</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">待发货</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-27</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001231</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">杭州</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南京</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">异常</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-26</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001230</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">武汉</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">长沙</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-24</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Vehicle Status Overview -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h4 class="text-xl font-semibold text-gray-800 mb-4">车辆状态总览</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">司机</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前位置</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">京A88888</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">G4京港澳高速</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">行驶中</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">沪B66666</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">上海仓库</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">粤S99999</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">深圳配送中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">装货中</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">苏E12345</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵六</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南京服务区</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">故障</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">鲁G54321</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孙七</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">济南仓库</td>
                                        <td class="px-2 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 货物管理 (Shipments) Section -->
            <section id="shipments" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">货物管理</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4">
                        <div class="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
                            <input type="text" placeholder="搜索货物ID、起点、终点..." class="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80">
                            <select class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                                <option value="">所有状态</option>
                                <option value="pending">待发货</option>
                                <option value="in-transit">在途中</option>
                                <option value="delivered">已送达</option>
                                <option value="exception">异常</option>
                            </select>
                            <input type="date" class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                            <button class="bg-blue-500 text-white px-5 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 w-full md:w-auto">
                                <i class="fas fa-filter mr-2"></i>过滤
                            </button>
                        </div>
                        <button class="bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors duration-200 mt-4 md:mt-0" onclick="openModal('shipmentModal')">
                            <i class="fas fa-plus mr-2"></i>添加货物
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">货物ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起点</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">终点</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承运商</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预计送达</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Dummy Data for Shipments -->
                                <!-- Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001235</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005678</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">上海市浦东新区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北京市朝阳区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">顺丰快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001236</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005679</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广州市天河区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">深圳市南山区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">圆通速递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-26</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001237</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005680</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成都市武侯区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重庆市渝中区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">待发货</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中通快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-29</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 4 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001238</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005681</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">武汉市洪山区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">长沙市岳麓区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">异常</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">德邦物流</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-27</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 5 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001239</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005682</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">杭州市西湖区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南京市玄武区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">京东物流</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 6 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001240</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005683</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">西安市雁塔区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">兰州市城关区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">待发货</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">韵达快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 7 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001241</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005684</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑州市金水区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">石家庄市长安区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">申通快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 8 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001242</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005685</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">济南市历下区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">青岛市市南区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">天天快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-29</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 9 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001243</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005686</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">福州市鼓楼区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厦门市思明区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">待发货</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">百世快递</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 10 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001244</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005687</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">沈阳市和平区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">大连市沙河口区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">异常</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">邮政EMS</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 11 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001245</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005688</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">哈尔滨市南岗区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">长春市南关区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">跨越速运</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-29</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 12 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001246</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005689</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南宁市青秀区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">桂林市秀峰区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">安能物流</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-26</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 13 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001247</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005690</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">贵阳市南明区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">昆明市盘龙区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在途中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中铁快运</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 14 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001248</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005691</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">合肥市庐阳区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南昌市东湖区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">待发货</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">顺心捷达</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 15 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHP001249</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ORD005692</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">太原市迎泽区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">呼和浩特市新城区</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已送达</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中通快运</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-27</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('shipmentModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 订单管理 (Orders) Section -->
            <section id="orders" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">订单管理</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4">
                        <div class="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
                            <input type="text" placeholder="搜索订单ID、客户名..." class="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80">
                            <select class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                                <option value="">所有状态</option>
                                <option value="new">新订单</option>
                                <option value="processing">处理中</option>
                                <option value="shipped">已发货</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <input type="date" class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                            <button class="bg-blue-500 text-white px-5 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 w-full md:w-auto">
                                <i class="fas fa-filter mr-2"></i>过滤
                            </button>
                        </div>
                        <button class="bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors duration-200 mt-4 md:mt-0" onclick="openModal('orderModal')">
                            <i class="fas fa-plus mr-2"></i>添加订单
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">货物内容</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单金额</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联货物ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下单日期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Dummy Data for Orders -->
                                <!-- Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005693</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">电子产品 x 5</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 12,500.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">处理中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001235</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-24</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005694</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李华</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">家具 x 2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 8,000.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001236</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005695</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王丽</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">服装 x 10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 3,500.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">新订单</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">待分配</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 4 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005696</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵强</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">食品 x 20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 1,800.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已取消</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N/A</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 5 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005697</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孙燕</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">建材 x 3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 25,000.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">已发货</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001239</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-24</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 6 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005698</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">周杰</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">汽车配件 x 1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 5,800.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">新订单</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">待分配</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 7 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005699</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">吴芳</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗器械 x 1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 18,000.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">处理中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001241</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-24</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 8 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005700</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑刚</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">日用品 x 50</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 900.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001242</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 9 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005701</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">林涛</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">书籍 x 15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 600.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">新订单</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">待分配</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 10 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ORD005702</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">刘霞</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">体育用品 x 3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥ 2,200.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">处理中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SHP001244</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-24</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('orderModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 车辆管理 (Vehicles) Section -->
            <section id="vehicles" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">车辆管理</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4">
                        <div class="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
                            <input type="text" placeholder="搜索车牌号、车型..." class="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80">
                            <select class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                                <option value="">所有状态</option>
                                <option value="available">空闲</option>
                                <option value="in-service">服务中</option>
                                <option value="maintenance">维护中</option>
                                <option value="broken">故障</option>
                            </select>
                            <button class="bg-blue-500 text-white px-5 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 w-full md:w-auto">
                                <i class="fas fa-filter mr-2"></i>过滤
                            </button>
                        </div>
                        <button class="bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors duration-200 mt-4 md:mt-0" onclick="openModal('vehicleModal')">
                            <i class="fas fa-plus mr-2"></i>添加车辆
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">载重(吨)</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前司机</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上次维护</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Dummy Data for Vehicles -->
                                <!-- Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">沪C12345</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重型卡车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">服务中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">粤A67890</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中型货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">京B00112</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厢式货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">故障</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 4 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">苏D33445</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">冷藏车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵六</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">维护中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 5 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙F55667</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">小型货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">钱七</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 6 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">鲁H77889</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重型卡车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孙八</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">服务中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 7 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">鄂K99001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中型货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">周九</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-05</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 8 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">川M11223</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厢式货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">吴十</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">服务中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-18</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 9 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">闽N33445</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">冷藏车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑十一</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 10 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">湘P55667</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">小型货车</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">冯十二</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">维护中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('vehicleModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 司机管理 (Drivers) Section -->
            <section id="drivers" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">司机管理</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4">
                        <div class="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
                            <input type="text" placeholder="搜索司机姓名、电话..." class="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80">
                            <select class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto">
                                <option value="">所有状态</option>
                                <option value="available">空闲</option>
                                <option value="on-duty">在岗</option>
                                <option value="on-leave">休假</option>
                            </select>
                            <button class="bg-blue-500 text-white px-5 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 w-full md:w-auto">
                                <i class="fas fa-filter mr-2"></i>过滤
                            </button>
                        </div>
                        <button class="bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors duration-200 mt-4 md:mt-0" onclick="openModal('driverModal')">
                            <i class="fas fa-plus mr-2"></i>添加司机
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">司机ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">电话</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">驾驶证号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前车辆</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Dummy Data for Drivers -->
                                <!-- Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13812345678</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110123456789</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">沪C12345</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在岗</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV002</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13987654321</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110987654321</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">粤A67890</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV003</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13700001111</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110111100000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">无</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">休假</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 4 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV004</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵六</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13622223333</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110222233333</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">苏D33445</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在岗</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 5 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV005</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">钱七</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13544445555</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110444455555</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙F55667</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 6 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV006</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孙八</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13466667777</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110666677777</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鲁H77889</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在岗</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 7 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV007</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">周九</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13388889999</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110888899999</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄂K99001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 8 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV008</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">吴十</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13200001234</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110000012345</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">川M11223</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在岗</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 9 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV009</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑十一</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13111112345</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110111123456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">闽N33445</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">空闲</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 10 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DRV010</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">冯十二</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13022223456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">110222234567</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">湘P55667</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">在岗</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('driverModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 仓库管理 (Warehouses) Section -->
            <section id="warehouses" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">仓库管理</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4">
                        <div class="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
                            <input type="text" placeholder="搜索仓库名称、地址..." class="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80">
                            <button class="bg-blue-500 text-white px-5 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 w-full md:w-auto">
                                <i class="fas fa-filter mr-2"></i>过滤
                            </button>
                        </div>
                        <button class="bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors duration-200 mt-4 md:mt-0" onclick="openModal('warehouseModal')">
                            <i class="fas fa-plus mr-2"></i>添加仓库
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总容量(m³)</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已用容量(m³)</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Dummy Data for Warehouses -->
                                <!-- Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">上海浦东仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">上海市浦东新区张江高科园区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS002</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北京大兴仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北京市大兴区亦庄经济开发区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李主管</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6200</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS003</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广州白云仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广州市白云区物流园</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 4 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS004</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成都双流仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成都市双流区航空港物流园</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵主管</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 5 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS005</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">武汉东西湖仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">武汉市东西湖区吴家山物流中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孙经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 6 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS006</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">杭州萧山仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">杭州市萧山区临江高新开发区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">11000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">周主管</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 7 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS007</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">西安国际港务区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">西安市灞桥区国际港务区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">吴经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- Row 8 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">WHS008</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑州航空港区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑州市新郑市航空港区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">郑主管</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openModal('warehouseModal', true)">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 数据报表 (Reports) Section -->
            <section id="reports" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">数据报表</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <p class="text-gray-600">这里将展示各类物流数据报表和可视化图表，如货物量趋势、订单完成率、车辆利用率、成本分析等。</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                            <h4 class="font-semibold text-gray-700 mb-2">货物状态分布</h4>
                            <div class="h-64 bg-gray-200 flex items-center justify-center text-gray-500 rounded-md">
                                <i class="fas fa-chart-pie text-4xl"></i>
                                <span class="ml-2">饼图占位符</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                            <h4 class="font-semibold text-gray-700 mb-2">每月发货量趋势</h4>
                            <div class="h-64 bg-gray-200 flex items-center justify-center text-gray-500 rounded-md">
                                <i class="fas fa-chart-bar text-4xl"></i>
                                <span class="ml-2">柱状图占位符</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                            <h4 class="font-semibold text-gray-700 mb-2">车辆里程统计</h4>
                            <div class="h-64 bg-gray-200 flex items-center justify-center text-gray-500 rounded-md">
                                <i class="fas fa-chart-line text-4xl"></i>
                                <span class="ml-2">折线图占位符</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                            <h4 class="font-semibold text-gray-700 mb-2">仓库库存概览</h4>
                            <div class="h-64 bg-gray-200 flex items-center justify-center text-gray-500 rounded-md">
                                <i class="fas fa-chart-area text-4xl"></i>
                                <span class="ml-2">面积图占位符</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 系统设置 (Settings) Section -->
            <section id="settings" class="content-section hidden">
                <h3 class="text-3xl font-bold text-gray-800 mb-6">系统设置</h3>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- User Profile Settings -->
                        <div class="border p-4 rounded-md bg-gray-50">
                            <h4 class="text-xl font-semibold text-gray-800 mb-4">个人资料设置</h4>
                            <form class="space-y-4">
                                <div>
                                    <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
                                    <input type="text" id="username" name="username" value="管理员" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700">邮箱</label>
                                    <input type="email" id="email" name="email" value="<EMAIL>" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700">电话</label>
                                    <input type="text" id="phone" name="phone" value="13800001111" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200">保存更改</button>
                            </form>
                        </div>

                        <!-- System Preferences -->
                        <div class="border p-4 rounded-md bg-gray-50">
                            <h4 class="text-xl font-semibold text-gray-800 mb-4">系统偏好设置</h4>
                            <form class="space-y-4">
                                <div>
                                    <label for="notifications" class="block text-sm font-medium text-gray-700">通知设置</label>
                                    <select id="notifications" name="notifications" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="all">所有通知</option>
                                        <option value="critical">仅关键通知</option>
                                        <option value="none">关闭通知</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="language" class="block text-sm font-medium text-gray-700">语言</label>
                                    <select id="language" name="language" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700">时区</label>
                                    <select id="timezone" name="timezone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="GMT+8">GMT+8 (北京时间)</option>
                                        <option value="GMT+0">GMT+0 (UTC)</option>
                                    </select>
                                </div>
                                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200">保存设置</button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

        </div>
    </main>

    <!-- Modals for CRUD operations -->

    <!-- Shipment Modal -->
    <div id="shipmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800" id="shipmentModalTitle">添加货物</h3>
                <button class="text-gray-500 hover:text-gray-700 text-2xl" onclick="closeModal('shipmentModal')">&times;</button>
            </div>
            <form id="shipmentForm" class="space-y-4">
                <div>
                    <label for="shipmentId" class="block text-sm font-medium text-gray-700">货物ID</label>
                    <input type="text" id="shipmentId" name="shipmentId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="自动生成或手动输入" readonly>
                </div>
                <div>
                    <label for="orderId" class="block text-sm font-medium text-gray-700">关联订单ID</label>
                    <input type="text" id="orderId" name="orderId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：ORD00123">
                </div>
                <div>
                    <label for="originAddress" class="block text-sm font-medium text-gray-700">起点地址</label>
                    <input type="text" id="originAddress" name="originAddress" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：上海市浦东新区">
                </div>
                <div>
                    <label for="destinationAddress" class="block text-sm font-medium text-gray-700">终点地址</label>
                    <input type="text" id="destinationAddress" name="destinationAddress" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：北京市朝阳区">
                </div>
                <div>
                    <label for="shipmentStatus" class="block text-sm font-medium text-gray-700">状态</label>
                    <select id="shipmentStatus" name="shipmentStatus" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="pending">待发货</option>
                        <option value="in-transit">在途中</option>
                        <option value="delivered">已送达</option>
                        <option value="exception">异常</option>
                    </select>
                </div>
                <div>
                    <label for="carrier" class="block text-sm font-medium text-gray-700">承运商</label>
                    <input type="text" id="carrier" name="carrier" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：顺丰快递">
                </div>
                <div>
                    <label for="estimatedDelivery" class="block text-sm font-medium text-gray-700">预计送达日期</label>
                    <input type="date" id="estimatedDelivery" name="estimatedDelivery" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200" onclick="closeModal('shipmentModal')">取消</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Order Modal -->
    <div id="orderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800" id="orderModalTitle">添加订单</h3>
                <button class="text-gray-500 hover:text-gray-700 text-2xl" onclick="closeModal('orderModal')">&times;</button>
            </div>
            <form id="orderForm" class="space-y-4">
                <div>
                    <label for="orderIdInput" class="block text-sm font-medium text-gray-700">订单ID</label>
                    <input type="text" id="orderIdInput" name="orderIdInput" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="自动生成或手动输入" readonly>
                </div>
                <div>
                    <label for="customerName" class="block text-sm font-medium text-gray-700">客户名称</label>
                    <input type="text" id="customerName" name="customerName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：张三">
                </div>
                <div>
                    <label for="orderContent" class="block text-sm font-medium text-gray-700">货物内容</label>
                    <textarea id="orderContent" name="orderContent" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：电子产品 x 5, 家具 x 2"></textarea>
                </div>
                <div>
                    <label for="orderAmount" class="block text-sm font-medium text-gray-700">订单金额</label>
                    <input type="number" id="orderAmount" name="orderAmount" step="0.01" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：12500.00">
                </div>
                <div>
                    <label for="orderStatus" class="block text-sm font-medium text-gray-700">状态</label>
                    <select id="orderStatus" name="orderStatus" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="new">新订单</option>
                        <option value="processing">处理中</option>
                        <option value="shipped">已发货</option>
                        <option value="completed">已完成</option>
                        <option value="cancelled">已取消</option>
                    </select>
                </div>
                <div>
                    <label for="associatedShipmentId" class="block text-sm font-medium text-gray-700">关联货物ID</label>
                    <input type="text" id="associatedShipmentId" name="associatedShipmentId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：SHP001235 (如果已发货)">
                </div>
                <div>
                    <label for="orderDate" class="block text-sm font-medium text-gray-700">下单日期</label>
                    <input type="date" id="orderDate" name="orderDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200" onclick="closeModal('orderModal')">取消</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Vehicle Modal -->
    <div id="vehicleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800" id="vehicleModalTitle">添加车辆</h3>
                <button class="text-gray-500 hover:text-gray-700 text-2xl" onclick="closeModal('vehicleModal')">&times;</button>
            </div>
            <form id="vehicleForm" class="space-y-4">
                <div>
                    <label for="licensePlate" class="block text-sm font-medium text-gray-700">车牌号</label>
                    <input type="text" id="licensePlate" name="licensePlate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：沪C12345">
                </div>
                <div>
                    <label for="vehicleType" class="block text-sm font-medium text-gray-700">车型</label>
                    <input type="text" id="vehicleType" name="vehicleType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：重型卡车">
                </div>
                <div>
                    <label for="capacity" class="block text-sm font-medium text-gray-700">载重(吨)</label>
                    <input type="number" id="capacity" name="capacity" step="0.1" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：20.0">
                </div>
                <div>
                    <label for="currentDriver" class="block text-sm font-medium text-gray-700">当前司机</label>
                    <input type="text" id="currentDriver" name="currentDriver" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：张三">
                </div>
                <div>
                    <label for="vehicleStatus" class="block text-sm font-medium text-gray-700">状态</label>
                    <select id="vehicleStatus" name="vehicleStatus" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="available">空闲</option>
                        <option value="in-service">服务中</option>
                        <option value="maintenance">维护中</option>
                        <option value="broken">故障</option>
                    </select>
                </div>
                <div>
                    <label for="lastMaintenance" class="block text-sm font-medium text-gray-700">上次维护日期</label>
                    <input type="date" id="lastMaintenance" name="lastMaintenance" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200" onclick="closeModal('vehicleModal')">取消</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Driver Modal -->
    <div id="driverModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800" id="driverModalTitle">添加司机</h3>
                <button class="text-gray-500 hover:text-gray-700 text-2xl" onclick="closeModal('driverModal')">&times;</button>
            </div>
            <form id="driverForm" class="space-y-4">
                <div>
                    <label for="driverId" class="block text-sm font-medium text-gray-700">司机ID</label>
                    <input type="text" id="driverId" name="driverId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="自动生成或手动输入" readonly>
                </div>
                <div>
                    <label for="driverName" class="block text-sm font-medium text-gray-700">姓名</label>
                    <input type="text" id="driverName" name="driverName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：张三">
                </div>
                <div>
                    <label for="driverPhone" class="block text-sm font-medium text-gray-700">电话</label>
                    <input type="tel" id="driverPhone" name="driverPhone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：13812345678">
                </div>
                <div>
                    <label for="licenseNumber" class="block text-sm font-medium text-gray-700">驾驶证号</label>
                    <input type="text" id="licenseNumber" name="licenseNumber" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：110123456789">
                </div>
                <div>
                    <label for="assignedVehicle" class="block text-sm font-medium text-gray-700">当前车辆</label>
                    <input type="text" id="assignedVehicle" name="assignedVehicle" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：沪C12345 (可选)">
                </div>
                <div>
                    <label for="driverStatus" class="block text-sm font-medium text-gray-700">状态</label>
                    <select id="driverStatus" name="driverStatus" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="available">空闲</option>
                        <option value="on-duty">在岗</option>
                        <option value="on-leave">休假</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200" onclick="closeModal('driverModal')">取消</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Warehouse Modal -->
    <div id="warehouseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800" id="warehouseModalTitle">添加仓库</h3>
                <button class="text-gray-500 hover:text-gray-700 text-2xl" onclick="closeModal('warehouseModal')">&times;</button>
            </div>
            <form id="warehouseForm" class="space-y-4">
                <div>
                    <label for="warehouseId" class="block text-sm font-medium text-gray-700">仓库ID</label>
                    <input type="text" id="warehouseId" name="warehouseId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="自动生成或手动输入" readonly>
                </div>
                <div>
                    <label for="warehouseName" class="block text-sm font-medium text-gray-700">仓库名称</label>
                    <input type="text" id="warehouseName" name="warehouseName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：上海浦东仓库">
                </div>
                <div>
                    <label for="warehouseAddress" class="block text-sm font-medium text-gray-700">地址</label>
                    <input type="text" id="warehouseAddress" name="warehouseAddress" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：上海市浦东新区张江高科园区">
                </div>
                <div>
                    <label for="totalCapacity" class="block text-sm font-medium text-gray-700">总容量(m³)</label>
                    <input type="number" id="totalCapacity" name="totalCapacity" step="1" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：10000">
                </div>
                <div>
                    <label for="usedCapacity" class="block text-sm font-medium text-gray-700">已用容量(m³)</label>
                    <input type="number" id="usedCapacity" name="usedCapacity" step="1" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：7500">
                </div>
                <div>
                    <label for="warehouseManager" class="block text-sm font-medium text-gray-700">负责人</label>
                    <input type="text" id="warehouseManager" name="warehouseManager" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如：陈经理">
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200" onclick="closeModal('warehouseModal')">取消</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript for interactivity -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const pageTitle = document.getElementById('page-title');

            // Function to show/hide sections
            function showSection(id) {
                contentSections.forEach(section => {
                    section.classList.add('hidden');
                });
                document.getElementById(id).classList.remove('hidden');
                pageTitle.textContent = document.querySelector(`.nav-link[href="#${id}"] span`).textContent;
            }

            // Handle navigation clicks
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    link.classList.add('active');
                    const targetId = link.getAttribute('href').substring(1); // Remove '#'
                    showSection(targetId);
                });
            });

            // Handle modal open/close
            window.openModal = function(modalId, isEdit = false) {
                const modal = document.getElementById(modalId);
                const titleElement = modal.querySelector(`#${modalId}Title`);
                const form = modal.querySelector('form');

                if (isEdit) {
                    titleElement.textContent = `编辑${modalId === 'shipmentModal' ? '货物' : modalId === 'orderModal' ? '订单' : modalId === 'vehicleModal' ? '车辆' : modalId === 'driverModal' ? '司机' : '仓库'}信息`;
                    // Populate form fields with dummy data for demonstration
                    // In a real application, you would fetch data based on the selected row
                    if (modalId === 'shipmentModal') {
                        document.getElementById('shipmentId').value = 'SHP001235';
                        document.getElementById('orderId').value = 'ORD005678';
                        document.getElementById('originAddress').value = '上海市浦东新区';
                        document.getElementById('destinationAddress').value = '北京市朝阳区';
                        document.getElementById('shipmentStatus').value = 'in-transit';
                        document.getElementById('carrier').value = '顺丰快递';
                        document.getElementById('estimatedDelivery').value = '2023-10-28';
                    } else if (modalId === 'orderModal') {
                        document.getElementById('orderIdInput').value = 'ORD005693';
                        document.getElementById('customerName').value = '张明';
                        document.getElementById('orderContent').value = '电子产品 x 5';
                        document.getElementById('orderAmount').value = '12500.00';
                        document.getElementById('orderStatus').value = 'processing';
                        document.getElementById('associatedShipmentId').value = 'SHP001235';
                        document.getElementById('orderDate').value = '2023-10-24';
                    } else if (modalId === 'vehicleModal') {
                        document.getElementById('licensePlate').value = '沪C12345';
                        document.getElementById('vehicleType').value = '重型卡车';
                        document.getElementById('capacity').value = '20';
                        document.getElementById('currentDriver').value = '张三';
                        document.getElementById('vehicleStatus').value = 'in-service';
                        document.getElementById('lastMaintenance').value = '2023-09-15';
                    } else if (modalId === 'driverModal') {
                        document.getElementById('driverId').value = 'DRV001';
                        document.getElementById('driverName').value = '张三';
                        document.getElementById('driverPhone').value = '13812345678';
                        document.getElementById('licenseNumber').value = '110123456789';
                        document.getElementById('assignedVehicle').value = '沪C12345';
                        document.getElementById('driverStatus').value = 'on-duty';
                    } else if (modalId === 'warehouseModal') {
                        document.getElementById('warehouseId').value = 'WHS001';
                        document.getElementById('warehouseName').value = '上海浦东仓库';
                        document.getElementById('warehouseAddress').value = '上海市浦东新区张江高科园区';
                        document.getElementById('totalCapacity').value = '10000';
                        document.getElementById('usedCapacity').value = '7500';
                        document.getElementById('warehouseManager').value = '陈经理';
                    }
                } else {
                    titleElement.textContent = `添加${modalId === 'shipmentModal' ? '货物' : modalId === 'orderModal' ? '订单' : modalId === 'vehicleModal' ? '车辆' : modalId === 'driverModal' ? '司机' : '仓库'}信息`;
                    form.reset(); // Clear form fields for new entry
                    // Auto-generate ID for new entries if applicable
                    if (modalId === 'shipmentModal') document.getElementById('shipmentId').value = 'SHP' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                    if (modalId === 'orderModal') document.getElementById('orderIdInput').value = 'ORD' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                    if (modalId === 'driverModal') document.getElementById('driverId').value = 'DRV' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                    if (modalId === 'warehouseModal') document.getElementById('warehouseId').value = 'WHS' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                }
                modal.classList.remove('hidden');
            };

            window.closeModal = function(modalId) {
                document.getElementById(modalId).classList.add('hidden');
            };

            // Close modal on outside click (optional)
            document.querySelectorAll('.fixed.inset-0').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.classList.add('hidden');
                    }
                });
            });

            // Initial load: show dashboard
            showSection('dashboard');
        });
    </script>
</body>
</html>