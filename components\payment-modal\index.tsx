'use client';

import React, { useState, useEffect } from 'react';
import { X, CreditCard, Clock, CheckCircle, AlertCircle, Loader2, Crown, Sparkles, Gift } from 'lucide-react';

// 订单类型定义
interface OrderData {
  id: number;
  order_no: string;
  order_type: 'subscription' | 'recharge';
  amount: number;
  original_price: number;
  discount_price: number;
  status: 'pending' | 'paid' | 'cancelled' | 'expired' | 'refunded';
  expires_at: string;
  created_at: string;
  // 订阅订单特有字段
  membership_type?: 'pro' | 'max';
  plan_key?: string;
  duration_months?: number;
  points_included?: number;
  // 充值订单特有字段
  package_key?: string;
  points_amount?: number;
  bonus_points?: number;
  points_validity_days?: number;
}

// 支付方式类型
type PaymentMethod = 'alipay' | 'wechat' | 'mock';

// 弹窗视图状态
type ModalView = 'payment' | 'processing' | 'success' | 'error' | 'pending_order';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId?: number;
  orderData?: OrderData;
  onPaymentSuccess?: () => void;
}

export default function PaymentModal({
  isOpen,
  onClose,
  orderId,
  orderData: initialOrderData,
  onPaymentSuccess
}: PaymentModalProps) {
  const [currentView, setCurrentView] = useState<ModalView>('payment');
  const [orderData, setOrderData] = useState<OrderData | null>(initialOrderData || null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('alipay');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [hasPendingOrder, setHasPendingOrder] = useState(false);
  const [pendingOrderInfo, setPendingOrderInfo] = useState<any>(null);

  // 获取订单数据
  useEffect(() => {
    if (isOpen && orderId) {
      // 总是重新获取最新的订单数据，忽略 initialOrderData
      console.log(`🚀 支付弹窗打开，重新获取订单数据: orderId=${orderId}`);
      fetchOrderData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, orderId]);

  // 检查是否有其他pending订单
  useEffect(() => {
    if (isOpen && orderData) {
      checkForOtherPendingOrders();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, orderData]);

  // 倒计时逻辑
  useEffect(() => {
    if (!orderData?.expires_at) {
      console.log('⚠️ 订单数据中没有过期时间');
      return;
    }

    const updateTimer = () => {
      const now = new Date().getTime();
      const expiresAt = new Date(orderData.expires_at).getTime();
      const remaining = Math.max(0, expiresAt - now);

      // 添加调试日志
      console.log(`⏰ 倒计时更新: 当前时间=${new Date(now).toLocaleString()}, 过期时间=${new Date(expiresAt).toLocaleString()}, 剩余=${Math.floor(remaining/1000)}秒`);

      setTimeRemaining(remaining);

      if (remaining === 0) {
        console.log('❌ 订单已过期');
        setError('订单已过期');
        setCurrentView('error');
      }
    };

    updateTimer();
    const timer = setInterval(updateTimer, 1000);
    return () => clearInterval(timer);
  }, [orderData?.expires_at]);

  // 获取订单数据
  const fetchOrderData = async () => {
    if (!orderId) return;

    try {
      setLoading(true);
      console.log(`🔄 正在获取订单数据: orderId=${orderId}`);

      const response = await fetch(`/api/orders/${orderId}?t=${Date.now()}`); // 添加时间戳防止缓存
      const result = await response.json();

      console.log('📦 获取到的订单数据:', result);

      if (result.success) {
        console.log(`✅ 订单数据获取成功: 订单号=${result.data.order_no}, 过期时间=${result.data.expires_at}`);
        setOrderData(result.data);
      } else {
        console.error('❌ 获取订单数据失败:', result);
        setError(result.error || '获取订单信息失败');
        setCurrentView('error');
      }
    } catch (error) {
      console.error('获取订单数据失败:', error);
      setError('网络错误，请重试');
      setCurrentView('error');
    } finally {
      setLoading(false);
    }
  };

  // 检查是否有其他pending订单
  const checkForOtherPendingOrders = async () => {
    if (!orderData) return;

    try {
      const orderType = orderData.order_type;
      const response = await fetch(`/api/orders/pending?type=${orderType}`);
      const result = await response.json();

      if (result.success && result.data && result.data.id !== orderData.id) {
        // 检查pending订单是否真的有效（前端二次验证）
        const now = new Date().getTime();
        const expiresAt = new Date(result.data.expires_at).getTime();
        const isExpired = now >= expiresAt;

        console.log('🔍 发现其他pending订单:', result.data);
        console.log(`   订单过期时间: ${new Date(result.data.expires_at).toLocaleString()}`);
        console.log(`   当前时间: ${new Date(now).toLocaleString()}`);
        console.log(`   是否过期: ${isExpired}`);

        if (!isExpired) {
          // 订单确实有效，显示未完成订单界面
          setPendingOrderInfo(result.data);
          setCurrentView('pending_order');
        } else {
          console.log('⚠️ pending订单已过期，忽略并继续当前支付流程');
        }
      } else {
        console.log('✅ 没有其他pending订单，继续当前支付流程');
      }
    } catch (error) {
      console.error('检查pending订单失败:', error);
    }
  };

  // 处理支付
  const handlePayment = async () => {
    if (!orderData) return;

    try {
      setCurrentView('processing');

      const response = await fetch(`/api/orders/${orderData.id}/pay`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method: selectedPaymentMethod,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentView('success');
        onPaymentSuccess?.();
      } else {
        setError(result.error || '支付失败');
        setCurrentView('error');
      }
    } catch (error) {
      console.error('支付处理失败:', error);
      setError('网络错误，请重试');
      setCurrentView('error');
    }
  };

  // 格式化时间显示
  const formatTimeRemaining = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // 处理取消pending订单并创建新订单
  const handleCancelPendingAndCreateNew = async () => {
    if (!pendingOrderInfo) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/orders/${pendingOrderInfo.id}/cancel`, {
        method: 'POST'
      });

      if (response.ok) {
        // 取消成功，关闭弹窗让用户重新创建订单
        handleClose();
      } else {
        setError('取消订单失败，请重试');
        setCurrentView('error');
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      setError('网络错误，请重试');
      setCurrentView('error');
    } finally {
      setLoading(false);
    }
  };

  // 处理继续支付pending订单
  const handleContinuePendingPayment = () => {
    if (!pendingOrderInfo) return;

    // 切换到当前pending订单
    setOrderData(pendingOrderInfo);
    setPendingOrderInfo(null);
    setCurrentView('payment');
  };

  // 关闭弹窗
  const handleClose = () => {
    setCurrentView('payment');
    setError('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm">
      {/* 背景遮罩 - 与订阅弹窗完全一致 */}
      <div
        className="absolute inset-0"
        onClick={handleClose}
      />

      {/* 弹窗内容 - 与订阅弹窗完全一致的尺寸和位置 */}
      <div className="absolute left-1/2 top-[10vh] transform -translate-x-1/2 w-full max-w-6xl h-[80vh] max-h-[800px] min-h-[580px] bg-white dark:bg-[#0f0f0f] rounded-2xl shadow-2xl overflow-hidden z-10 mx-4 border border-gray-200/50 dark:border-gray-600/50 backdrop-blur-xl flex flex-col">
        {/* 顶部栏 - 与订阅弹窗完全一致 */}
        <div className="relative px-8 py-3 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 dark:from-[#0f0f0f] dark:via-[#0f0f0f] dark:to-[#0f0f0f] border-b border-gray-200/50 dark:border-gray-600/30 flex-shrink-0">
          <button
            onClick={handleClose}
            className="absolute top-2 right-6 w-8 h-8 rounded-full bg-white/80 dark:bg-[#1a1a1a]/80 hover:bg-white dark:hover:bg-[#2a2a2a] border border-gray-200/50 dark:border-gray-600/50 flex items-center justify-center transition-all duration-200 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 shadow-md z-10"
          >
            <X className="w-5 h-5" />
          </button>

          <div className="flex items-center gap-3">
            <div className="transition-all duration-300 ease-out hover:scale-110 hover:-translate-y-0.5 hover:drop-shadow-lg">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-white" />
              </div>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                {currentView === 'payment' && '订单支付'}
                {currentView === 'processing' && '支付处理中'}
                {currentView === 'success' && '支付成功'}
                {currentView === 'error' && '支付失败'}
                {currentView === 'pending_order' && '未完成订单'}
              </h1>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto bg-gradient-to-br from-gray-50/20 to-white dark:from-[#0f0f0f] dark:to-[#0f0f0f]">
          {loading ? (
            <div className="flex items-center justify-center h-full min-h-[400px]">
              <div className="flex flex-col items-center gap-6">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-500 dark:border-t-blue-400 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-500 dark:border-r-purple-400 rounded-full animate-spin animation-delay-150"></div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">正在加载支付信息...</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center min-h-[500px] p-6">
              <div className="w-full">
                {currentView === 'payment' && renderPaymentView()}
                {currentView === 'processing' && renderProcessingView()}
                {currentView === 'success' && renderSuccessView()}
                {currentView === 'error' && renderErrorView()}
                {currentView === 'pending_order' && renderPendingOrderView()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // 支付视图
  function renderPaymentView() {
    if (!orderData) return null;

    return (
      <div className="flex h-full min-h-0">
        {/* 左侧订单信息 - 扩大宽度以平衡布局 */}
        <div className="w-[480px] bg-gray-50/80 dark:bg-[#0f0f0f] border-l border-gray-200/50 dark:border-gray-600/30 flex flex-col min-h-0">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-[#0f0f0f] flex-shrink-0">
              <div className="mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white tracking-wide">
                    订单详情
                  </h3>
                </div>
              </div>

              <div className="space-y-0">
                {/* 商品明细 - 简洁显示 */}
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                    商品明细
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {orderData.order_type === 'subscription'
                            ? `${orderData.membership_type?.toUpperCase()}会员`
                            : `${orderData.points_amount?.toLocaleString()}积分套餐`
                          }
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {orderData.order_type === 'subscription'
                            ? `${orderData.duration_months}个月订阅`
                            : '积分套餐 × 1'
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 订单详情 - 合并订单信息和权益 */}
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                    订单详情
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">订单编号</span>
                      <span className="font-mono text-blue-600 dark:text-blue-400 font-medium text-sm">
                        {orderData.order_no}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">创建时间</span>
                      <span className="font-mono text-gray-900 dark:text-white text-sm">
                        {new Date(orderData.created_at).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>

                    {/* 权益信息 */}
                    {orderData.order_type === 'subscription' ? (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">每月积分</span>
                          <span className="font-mono text-emerald-600 dark:text-emerald-400 font-medium">
                            +{orderData.points_included?.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">积分有效期</span>
                          <span className="font-mono text-gray-900 dark:text-white">
                            30天
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">发放方式</span>
                          <span className="font-mono text-gray-900 dark:text-white">
                            {orderData.duration_months === 1 ? '立即到账' : '按月发放'}
                          </span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">获得积分</span>
                          <span className="font-mono font-semibold text-emerald-600 dark:text-emerald-400">
                            +{orderData.points_amount?.toLocaleString()}
                          </span>
                        </div>
                        {orderData.bonus_points && orderData.bonus_points > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">赠送积分</span>
                            <span className="font-mono text-emerald-600 dark:text-emerald-400">
                              +{orderData.bonus_points.toLocaleString()}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">积分有效期</span>
                          <span className="font-mono text-gray-900 dark:text-white">永久有效</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* 价格明细 - PI单据样式 */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                    价格明细
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">商品原价</span>
                      <span className="font-mono text-gray-900 dark:text-white">¥{Number(orderData.original_price).toFixed(2)}</span>
                    </div>

                    {orderData.original_price > orderData.discount_price && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">限时优惠 (-{Math.round(((orderData.original_price - orderData.discount_price) / orderData.original_price) * 100)}%)</span>
                        <span className="font-mono text-emerald-600 dark:text-emerald-400">
                          -¥{(orderData.original_price - orderData.discount_price).toFixed(2)}
                        </span>
                      </div>
                    )}

                    {/* 总计 - PI单据样式 */}
                    <div className="border-t border-gray-300 dark:border-gray-600 pt-3 mt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-base font-bold text-gray-900 dark:text-white uppercase tracking-wide">合计</span>
                        <div className="text-right">
                          <div className="text-2xl font-bold font-mono text-gray-900 dark:text-white">¥{Number(orderData.discount_price).toFixed(2)}</div>
                          {orderData.original_price > orderData.discount_price && (
                            <div className="text-sm text-gray-500 dark:text-gray-400 line-through font-mono">¥{Number(orderData.original_price).toFixed(2)}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 倒计时区域 */}
          <div className="p-6 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-t border-orange-200/50 dark:border-orange-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium text-orange-800 dark:text-orange-300">订单剩余时间</span>
              </div>
              <span className="font-mono text-xl font-bold text-orange-600 dark:text-orange-400">
                {formatTimeRemaining(timeRemaining)}
              </span>
            </div>
          </div>
        </div>

        {/* 右侧支付区域 - 居中布局优化 */}
        <div className="flex-1 bg-white dark:bg-[#0f0f0f] border-l border-gray-200/50 dark:border-gray-600/30 flex flex-col min-h-0">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {/* 标题靠近分割线 */}
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-1 h-6 bg-gradient-to-b from-green-500 to-blue-600 rounded-full"></div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">选择支付方式</h3>
                </div>
              </div>

              {/* 支付方式选择 */}
              <div className="mb-8">
                <div className="grid grid-cols-2 gap-3">
                  {/* 支付宝 */}
                  <button
                    onClick={() => setSelectedPaymentMethod('alipay')}
                    className={`p-4 border-2 rounded-xl transition-all duration-300 ${
                      selectedPaymentMethod === 'alipay'
                        ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 shadow-lg scale-105'
                        : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 hover:shadow-md hover:scale-102'
                    }`}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                        <span className="text-white text-sm font-bold">支</span>
                      </div>
                      <span className="text-xs font-bold text-gray-900 dark:text-white">支付宝</span>
                    </div>
                  </button>

                  {/* 微信支付 */}
                  <button
                    onClick={() => setSelectedPaymentMethod('wechat')}
                    className={`p-4 border-2 rounded-xl transition-all duration-300 ${
                      selectedPaymentMethod === 'wechat'
                        ? 'border-green-500 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 shadow-lg scale-105'
                        : 'border-gray-200 dark:border-gray-600 hover:border-green-300 hover:shadow-md hover:scale-102'
                    }`}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                        <span className="text-white text-sm font-bold">微</span>
                      </div>
                      <span className="text-xs font-bold text-gray-900 dark:text-white">微信支付</span>
                    </div>
                  </button>
                </div>
              </div>

              {/* 二维码展示区域 */}
              <div className="bg-white dark:bg-[#1a1a1a] rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-48 h-48 bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-2xl flex items-center justify-center shadow-inner">
                    <div className="text-center">
                      <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-xl mx-auto mb-3 flex items-center justify-center">
                        <div className="w-16 h-16 bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-500 rounded-lg"></div>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">扫码支付</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center font-medium">
                    请使用{selectedPaymentMethod === 'alipay' ? '支付宝' : '微信'}扫码支付
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 底部支付按钮 */}
          <div className="p-6 bg-white/90 dark:bg-[#0f0f0f] border-t border-gray-200/50 dark:border-gray-600/30">
            <button
              onClick={handlePayment}
              className="w-full h-14 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl shadow-lg flex items-center justify-center gap-3 relative overflow-hidden cursor-pointer border-none transition-all duration-300 hover:shadow-2xl group"
              style={{
                boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <CreditCard className="w-5 h-5 z-10 relative" />
              <span className="text-lg z-10 relative">模拟支付成功 ¥{Number(orderData?.discount_price || 0).toFixed(2)}</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 支付处理中视图
  function renderProcessingView() {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-[#1a1a1a] dark:to-[#0f0f0f] rounded-2xl p-12 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
          <div className="flex flex-col items-center justify-center space-y-6">
            <div className="relative">
              <Loader2 className="w-16 h-16 animate-spin text-blue-600" />
              <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-500 dark:border-r-purple-400 rounded-full animate-spin animation-delay-150"></div>
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">支付处理中...</h3>
              <p className="text-base text-gray-600 dark:text-gray-400">
                请稍候，正在处理您的支付请求
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 支付成功视图
  function renderSuccessView() {
    if (!orderData) return null;

    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-[#1a1a1a] dark:to-[#0f0f0f] rounded-2xl p-8 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
          <div className="flex flex-col items-center justify-center space-y-8">
            {/* 成功图标 */}
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                <CheckCircle className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -inset-2 bg-green-400/20 rounded-full animate-ping"></div>
            </div>

            {/* 成功信息 */}
            <div className="text-center space-y-3">
              <h3 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
                支付成功！
              </h3>
              <p className="text-base text-gray-600 dark:text-gray-400">
                订单 <span className="font-mono font-bold text-blue-600 dark:text-blue-400">{orderData.order_no}</span> 已完成支付
              </p>
            </div>

            {/* 获得的权益 */}
            <div className="w-full bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/30">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1 h-6 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full"></div>
                <h4 className="text-lg font-bold text-green-800 dark:text-green-400">您已获得：</h4>
              </div>
              {orderData.order_type === 'subscription' ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                    <Crown className="w-5 h-5 text-yellow-600" />
                    <span className="font-bold text-gray-900 dark:text-white">
                      {orderData.membership_type?.toUpperCase()}会员 {orderData.duration_months}个月
                    </span>
                  </div>
                  {orderData.points_included && (
                    <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                      <Sparkles className="w-5 h-5 text-blue-600" />
                      <span className="font-bold text-gray-900 dark:text-white">
                        {orderData.points_included.toLocaleString()}积分
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                    <span className="font-bold text-gray-900 dark:text-white">
                      {orderData.points_amount?.toLocaleString()}积分
                    </span>
                  </div>
                  {orderData.bonus_points && orderData.bonus_points > 0 && (
                    <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                      <Gift className="w-5 h-5 text-purple-600" />
                      <span className="font-bold text-gray-900 dark:text-white">
                        {orderData.bonus_points.toLocaleString()}赠送积分
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 完成按钮 */}
            <button
              onClick={handleClose}
              className="w-full h-14 bg-gradient-to-r from-green-600 via-emerald-600 to-green-700 text-white font-bold rounded-2xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center gap-3 text-lg overflow-hidden group"
              style={{
                boxShadow: '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <CheckCircle className="w-6 h-6 relative z-10" />
              <span className="relative z-10">完成</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 错误视图
  function renderErrorView() {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-[#1a1a1a] dark:to-[#0f0f0f] rounded-2xl p-8 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
          <div className="flex flex-col items-center justify-center space-y-8">
            {/* 错误图标 */}
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                <AlertCircle className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -inset-2 bg-red-400/20 rounded-full animate-pulse"></div>
            </div>

            {/* 错误信息 */}
            <div className="text-center space-y-3">
              <h3 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
                支付失败
              </h3>
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800/30">
                <p className="text-base text-red-700 dark:text-red-300 font-medium">
                  {error === '订单已过期' ? (
                    <>
                      <Clock className="w-5 h-5 inline mr-2" />
                      订单已过期，请重新创建订单
                    </>
                  ) : (
                    error || '支付过程中出现未知错误，请重试'
                  )}
                </p>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-4 w-full">
              <button
                onClick={handleClose}
                className="flex-1 h-14 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-bold rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2"
              >
                <X className="w-5 h-5" />
                关闭
              </button>
              <button
                onClick={() => {
                  setCurrentView('payment');
                  setError('');
                  // 如果是订单过期，重新获取订单数据
                  if (error === '订单已过期') {
                    fetchOrderData();
                  }
                }}
                className="flex-1 h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 text-white font-bold rounded-2xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center gap-3 text-lg overflow-hidden group"
                style={{
                  boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <AlertCircle className="w-6 h-6 relative z-10" />
                <span className="relative z-10">重试</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 未完成订单视图
  function renderPendingOrderView() {
    if (!pendingOrderInfo) return null;

    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-[#1a1a1a] dark:to-[#0f0f0f] rounded-2xl p-8 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
          <div className="flex flex-col items-center justify-center space-y-8">
            {/* 警告图标 */}
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
                <AlertCircle className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -inset-2 bg-orange-400/20 rounded-full animate-pulse"></div>
            </div>

            {/* 提示信息 */}
            <div className="text-center space-y-4">
              <h3 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-orange-800 bg-clip-text text-transparent">
                您有未完成的订单
              </h3>
              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-6 border border-orange-200 dark:border-orange-800/30">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">订单编号</span>
                    <span className="font-mono text-orange-700 dark:text-orange-300 font-bold">{pendingOrderInfo.order_no}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">订单金额</span>
                    <span className="font-mono text-orange-700 dark:text-orange-300 font-bold text-lg">¥{pendingOrderInfo.amount}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">创建时间</span>
                    <span className="text-sm text-gray-700 dark:text-gray-300">{new Date(pendingOrderInfo.created_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>
              <p className="text-base text-gray-600 dark:text-gray-400">
                请选择继续支付现有订单，或取消后重新下单
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-4 w-full">
              <button
                onClick={handleCancelPendingAndCreateNew}
                disabled={loading}
                className="flex-1 h-14 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-bold rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50"
              >
                <X className="w-5 h-5" />
                取消重新下单
              </button>
              <button
                onClick={handleContinuePendingPayment}
                disabled={loading}
                className="flex-1 h-14 bg-gradient-to-r from-orange-600 via-orange-700 to-orange-800 text-white font-bold rounded-2xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center gap-3 text-lg overflow-hidden group disabled:opacity-50"
                style={{
                  boxShadow: '0 10px 25px rgba(234, 88, 12, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <CreditCard className="w-6 h-6 relative z-10" />
                <span className="relative z-10">继续支付</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}