/**
 * 🚀 企业级性能监控系统
 * 专门用于监控社区项目加载性能
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  slowQueries: number;
  errorRate: number;
  lastUpdated: number;
}

export class PerformanceLogger {
  private static instance: PerformanceLogger;
  private metrics: Map<string, PerformanceMetric> = new Map();
  private stats: PerformanceStats = {
    totalRequests: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    slowQueries: 0,
    errorRate: 0,
    lastUpdated: Date.now()
  };
  
  // 🎯 性能阈值配置 - 优化阈值，减少误报
  private readonly THRESHOLDS = {
    SLOW_QUERY: 1000,   // 1秒以上为慢查询 (从500ms调整)
    VERY_SLOW: 3000,    // 3秒以上为极慢查询 (从1秒调整)
    WARNING: 5000,      // 5秒以上发出警告 (从2秒调整)
    ERROR: 10000        // 10秒以上视为错误 (从5秒调整)
  };

  private constructor() {
    // 🔄 定期清理老旧指标
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanupOldMetrics(), 5 * 60 * 1000); // 5分钟清理一次
    }
  }

  static getInstance(): PerformanceLogger {
    if (!PerformanceLogger.instance) {
      PerformanceLogger.instance = new PerformanceLogger();
    }
    return PerformanceLogger.instance;
  }

  // 🚀 开始性能测量
  static startTimer(name: string, metadata?: Record<string, any>): void {
    const logger = PerformanceLogger.getInstance();
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata
    };
    
    logger.metrics.set(name, metric);
    
    // 🔧 只在开发环境记录详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ 开始测量: ${name}`, metadata);
    }
  }

  // 🏁 结束性能测量 - 优化日志输出
  static endTimer(name: string, additionalMetadata?: Record<string, any>): number {
    const logger = PerformanceLogger.getInstance();
    const metric = logger.metrics.get(name);
    
    if (!metric) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`⚠️ 性能测量未找到: ${name}`);
      }
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    // 🔧 修复：检查duration是否为有效数值
    const safeDuration = Number.isFinite(duration) && duration >= 0 ? duration : 0;
    if (safeDuration !== duration && process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ 性能测量时间异常: ${name}, 原始duration: ${duration}, 修正为: ${safeDuration}`);
    }
    
    // 更新指标
    metric.endTime = endTime;
    metric.duration = safeDuration;
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    // 🔄 更新统计数据
    logger.updateStats(safeDuration);

    // 🎯 性能分析和警告 - 优化警告条件
    logger.analyzePerformance(name, safeDuration, metric.metadata);

    // 🔧 优化开发环境日志：只显示真正需要关注的性能问题
    if (process.env.NODE_ENV === 'development') {
      if (safeDuration > logger.THRESHOLDS.WARNING) {
        const color = safeDuration > logger.THRESHOLDS.ERROR ? '🔴' : '🟡';
        console.log(`${color} 性能警告: ${name} - ${safeDuration.toFixed(1)}ms`, metric.metadata);
      } else if (safeDuration > logger.THRESHOLDS.VERY_SLOW) {
        console.log(`🟠 较慢操作: ${name} - ${safeDuration.toFixed(1)}ms`);
      }
      // 正常速度的操作不再显示日志，减少噪音
    }

    return safeDuration;
  }

  // 📊 记录API调用
  static logApiCall(
    endpoint: string, 
    method: string, 
    duration: number, 
    status: number, 
    fromCache: boolean = false,
    metadata?: Record<string, any>
  ): void {
    const logger = PerformanceLogger.getInstance();
    
    // 🔧 修复：确保duration是有效数值
    const safeDuration = Number.isFinite(duration) && duration >= 0 ? duration : 0;
    if (safeDuration !== duration) {
      console.warn(`⚠️ API调用时间异常: ${endpoint}, 原始duration: ${duration}, 修正为: ${safeDuration}`);
    }
    
    // 🔄 更新统计数据
    logger.updateStats(safeDuration, fromCache, status >= 400);

    const logData = {
      endpoint,
      method,
      duration: `${safeDuration.toFixed(1)}ms`,
      status,
      fromCache,
      timestamp: new Date().toISOString(),
      ...metadata
    };

    if (process.env.NODE_ENV === 'development') {
      const statusColor = status >= 400 ? '🔴' : status >= 300 ? '🟡' : '🟢';
      const cacheStatus = fromCache ? '💾' : '🌐';
      const speedStatus = safeDuration > logger.THRESHOLDS.SLOW_QUERY ? '🐌' : '⚡';
      
      console.log(`${statusColor}${cacheStatus}${speedStatus} API调用:`, logData);
    }

    // 🚨 慢查询警告
    if (safeDuration > logger.THRESHOLDS.WARNING) {
      console.warn(`⚠️ 极慢API调用: ${endpoint} - ${safeDuration.toFixed(1)}ms`, logData);
    }
  }

  // 🔍 记录数据库查询性能
  static logDatabaseQuery(
    queryType: string,
    duration: number,
    resultCount: number,
    fromCache: boolean = false
  ): void {
    const logger = PerformanceLogger.getInstance();
    
    const logData = {
      queryType,
      duration: `${duration.toFixed(1)}ms`,
      resultCount,
      fromCache,
      timestamp: new Date().toISOString()
    };

    if (process.env.NODE_ENV === 'development') {
      const cacheStatus = fromCache ? '💾' : '🗄️';
      const speedStatus = duration > logger.THRESHOLDS.SLOW_QUERY ? '🐌' : '⚡';
      
      console.log(`${cacheStatus}${speedStatus} DB查询:`, logData);
    }

    // 🔄 更新慢查询计数
    if (duration > logger.THRESHOLDS.SLOW_QUERY) {
      logger.stats.slowQueries++;
    }
  }

  // 📈 记录缓存性能
  static logCacheOperation(
    operation: 'hit' | 'miss' | 'set' | 'clear',
    key: string,
    duration?: number
  ): void {
    if (process.env.NODE_ENV === 'development') {
      const operationEmoji = {
        hit: '✅',
        miss: '❌', 
        set: '💾',
        clear: '🗑️'
      }[operation];

      console.log(`${operationEmoji} 缓存${operation}: ${key}${duration ? ` (${duration.toFixed(1)}ms)` : ''}`);
    }
  }

  // 📊 获取性能统计
  static getStats(): PerformanceStats {
    return PerformanceLogger.getInstance().stats;
  }

  // 📈 获取详细性能报告
  static getDetailedReport(): {
    currentMetrics: PerformanceMetric[];
    stats: PerformanceStats;
    recommendations: string[];
  } {
    const logger = PerformanceLogger.getInstance();
    const currentMetrics = Array.from(logger.metrics.values());
    const recommendations: string[] = [];

    // 🎯 生成优化建议
    if (logger.stats.averageResponseTime > logger.THRESHOLDS.SLOW_QUERY) {
      recommendations.push('响应时间较慢，建议优化查询或增加缓存');
    }
    if (logger.stats.cacheHitRate < 0.7) {
      recommendations.push('缓存命中率较低，建议优化缓存策略');
    }
    if (logger.stats.errorRate > 0.05) {
      recommendations.push('错误率较高，建议检查API稳定性');
    }

    return {
      currentMetrics,
      stats: logger.stats,
      recommendations
    };
  }

  // 🎯 通用日志方法
  static log(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${message}`, data);
    }
  }

  static warn(message: string, data?: any): void {
    console.warn(`⚠️ ${message}`, data);
  }

  static error(message: string, data?: any): void {
    console.error(`❌ ${message}`, data);
  }

  // 🔄 私有方法：更新统计数据
  private updateStats(duration: number, fromCache: boolean = false, isError: boolean = false): void {
    this.stats.totalRequests++;
    
    // 计算移动平均响应时间
    const weight = Math.min(this.stats.totalRequests, 100); // 最多考虑最近100次请求
    this.stats.averageResponseTime = (
      (this.stats.averageResponseTime * (weight - 1) + duration) / weight
    );

    // 更新缓存命中率
    if (this.stats.totalRequests <= 100) {
      const cacheHits = fromCache ? 1 : 0;
      this.stats.cacheHitRate = (
        (this.stats.cacheHitRate * (this.stats.totalRequests - 1) + cacheHits) / 
        this.stats.totalRequests
      );
    }

    // 更新错误率
    if (isError) {
      this.stats.errorRate = (
        (this.stats.errorRate * (this.stats.totalRequests - 1) + 1) / 
        this.stats.totalRequests
      );
    } else if (this.stats.totalRequests > 1) {
      this.stats.errorRate = (
        (this.stats.errorRate * (this.stats.totalRequests - 1)) / 
        this.stats.totalRequests
      );
    }

    this.stats.lastUpdated = Date.now();
  }

  // 🎯 私有方法：性能分析 - 优化警告逻辑
  private analyzePerformance(name: string, duration: number, metadata?: Record<string, any>): void {
    // 🔧 特殊处理：AI相关操作的阈值更宽松
    const isAIOperation = name.includes('ai-') || name.includes('AI') || name.includes('chat');
    const adjustedErrorThreshold = isAIOperation ? this.THRESHOLDS.ERROR * 2 : this.THRESHOLDS.ERROR;
    const adjustedWarningThreshold = isAIOperation ? this.THRESHOLDS.WARNING * 1.5 : this.THRESHOLDS.WARNING;
    
    if (duration > adjustedErrorThreshold) {
      console.error(`🚨 极慢操作: ${name} - ${duration.toFixed(1)}ms`, metadata);
    } else if (duration > adjustedWarningThreshold) {
      console.warn(`⚠️ 慢操作: ${name} - ${duration.toFixed(1)}ms`, metadata);
    } else if (duration > this.THRESHOLDS.SLOW_QUERY) {
      this.stats.slowQueries++;
      // 🔧 慢查询不再输出警告日志，只记录统计
    }
  }

  // 🧹 私有方法：清理老旧指标
  private cleanupOldMetrics(): void {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    for (const [name, metric] of this.metrics.entries()) {
      if (now - metric.startTime > maxAge) {
        this.metrics.delete(name);
      }
    }
  }
}

// 🚀 导出单例实例
export const performanceLogger = PerformanceLogger.getInstance(); 