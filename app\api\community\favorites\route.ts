import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getUserByToken } from '@/lib/auth-service';
import mysql from 'mysql2/promise';

// 数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+08:00',
};

// 专用的查询函数，避免连接池问题
const executeSimpleQuery = async (query: string, params: (string | number)[] = []) => {
  let connection;
  try {
    console.log('🔗 创建新的数据库连接');
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('📝 执行查询:', { query: query.replace(/\s+/g, ' ').trim(), params });
    const [results] = await connection.execute(query, params);
    console.log('✅ 查询成功');
    return results;
  } catch (error) {
    console.error('❌ 查询失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 连接已关闭');
    }
  }
};

// 获取用户的收藏列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

        console.log('🔍 收藏API: 执行查询', { userId: user.id, limit, offset, userIdType: typeof user.id });
    
    // 首先获取用户的收藏ID列表 - 使用更兼容的语法
    const favoriteIdsQuery = `SELECT project_id, created_at as favorited_at FROM project_favorites WHERE user_id = ? ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`;
    
    const favoriteIds = await executeSimpleQuery(favoriteIdsQuery, [user.id]) as any[];
    
    console.log('📊 用户收藏ID列表:', favoriteIds.length);
    
    if (favoriteIds.length === 0) {
      console.log('✅ 用户无收藏，返回空列表');
      return NextResponse.json({
        favorites: [],
        pagination: {
          total: 0,
          limit,
          offset,
          hasMore: false
        }
      });
    }
    
    // 为每个收藏的项目获取详细信息
    const favorites = await Promise.all(favoriteIds.map(async (fav: any) => {
      // 获取项目信息
      const projectQuery = `
        SELECT id, original_project_id, user_id, title, html_content, created_at, updated_at
        FROM community_projects 
        WHERE id = ?
      `;
             const projectResult = await executeSimpleQuery(projectQuery, [fav.project_id]) as any[];
      
      if (projectResult.length === 0) return null;
      
      const project = projectResult[0];
      
      // 获取作者信息
      const authorQuery = `
        SELECT nickname, phone, avatar_url
        FROM users 
        WHERE id = ?
      `;
             const authorResult = await executeSimpleQuery(authorQuery, [project.user_id]) as any[];
      const author = authorResult[0] || {};
      
      return {
        ...project,
        favorited_at: fav.favorited_at,
        author_name: author.nickname,
        author_phone: author.phone,
        author_avatar_url: author.avatar_url
      };
    }));
    
    // 过滤掉null值
    const validFavorites = favorites.filter(f => f !== null);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM project_favorites pf
      JOIN community_projects cp ON pf.project_id = cp.id
      WHERE pf.user_id = ?
    `;
    const countResult = await executeSimpleQuery(countQuery, [user.id]) as any[];
    const total = countResult[0]?.total || 0;

    // 格式化返回数据 - 为每个项目单独获取收藏数量
    const formattedFavorites = await Promise.all(validFavorites.map(async (item: any) => {
      // 获取每个项目的收藏数量
      const countResult = await executeSimpleQuery(
        'SELECT COUNT(*) as count FROM project_favorites WHERE project_id = ?',
        [item.id]
      ) as any[];
      const favoritesCount = countResult[0]?.count || 0;

      return {
        id: item.id,
        originalProjectId: item.original_project_id,
        userId: item.user_id,
        title: item.title,
        htmlContent: item.html_content,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        favoritedAt: item.favorited_at,
        author: {
          name: item.author_name,
          phone: item.author_phone,
          avatar_url: item.author_avatar_url
        },
        favoritesCount,
        isFavorited: true
      };
    }));

    return NextResponse.json({
      favorites: formattedFavorites,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error) {
    console.error('获取收藏列表失败:', error);
    return NextResponse.json({ error: '获取收藏列表失败' }, { status: 500 });
  }
}

// 添加或取消收藏
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const { projectId, action } = await request.json();

    if (!projectId || !action) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 检查项目是否存在
    const projectExists = await executeSimpleQuery(
      'SELECT id FROM community_projects WHERE id = ?',
      [projectId]
    ) as any[];

    if (!projectExists.length) {
      return NextResponse.json({ error: '项目不存在' }, { status: 404 });
    }

    if (action === 'add') {
      // 添加收藏
      try {
        await executeSimpleQuery(
          'INSERT INTO project_favorites (user_id, project_id) VALUES (?, ?)',
          [user.id, projectId]
        );
      } catch (error: any) {
        // 如果是重复收藏，忽略错误
        if (error.code !== 'ER_DUP_ENTRY') {
          throw error;
        }
      }
    } else if (action === 'remove') {
      // 取消收藏
      await executeSimpleQuery(
        'DELETE FROM project_favorites WHERE user_id = ? AND project_id = ?',
        [user.id, projectId]
      );
    } else {
      return NextResponse.json({ error: '无效的操作' }, { status: 400 });
    }

    // 获取更新后的收藏数量
    const countResult = await executeSimpleQuery(
      'SELECT COUNT(*) as count FROM project_favorites WHERE project_id = ?',
      [projectId]
    ) as any[];
    const favoritesCount = countResult[0]?.count || 0;

    // 检查用户是否已收藏
    const userFavoriteResult = await executeSimpleQuery(
      'SELECT id FROM project_favorites WHERE user_id = ? AND project_id = ?',
      [user.id, projectId]
    ) as any[];
    const isFavorited = userFavoriteResult.length > 0;

    return NextResponse.json({
      success: true,
      favoritesCount,
      isFavorited
    });

  } catch (error) {
    console.error('收藏操作失败:', error);
    return NextResponse.json({ error: '收藏操作失败' }, { status: 500 });
  }
} 