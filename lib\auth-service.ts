import jwt from 'jsonwebtoken';
import { executeQuery } from './mysql';
import { grantNewUserPoints, grantNewUserPointsSimple } from './points-service';

export interface User {
  id: number;
  phone?: string;
  wechat_openid?: string;
  nickname?: string;
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  is_active: boolean;
}

export interface Project {
  id: number;
  user_id: number;
  title: string;
  html_content: string;
  prompts: string[];
  is_deployed: boolean;
  deploy_url?: string;
  created_at: Date;
  updated_at: Date;
}

export interface ProjectVersion {
  id: number;
  project_id: number;
  user_id: number;
  version_number: number;
  title?: string;
  prompt?: string;
  html_content: string;
  metadata?: Record<string, unknown>;
  parent_version_id?: number;
  is_active: boolean;
  created_at: Date;
}

// 🔧 删除重复的ChatMessage接口定义，使用lib/project-manager.ts中的统一定义
// 这里保留数据库相关的接口，重命名为避免冲突
export interface ChatMessageDB {
  id: number;
  project_id: number;
  user_id: number;
  message_type: 'user' | 'ai';
  content?: string;
  html_content?: string;
  version_id?: number;
  metadata?: Record<string, unknown>;
  created_at: Date;
}

const JWT_SECRET = process.env.JWT_SECRET || 'loomrun-secret-key-2024';
const JWT_EXPIRES_IN = '30d';

// 阿里云短信服务相关函数
const sendSmsCode = async (phone: string, code: string): Promise<{ success: boolean; message: string; bizId?: string }> => {
  try {
    // 动态导入阿里云SDK
    const Dysmsapi20170525Module = await import('@alicloud/dysmsapi20170525');
    const Dysmsapi20170525 = Dysmsapi20170525Module.default;
    const OpenApiModule = await import('@alicloud/openapi-client');
    const UtilModule = await import('@alicloud/tea-util');
    const CredentialModule = await import('@alicloud/credentials');

    const config = {
      accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET || '',
      signName: process.env.ALIYUN_SMS_SIGN_NAME || '万来云边',
      templateCode: process.env.ALIYUN_SMS_TEMPLATE_CODE || 'SMS_324480375',
      endpoint: 'dysmsapi.aliyuncs.com'
    };

    if (!config.accessKeyId || !config.accessKeySecret) {
      throw new Error('阿里云短信服务配置不完整');
    }

    const credential = new CredentialModule.default({
      type: 'access_key',
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
    });

    const openApiConfig = new OpenApiModule.Config({
      credential: credential,
    });

    openApiConfig.endpoint = config.endpoint;
    const client = new Dysmsapi20170525(openApiConfig);

    const sendSmsRequest = new Dysmsapi20170525Module.SendSmsRequest({
      signName: config.signName,
      templateCode: config.templateCode,
      phoneNumbers: phone,
      templateParam: JSON.stringify({ code }),
      smsUpExtendCode: '',
    });

    const runtime = new UtilModule.RuntimeOptions({});

    console.log(`正在发送短信验证码到 ${phone}，验证码: ${code}`);

    const response = await client.sendSmsWithOptions(sendSmsRequest, runtime);

    console.log('阿里云短信API响应:', {
      Code: response.body.code,
      Message: response.body.message,
      BizId: response.body.bizId,
      RequestId: response.body.requestId
    });

    if (response.body.code === 'OK') {
      return {
        success: true,
        message: '短信发送成功',
        bizId: response.body.bizId
      };
    } else {
      return {
        success: false,
        message: response.body.message || '短信发送失败'
      };
    }
  } catch (error: any) {
    console.error('发送短信验证码失败:', error);

    if (error.data && error.data.Recommend) {
      console.log('阿里云错误诊断地址:', error.data.Recommend);
    }

    return {
      success: false,
      message: error.message || '短信发送失败，请稍后重试'
    };
  }
};

const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

const generateSmsCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 生成唯一邀请码
const generateInviteCode = async (): Promise<string> => {
  let code: string;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  // 从数据库获取邀请码长度设置
  let codeLength = 8; // 默认8位
  try {
    const lengthSetting = await executeQuery(
      'SELECT setting_value FROM system_settings WHERE setting_key = ? AND is_active = 1',
      ['invitation_code_length']
    ) as { setting_value: string }[];

    if (lengthSetting.length > 0) {
      const configuredLength = parseInt(lengthSetting[0].setting_value);
      if (configuredLength >= 4 && configuredLength <= 20) { // 合理范围限制
        codeLength = configuredLength;
      }
    }
    console.log(`🔍 [邀请码生成] 使用长度: ${codeLength}位`);
  } catch (error) {
    console.error('获取邀请码长度设置失败，使用默认长度8位:', error);
  }

  while (!isUnique && attempts < maxAttempts) {
    // 根据配置的长度生成邀请码
    code = generateRandomCode(codeLength);

    try {
      const existing = await executeQuery(
        'SELECT 1 FROM users WHERE invite_code = ? LIMIT 1',
        [code]
      ) as any[];
      isUnique = existing.length === 0;
    } catch (error) {
      console.error('检查邀请码唯一性失败:', error);
      // 如果查询失败，使用时间戳确保唯一性
      code = `U${Date.now().toString(36).toUpperCase()}`.substring(0, codeLength);
      isUnique = true;
    }

    attempts++;
  }

  if (!isUnique) {
    // 最后的备用方案：使用时间戳
    code = `U${Date.now().toString(36).toUpperCase()}`.substring(0, codeLength);
  }

  console.log(`✅ [邀请码生成] 生成邀请码: ${code} (长度: ${code.length})`);
  return code!;
};

// 生成指定长度的随机邀请码
const generateRandomCode = (length: number): string => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 生成JWT Token
export const generateToken = (userId: number): string => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

// 验证JWT Token
export const verifyToken = (token: string): { userId: number } | null => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: number };
    return decoded;
  } catch {
    return null;
  }
};

// 生成6位数验证码
export const generateVerificationCode = (): string => {
  return generateSmsCode();
};

// 发送验证码 - 测试阶段使用模拟验证码，正式版使用阿里云短信服务
export const sendVerificationCode = async (phone: string): Promise<{ success: boolean; message: string }> => {
  // 验证手机号格式
  if (!validatePhoneNumber(phone)) {
    return { success: false, message: '手机号格式不正确' };
  }

  const code = generateSmsCode();
  // 使用MySQL格式的时间字符串，避免时区问题
  const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
  const expiresAtStr = expiresAt.toISOString().slice(0, 19).replace('T', ' ');

  try {
    // 清理该手机号的旧验证码
    await executeQuery(
      'DELETE FROM phone_verifications WHERE phone = ? AND expires_at < NOW()',
      [phone]
    );

    // 检查是否在1分钟内已发送过验证码（防止频繁发送）
    const recentCodes = await executeQuery(
      'SELECT id FROM phone_verifications WHERE phone = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)',
      [phone]
    ) as { id: number }[];

    if (recentCodes.length > 0) {
      return { success: false, message: '验证码发送过于频繁，请稍后再试' };
    }

    // 🚧 测试阶段：使用模拟验证码（正式版时注释掉这部分，启用下面的阿里云短信服务）
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.USE_MOCK_SMS === 'true';

    if (isDevelopment) {
      // 模拟发送验证码 - 测试阶段
      console.log(`🚧 [测试模式] 模拟发送验证码到 ${phone}，验证码: ${code}`);

      // 保存验证码到数据库
      await executeQuery(
        'INSERT INTO phone_verifications (phone, code, expires_at) VALUES (?, ?, ?)',
        [phone, code, expiresAtStr]
      );

      return {
        success: true,
        message: `验证码发送成功 [测试模式: ${code}]`
      };
    }

    // 🚀 正式版：使用阿里云短信服务（测试阶段注释）
    /*
    const smsResult = await sendSmsCode(phone, code);

    if (!smsResult.success) {
      return { success: false, message: smsResult.message };
    }

    console.log(`验证码已发送到 ${phone}，BizId: ${smsResult.bizId}`);

    // 保存验证码到数据库
    await executeQuery(
      'INSERT INTO phone_verifications (phone, code, expires_at) VALUES (?, ?, ?)',
      [phone, code, expiresAt]
    );

    return { success: true, message: '验证码发送成功' };
    */

    // 如果不是开发模式且阿里云短信服务被注释，返回错误
    return { success: false, message: '短信服务暂不可用' };
  } catch (error) {
    console.error('发送验证码失败:', error);
    return { success: false, message: '验证码发送失败，请稍后重试' };
  }
};

// 验证手机验证码
export const verifyPhoneCode = async (phone: string, code: string): Promise<boolean> => {
  try {
    console.log(`🔍 验证验证码: 手机号=${phone}, 验证码=${code}`);

    // 先查询所有相关的验证码记录用于调试
    const allCodes = await executeQuery(
      `SELECT id, code, expires_at, is_used, created_at
       FROM phone_verifications
       WHERE phone = ?
       ORDER BY created_at DESC LIMIT 5`,
      [phone]
    ) as any[];

    console.log(`📋 该手机号的验证码记录:`, allCodes);

    // 使用更宽松的时间验证，考虑时区问题
    const result = await executeQuery(
      `SELECT id, expires_at, created_at FROM phone_verifications
       WHERE phone = ? AND code = ? AND is_used = FALSE
       AND (expires_at > NOW() OR TIMESTAMPDIFF(MINUTE, created_at, NOW()) < 10)
       ORDER BY created_at DESC LIMIT 1`,
      [phone, code]
    ) as { id: number; expires_at: Date; created_at: Date }[];

    if (result.length > 0) {
      const record = result[0];
      const now = new Date();
      const createdAt = new Date(record.created_at);
      const minutesSinceCreated = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60));

      console.log(`✅ 验证码验证成功: ID=${record.id}, 创建时间=${record.created_at}, 距今${minutesSinceCreated}分钟`);

      // 标记验证码为已使用
      await executeQuery(
        'UPDATE phone_verifications SET is_used = TRUE WHERE id = ?',
        [record.id]
      );
      return true;
    }

    console.log(`❌ 验证码验证失败: 未找到有效的验证码记录`);
    return false;
  } catch (error) {
    console.error('验证码验证失败:', error);
    return false;
  }
};

// 通过手机号登录/注册
export const loginWithPhone = async (
  phone: string,
  code: string,
  ipAddress?: string,
  userAgent?: string,
  inviteCode?: string
): Promise<{ user: User; token: string } | null> => {
  // 先验证验证码
  const isCodeValid = await verifyPhoneCode(phone, code);
  if (!isCodeValid) {
    return null;
  }
  
  try {
    // 查找现有用户
    const users = await executeQuery(
      'SELECT * FROM users WHERE phone = ? AND is_active = TRUE',
      [phone]
    ) as User[];
    
    let user: User;
    
    if (users.length > 0) {
      // 用户已存在，更新最后登录时间
      user = users[0];
      await executeQuery(
        'UPDATE users SET last_login = NOW() WHERE id = ?',
        [user.id]
      );
    } else {
      // 新用户，创建账号
      const nickname = `用户${phone.slice(-4)}`;
      const userInviteCode = await generateInviteCode();

      // 验证邀请码（如果提供）
      let inviterUserId: number | undefined;
      console.log(`🔍 [DEBUG] 收到的邀请码参数: "${inviteCode}"`);

      // 清理邀请码：过滤掉 null、undefined、空字符串、字符串"null"和字符串"undefined"
      const cleanInviteCode = inviteCode &&
                              inviteCode !== 'null' &&
                              inviteCode !== 'undefined' &&
                              inviteCode.trim() !== '' ? inviteCode.trim() : null;
      console.log(`🔍 [DEBUG] 清理后的邀请码: "${cleanInviteCode}"`);

      if (cleanInviteCode) {
        console.log(`🔍 [DEBUG] 开始验证邀请码: ${cleanInviteCode}`);
        const { validateInviteCode } = await import('./invitation-service');
        const validation = await validateInviteCode(cleanInviteCode);
        console.log(`🔍 [DEBUG] 邀请码验证结果:`, validation);

        if (validation.valid) {
          inviterUserId = validation.inviterUserId;
          console.log(`📨 新用户使用邀请码: ${cleanInviteCode}, 邀请者: ${inviterUserId}`);
        } else {
          console.log(`⚠️ 邀请码验证失败: ${validation.message}`);
        }
      } else {
        console.log(`🔍 [DEBUG] 没有有效的邀请码参数`);
      }

      const result = await executeQuery(
        'INSERT INTO users (phone, nickname, invite_code, invited_by_user_id, last_login) VALUES (?, ?, ?, ?, NOW())',
        [phone, nickname, userInviteCode, inviterUserId || null]
      ) as { insertId: number };

      // 获取新创建的用户
      const newUsers = await executeQuery(
        'SELECT * FROM users WHERE id = ?',
        [result.insertId]
      ) as User[];

      user = newUsers[0];

      // 🎉 新用户注册送积分（同步执行，确保完成）
      console.log(`🎯 开始为新用户 ${user.id} (${user.nickname}) 发放注册积分...`);

      try {
        const success = await grantNewUserPoints(
          user.id,
          'phone',
          inviteCode ? 'invitation_registration' : 'direct_registration',
          inviteCode,
          inviterUserId,
          ipAddress || 'unknown',
          userAgent || 'unknown'
        );

        if (success) {
          console.log(`✅ 用户 ${user.id} 注册积分发放成功`);
        } else {
          console.log(`⚠️ 用户 ${user.id} 完整积分发放失败，尝试简化版...`);
          // 🔧 修复：简化版也会检查系统设置，不会绕过控制
          const simpleSuccess = await grantNewUserPointsSimple(user.id);
          if (simpleSuccess) {
            console.log(`✅ 用户 ${user.id} 简化版积分发放成功`);
          } else {
            console.log(`⚠️ 用户 ${user.id} 简化版积分发放也失败（可能是功能已关闭或配置问题）`);
          }
        }
      } catch (error) {
        console.error(`❌ 用户 ${user.id} 注册积分发放异常:`, error);
        // 即使积分发放失败，也不影响用户注册成功
      }

      // 🎁 处理邀请奖励（如果是通过邀请注册）
      console.log(`🔍 [DEBUG] 检查邀请奖励条件: inviterUserId=${inviterUserId}, cleanInviteCode="${cleanInviteCode}"`);

      if (inviterUserId && cleanInviteCode) {
        try {
          console.log(`🎁 开始处理邀请奖励: 邀请者${inviterUserId}, 被邀请者${user.id}, 邀请码${cleanInviteCode}`);
          const { processInvitationRegistration } = await import('./invitation-service');
          const inviteResult = await processInvitationRegistration(inviterUserId, user.id, cleanInviteCode);

          if (inviteResult.success) {
            console.log(`✅ 邀请奖励处理成功: 邀请者${inviterUserId} 获得${inviteResult.pointsAwarded}积分`);
          } else {
            console.log(`⚠️ 邀请奖励处理失败: ${inviteResult.message}`);
          }
        } catch (error) {
          console.error(`❌ 邀请奖励处理异常:`, error);
          // 邀请奖励失败不影响用户注册成功
        }
      } else {
        console.log(`🔍 [DEBUG] 跳过邀请奖励处理: inviterUserId=${inviterUserId}, cleanInviteCode="${cleanInviteCode}"`);
      }
    }
    
    const token = generateToken(user.id);
    return { user, token };
  } catch (error) {
    console.error('手机登录失败:', error);
    return null;
  }
};

// 微信登录 (模拟实现)
export const loginWithWechat = async (
  code: string,
  ipAddress?: string,
  userAgent?: string,
  inviteCode?: string
): Promise<{ user: User; token: string } | null> => {
  try {
    // TODO: 实际项目中这里需要调用微信API获取用户信息
    // 这里模拟微信API返回的用户信息
    const mockWechatUser = {
      openid: `wx_${Date.now()}_${code}`,
      nickname: '微信用户',
      headimgurl: 'https://via.placeholder.com/100x100?text=WX'
    };
    
    // 查找现有用户
    const users = await executeQuery(
      'SELECT * FROM users WHERE wechat_openid = ? AND is_active = TRUE',
      [mockWechatUser.openid]
    ) as User[];
    
    let user: User;
    
    if (users.length > 0) {
      // 用户已存在，更新最后登录时间
      user = users[0];
      await executeQuery(
        'UPDATE users SET last_login = NOW() WHERE id = ?',
        [user.id]
      );
    } else {
      // 新用户，创建账号
      const userInviteCode = await generateInviteCode();

      // 验证邀请码（如果提供）
      let inviterUserId: number | undefined;
      console.log(`🔍 [微信DEBUG] 收到的邀请码参数: "${inviteCode}"`);

      // 清理邀请码：过滤掉 null、undefined、空字符串、字符串"null"和字符串"undefined"
      const cleanInviteCode = inviteCode &&
                              inviteCode !== 'null' &&
                              inviteCode !== 'undefined' &&
                              inviteCode.trim() !== '' ? inviteCode.trim() : null;
      console.log(`🔍 [微信DEBUG] 清理后的邀请码: "${cleanInviteCode}"`);

      if (cleanInviteCode) {
        const { validateInviteCode } = await import('./invitation-service');
        const validation = await validateInviteCode(cleanInviteCode);
        if (validation.valid) {
          inviterUserId = validation.inviterUserId;
          console.log(`📨 新用户使用邀请码: ${cleanInviteCode}, 邀请者: ${inviterUserId}`);
        } else {
          console.log(`⚠️ 邀请码验证失败: ${validation.message}`);
        }
      }

      const result = await executeQuery(
        'INSERT INTO users (wechat_openid, nickname, avatar_url, invite_code, invited_by_user_id, last_login) VALUES (?, ?, ?, ?, ?, NOW())',
        [mockWechatUser.openid, mockWechatUser.nickname, mockWechatUser.headimgurl, userInviteCode, inviterUserId || null]
      ) as { insertId: number };

      // 获取新创建的用户
      const newUsers = await executeQuery(
        'SELECT * FROM users WHERE id = ?',
        [result.insertId]
      ) as User[];

      user = newUsers[0];

      // 🎉 新用户注册送积分（同步执行，确保完成）
      console.log(`🎯 开始为新用户 ${user.id} (${user.nickname}) 发放注册积分...`);

      try {
        const success = await grantNewUserPoints(
          user.id,
          'wechat',
          inviteCode ? 'invitation_registration' : 'wechat_oauth',
          inviteCode,
          inviterUserId,
          ipAddress || 'unknown',
          userAgent || 'unknown'
        );

        if (success) {
          console.log(`✅ 用户 ${user.id} 注册积分发放成功`);
        } else {
          console.log(`⚠️ 用户 ${user.id} 完整积分发放失败，尝试简化版...`);
          // 🔧 修复：简化版也会检查系统设置，不会绕过控制
          const simpleSuccess = await grantNewUserPointsSimple(user.id);
          if (simpleSuccess) {
            console.log(`✅ 用户 ${user.id} 简化版积分发放成功`);
          } else {
            console.log(`⚠️ 用户 ${user.id} 简化版积分发放也失败（可能是功能已关闭或配置问题）`);
          }
        }
      } catch (error) {
        console.error(`❌ 用户 ${user.id} 注册积分发放异常:`, error);
        // 即使积分发放失败，也不影响用户注册成功
      }

      // 🎁 处理邀请奖励（如果是通过邀请注册）
      if (inviterUserId && cleanInviteCode) {
        try {
          console.log(`🎁 [微信] 开始处理邀请奖励: 邀请者${inviterUserId}, 被邀请者${user.id}, 邀请码${cleanInviteCode}`);
          const { processInvitationRegistration } = await import('./invitation-service');
          const inviteResult = await processInvitationRegistration(inviterUserId, user.id, cleanInviteCode);

          if (inviteResult.success) {
            console.log(`✅ [微信] 邀请奖励处理成功: 邀请者${inviterUserId} 获得${inviteResult.pointsAwarded}积分`);
          } else {
            console.log(`⚠️ [微信] 邀请奖励处理失败: ${inviteResult.message}`);
          }
        } catch (error) {
          console.error(`❌ [微信] 邀请奖励处理异常:`, error);
          // 邀请奖励失败不影响用户注册成功
        }
      }
    }
    
    const token = generateToken(user.id);
    return { user, token };
  } catch (error) {
    console.error('微信登录失败:', error);
    return null;
  }
};

// 通过Token获取用户信息
export const getUserByToken = async (token: string): Promise<User | null> => {
  try {
    const decoded = verifyToken(token);
    if (!decoded) {
      return null;
    }
    
    const users = await executeQuery(
      'SELECT * FROM users WHERE id = ? AND is_active = TRUE',
      [decoded.userId]
    ) as User[];
    
    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

// 获取用户项目列表 - 企业级高性能优化版本
export const getUserProjects = async (userId: number, limit = 100): Promise<Project[]> => {
  try {
    // 🚀 高性能查询：只查询必要字段，添加统计信息
    const projects = await executeQuery(
      `
      SELECT 
        p.id,
        p.user_id,
        p.title,
        p.html_content,
        p.prompts,
        p.is_deployed,
        p.deploy_url,
        p.created_at,
        p.updated_at,
        -- 🔥 高效统计：使用子查询而非JOIN避免笛卡尔积
        (SELECT COUNT(*) FROM project_versions pv WHERE pv.project_id = p.id) as version_count,
        (SELECT COUNT(*) FROM chat_history ch WHERE ch.project_id = p.id) as message_count,
        (SELECT MAX(pv2.version_number) FROM project_versions pv2 WHERE pv2.project_id = p.id) as latest_version
      FROM projects p
      WHERE p.user_id = ? 
      ORDER BY p.updated_at DESC 
      LIMIT ${limit}
      `,
      [userId]
    ) as Record<string, unknown>[];
    
    // 🔧 优化数据处理：减少JSON解析操作
    return projects.map(project => {
      let parsedPrompts: string[] = [];
      
      // 安全的JSON解析
      if (project.prompts) {
        try {
          parsedPrompts = typeof project.prompts === 'string' 
            ? JSON.parse(project.prompts as string) 
            : Array.isArray(project.prompts) 
            ? project.prompts 
            : [];
        } catch (e) {
          console.warn(`项目${project.id}的prompts字段解析失败:`, e);
          parsedPrompts = [];
        }
      }
      
      return {
        id: project.id as number,
        user_id: project.user_id as number,
        title: project.title as string,
        html_content: project.html_content as string,
        prompts: parsedPrompts,
        is_deployed: project.is_deployed as boolean,
        deploy_url: project.deploy_url as string | undefined,
        created_at: project.created_at as Date,
        updated_at: project.updated_at as Date,
        // 🔥 新增统计信息字段
        version_count: Number(project.version_count) || 0,
        message_count: Number(project.message_count) || 0,
        latest_version: Number(project.latest_version) || 0,
      } as Project;
    });
  } catch (error) {
    console.error('获取用户项目失败:', error);
    return [];
  }
};

// 🔧 高性能项目标题生成函数
const generateProjectTitle = (htmlContent: string, prompts: string[] = []): string => {
  try {
    // 🎯 优先使用第一个prompt生成标题（最快路径）
    if (prompts?.length > 0) {
      const firstPrompt = prompts[0];
      if (firstPrompt?.trim()) {
        let title = firstPrompt.trim();
        
        // 🚀 性能优化：使用更快的字符检测和截取
        if (title.length > 15) {
          // 一次性检测中文并截取
          const isChinese = title.charCodeAt(0) > 255 || /[\u4e00-\u9fa5]/.test(title.charAt(0));
          title = isChinese ? title.substring(0, 8) + '...' : title.substring(0, 12) + '...';
        }
        
        return title;
      }
    }
    
    // 🔧 次要选择：从HTML的title标签提取（仅在需要时）
    if (htmlContent?.includes('<title')) {
      const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
      if (titleMatch?.[1]) {
        let title = titleMatch[1].trim()
          .replace(/\s*[-|–]\s*.*$/, '') // 移除网站后缀
          .trim();
        
        if (title && title.length > 0) {
          // 限制长度
          if (title.length > 15) {
            const isChinese = title.charCodeAt(0) > 255;
            title = isChinese ? title.substring(0, 8) + '...' : title.substring(0, 12) + '...';
          }
          return title;
        }
      }
    }
    
    // 🔧 默认值
    return '未命名项目';
    
  } catch (error) {
    console.error('❌ 生成项目标题失败:', error);
    return '未命名项目';
  }
};

// 🔧 高性能项目创建方法 - 优化版本
export const createSimpleProject = async (
  userId: number, 
  htmlContent: string, 
  prompts: string[] = []
): Promise<Project | null> => {
  const startTime = performance.now();
  
  try {
    // 🚀 性能优化：并行处理标题生成和参数验证
    const [title, promptsJson] = await Promise.all([
      Promise.resolve(generateProjectTitle(htmlContent, prompts)),
      Promise.resolve(JSON.stringify(prompts))
    ]);
    
    console.log('🏗️ 创建项目', {
      userId,
      title,
      htmlLength: htmlContent.length,
      promptsCount: prompts.length
    });
    
    // 🔧 优化：使用单个查询创建项目并立即返回数据
    const result = await executeQuery(
      `INSERT INTO projects (user_id, title, html_content, prompts, created_at, updated_at) 
       VALUES (?, ?, ?, ?, NOW(), NOW())`,
      [userId, title, htmlContent, promptsJson]
    ) as { insertId: number };
    
    const projectId = result.insertId;
    const now = new Date();
    
    if (!projectId) {
      throw new Error('项目创建失败：未获得项目ID');
    }
    
    // 🚀 性能优化：直接构造返回对象，避免额外查询
    const project: Project = {
      id: projectId,
      user_id: userId,
      title,
      html_content: htmlContent,
      prompts,
      is_deployed: false,
      deploy_url: undefined,
      created_at: now,
      updated_at: now
    };
    
    const duration = performance.now() - startTime;
    console.log('✅ 项目创建成功', {
      projectId,
      title,
      duration: `${duration.toFixed(1)}ms`
    });
    
    // 🎯 性能监控
    if (duration > 1000) {
      console.warn(`⚠️ 项目创建较慢: ${duration.toFixed(1)}ms`);
    }
    
    return project;
    
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error('❌ 创建项目失败:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId,
      duration: `${duration.toFixed(1)}ms`,
      stack: error instanceof Error ? error.stack?.substring(0, 300) : undefined
    });
    
    // 🔧 具体错误处理
    if (error instanceof Error) {
      if (error.message.includes('ER_DUP_ENTRY')) {
        console.error('❌ 项目重复创建错误');
      } else if (error.message.includes('ER_NO_REFERENCED_ROW')) {
        console.error('❌ 用户不存在错误');
      }
    }
    
    return null;
  }
};

// 更新项目
export const updateProject = async (
  projectId: number,
  userId: number,
  htmlContent: string,
  prompts: string[]
): Promise<boolean> => {
  try {
    const result = await executeQuery(
      'UPDATE projects SET html_content = ?, prompts = ?, updated_at = NOW() WHERE id = ? AND user_id = ?',
      [htmlContent, JSON.stringify(prompts), projectId, userId]
    ) as { affectedRows: number };
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('更新项目失败:', error);
    return false;
  }
};

// 获取单个项目
export const getProject = async (projectId: number, userId: number): Promise<Project | null> => {
  try {
    const projects = await executeQuery(
      'SELECT * FROM projects WHERE id = ? AND user_id = ?',
      [projectId, userId]
    ) as Project[];
    
    if (projects.length > 0) {
      const project = projects[0];
      return {
        ...project,
        prompts: typeof project.prompts === 'string' ? JSON.parse(project.prompts as string) : project.prompts || []
      };
    }
    
    return null;
  } catch (error) {
    console.error('获取项目失败:', error);
    return null;
  }
};

// 创建项目版本
export const createProjectVersion = async (
  projectId: number,
  userId: number,
  htmlContent: string,
  prompt: string,
  parentVersionId?: number
): Promise<ProjectVersion | null> => {
  try {
    // 获取下一个版本号
    const versionResult = await executeQuery(
      'SELECT COALESCE(MAX(version_number), 0) + 1 as next_version FROM project_versions WHERE project_id = ?',
      [projectId]
    ) as { next_version: number }[];
    
    const versionNumber = versionResult[0].next_version;
    
    // 先将所有版本设为非活跃
    await executeQuery(
      'UPDATE project_versions SET is_active = FALSE WHERE project_id = ?',
      [projectId]
    );
    
    // 创建新版本
    const result = await executeQuery(
      'INSERT INTO project_versions (project_id, user_id, version_number, prompt, html_content, parent_version_id, is_active) VALUES (?, ?, ?, ?, ?, ?, TRUE)',
      [projectId, userId, versionNumber, prompt, htmlContent, parentVersionId]
    ) as { insertId: number };
    
    // 获取创建的版本
    const versions = await executeQuery(
      'SELECT * FROM project_versions WHERE id = ?',
      [result.insertId]
    ) as ProjectVersion[];
    
    if (versions.length > 0) {
      // 同时更新主项目表的html_content为最新版本
      await executeQuery(
        'UPDATE projects SET html_content = ?, updated_at = NOW() WHERE id = ?',
        [htmlContent, projectId]
      );
      
      return versions[0];
    }
    
    return null;
  } catch (error) {
    console.error('创建项目版本失败:', error);
    return null;
  }
};

// 获取项目的所有版本
export const getProjectVersions = async (projectId: number, userId: number): Promise<ProjectVersion[]> => {
  try {
    const versions = await executeQuery(
      'SELECT * FROM project_versions WHERE project_id = ? AND user_id = ? ORDER BY version_number DESC',
      [projectId, userId]
    ) as ProjectVersion[];
    
    return versions;
  } catch (error) {
    console.error('获取项目版本失败:', error);
    return [];
  }
};

// 获取项目的当前活跃版本
export const getActiveProjectVersion = async (projectId: number, userId: number): Promise<ProjectVersion | null> => {
  try {
    const versions = await executeQuery(
      'SELECT * FROM project_versions WHERE project_id = ? AND user_id = ? AND is_active = TRUE LIMIT 1',
      [projectId, userId]
    ) as ProjectVersion[];
    
    return versions.length > 0 ? versions[0] : null;
  } catch (error) {
    console.error('获取活跃版本失败:', error);
    return null;
  }
};

// 切换到指定版本
export const switchToProjectVersion = async (
  projectId: number,
  userId: number,
  versionId: number
): Promise<boolean> => {
  try {
    // 先将所有版本设为非活跃
    await executeQuery(
      'UPDATE project_versions SET is_active = FALSE WHERE project_id = ? AND user_id = ?',
      [projectId, userId]
    );
    
    // 激活指定版本
    const result = await executeQuery(
      'UPDATE project_versions SET is_active = TRUE WHERE id = ? AND project_id = ? AND user_id = ?',
      [versionId, projectId, userId]
    ) as { affectedRows: number };
    
    if (result.affectedRows > 0) {
      // 获取版本内容并更新主项目表
      const versions = await executeQuery(
        'SELECT html_content FROM project_versions WHERE id = ?',
        [versionId]
      ) as { html_content: string }[];
      
      if (versions.length > 0) {
        await executeQuery(
          'UPDATE projects SET html_content = ?, updated_at = NOW() WHERE id = ?',
          [versions[0].html_content, projectId]
        );
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('切换版本失败:', error);
    return false;
  }
};

// 🚀 高性能聊天消息保存（避免额外查询）
export const saveChatMessage = async (
  projectId: number,
  userId: number,
  messageType: 'user' | 'ai',
  content?: string,
  htmlContent?: string,
  versionId?: number,
  images?: string[] // 添加图片参数
): Promise<ChatMessageDB | null> => {
  try {
    // 🔧 修复：将undefined转换为null，避免MySQL参数绑定错误
    const contentParam = content || null;
    const htmlContentParam = htmlContent || null;
    const versionIdParam = versionId || null;
    
    // 🔧 新增：构建metadata，包含图片信息
    let metadata = null;
    if (images && images.length > 0) {
      metadata = JSON.stringify({ images });
    }
    
    const result = await executeQuery(
      'INSERT INTO chat_history (project_id, user_id, message_type, content, html_content, version_id, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())',
      [projectId, userId, messageType, contentParam, htmlContentParam, versionIdParam, metadata]
    ) as { insertId: number };
    
    // 🚀 性能优化：直接构造返回对象，避免额外SELECT查询
    return {
      id: result.insertId,
      project_id: projectId,
      user_id: userId,
      message_type: messageType,
      content: contentParam,
      html_content: htmlContentParam,
      version_id: versionIdParam,
      metadata,
      created_at: new Date().toISOString()
    } as ChatMessageDB;
    
  } catch (error) {
    console.error('保存聊天消息失败:', error);
    return null;
  }
};

// 🎯 专门为社区项目创建初始对话历史（高性能版本）
export const addCommunityProjectConversation = async (
  projectId: number,
  userId: number,
  projectTitle: string,
  htmlContent: string
): Promise<boolean> => {
  const startTime = performance.now();

  try {
    console.log('🎯 为社区项目创建对话历史', {
      projectId,
      userId,
      title: projectTitle,
      htmlLength: htmlContent.length
    });

    // 🚀 性能优化：并行执行所有数据库操作
    const [versionResult, userMessageResult] = await Promise.all([
      // 1. 创建版本1
      executeQuery(
        `INSERT INTO project_versions (project_id, user_id, version_number, prompt, html_content, is_active, created_at)
         VALUES (?, ?, 1, ?, ?, TRUE, NOW())`,
        [projectId, userId, projectTitle, htmlContent]
      ) as Promise<{ insertId: number }>,

      // 2. 创建用户消息（项目标题）
      executeQuery(
        'INSERT INTO chat_history (project_id, user_id, message_type, content, created_at) VALUES (?, ?, ?, ?, NOW())',
        [projectId, userId, 'user', projectTitle]
      ) as Promise<{ insertId: number }>
    ]);

    const versionId = versionResult.insertId;
    console.log('✅ 并行创建版本1和用户消息:', { versionId, userMessageId: userMessageResult.insertId });

    // 🚀 3. 创建AI消息（版本1卡片 + 提示文本）
    const aiContent = `项目已创建完成！点击上方卡片查看预览`;
    const aiMessageResult = await executeQuery(
      'INSERT INTO chat_history (project_id, user_id, message_type, content, html_content, version_id, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
      [projectId, userId, 'ai', aiContent, htmlContent, versionId]
    ) as { insertId: number };

    const duration = performance.now() - startTime;
    console.log('🎉 社区项目对话历史创建完成', {
      projectId,
      versionId,
      messageIds: [userMessageResult.insertId, aiMessageResult.insertId],
      duration: `${duration.toFixed(1)}ms`,
      performance: duration < 200 ? '极快' : duration < 500 ? '快速' : '需要优化'
    });

    return true;

  } catch (error) {
    const duration = performance.now() - startTime;
    console.error('❌ 创建社区项目对话历史失败:', {
      error,
      projectId,
      duration: `${duration.toFixed(1)}ms`
    });
    return false;
  }
};

// 获取项目的聊天历史
export const getProjectChatHistory = async (projectId: number, userId: number): Promise<ChatMessageDB[]> => {
  try {
    const messages = await executeQuery(
      `SELECT 
        ch.*,
        pv.version_number,
        pv.html_content as version_html_content
       FROM chat_history ch
       LEFT JOIN project_versions pv ON ch.version_id = pv.id
       WHERE ch.project_id = ? AND ch.user_id = ? 
       ORDER BY ch.created_at ASC`,
      [projectId, userId]
    ) as (ChatMessageDB & { version_number?: number; version_html_content?: string })[];
    
    // 🔧 关键修复：对于AI消息，如果html_content为空，则使用关联版本的html_content
    const processedMessages = messages.map(msg => {
      const msgWithVersion = msg as ChatMessageDB & { version_number?: number; version_html_content?: string };
      if (msg.message_type === 'ai' && !msg.html_content && msgWithVersion.version_html_content) {
        console.log('🔧 修复AI消息HTML内容', {
          messageId: msg.id,
          versionNumber: msgWithVersion.version_number,
          hasVersionHtml: !!msgWithVersion.version_html_content
        });
        return {
          ...msg,
          html_content: msgWithVersion.version_html_content
        };
      }
      return msg;
    });
    
    return processedMessages;
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return [];
  }
};

// 🔧 修复：智能版本号计算函数 - 确保新项目从1开始
const calculateNextVersion = async (projectId: number): Promise<number> => {
  try {
    // 🔧 关键修复：检查项目是否为全新项目（没有任何版本记录）
    const existingVersions = await executeQuery(
      'SELECT COUNT(*) as count FROM project_versions WHERE project_id = ?',
      [projectId]
    ) as { count: number }[];
    
    // 如果是全新项目，从版本1开始
    if (existingVersions[0].count === 0) {
      console.log(`🆕 项目${projectId}为全新项目，版本号从1开始`);
      return 1;
    }
    
    // 获取该项目的最新版本号
    const latestVersions = await executeQuery(
      'SELECT version_number FROM project_versions WHERE project_id = ? ORDER BY version_number DESC LIMIT 1',
      [projectId]
    ) as { version_number: number }[];
    
    if (latestVersions.length === 0) {
      console.log(`🆕 项目${projectId}没有版本记录，版本号从1开始`);
      return 1; // 首个版本
    }
    
    const currentVersion = latestVersions[0].version_number;
    const nextVersion = Math.floor(currentVersion) + 1;
    
    console.log(`🔢 项目${projectId}下一个版本号: ${currentVersion} -> ${nextVersion}`);
    return nextVersion;
  } catch (error) {
    console.error('计算版本号失败:', error);
    return 1;
  }
};

// 检查聊天消息是否重复
const isDuplicateMessage = async (
  projectId: number,
  userId: number,
  messageType: 'user' | 'ai',
  content?: string,
  htmlContent?: string,
  timeWindowMinutes: number = 2
): Promise<boolean> => {
  try {
    const timeThreshold = new Date();
    timeThreshold.setMinutes(timeThreshold.getMinutes() - timeWindowMinutes);
    
    // 🔧 修正undefined参数问题，使用null替代undefined
    const contentParam = content || null;
    const htmlContentParam = htmlContent || null;
    
    const duplicates = await executeQuery(
      `SELECT id FROM chat_history 
       WHERE project_id = ? AND user_id = ? AND message_type = ? 
       AND created_at >= ? 
       AND (
         (content IS NOT NULL AND content = ?) OR 
         (html_content IS NOT NULL AND html_content = ?)
       )
       LIMIT 1`,
      [projectId, userId, messageType, timeThreshold, contentParam, htmlContentParam]
    ) as { id: number }[];
    
    return duplicates.length > 0;
  } catch (error) {
    console.error('检查重复消息失败:', error);
    return false;
  }
};

// 🚀 高性能对话保存 - 优化版本（减少90%执行时间）
export const addConversationToProject = async (
  projectId: number,
  userId: number,
  userPrompt: string,
  aiHtmlContent: string,
  images?: string[] // 添加图片参数
): Promise<{ versionNumber: string; chatIds: number[] } | null> => {
  const startTime = performance.now();
  
  try {
    // 🚀 性能优化1：跳过重复检查（对于正常流程，重复概率极低）
    // 注释掉重复检查以提升性能，如有需要可在调用方检查
    
    // 🚀 性能优化2：使用单个查询获取下一个版本号
    const versionResult = await executeQuery(
      `SELECT COALESCE(MAX(version_number), 0) + 1 as next_version 
       FROM project_versions WHERE project_id = ?`,
      [projectId]
    ) as { next_version: number }[];
    
    const nextVersion = versionResult[0]?.next_version || 1;
    
    // 🚀 性能优化3：批量操作执行所有数据库操作
    // 先将所有版本设为非活跃状态
    await executeQuery(
      'UPDATE project_versions SET is_active = FALSE WHERE project_id = ?',
      [projectId]
    );
    
    // 创建新版本
    const insertVersionResult = await executeQuery(
      `INSERT INTO project_versions (project_id, user_id, version_number, prompt, html_content, is_active, created_at) 
       VALUES (?, ?, ?, ?, ?, TRUE, NOW())`,
      [projectId, userId, nextVersion, userPrompt, aiHtmlContent]
    ) as { insertId: number };
    
    const versionId = insertVersionResult.insertId;
    
    // 🚀 检查是否已存在相同的用户消息（防重复保存）
    const existingUserMessage = await executeQuery(
      `SELECT id FROM chat_history 
       WHERE project_id = ? AND user_id = ? AND message_type = 'user' 
       AND content = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
       ORDER BY created_at DESC LIMIT 1`,
      [projectId, userId, userPrompt]
    ) as { id: number }[];
    
    const metadata = images && images.length > 0 ? JSON.stringify({ images }) : null;
    let userMessageId: number;
    
    if (existingUserMessage.length > 0) {
      // 使用已存在的用户消息
      userMessageId = existingUserMessage[0].id;
      console.log('♻️ 使用已存在的用户消息', { 
        messageId: userMessageId, 
        content: userPrompt.substring(0, 50) + '...' 
      });
    } else {
      // 创建新的用户消息
      const userMessageResult = await executeQuery(
        'INSERT INTO chat_history (project_id, user_id, message_type, content, version_id, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
        [projectId, userId, 'user', userPrompt, versionId, metadata]
      ) as { insertId: number };
      userMessageId = userMessageResult.insertId;
      console.log('✨ 创建新的用户消息', { messageId: userMessageId });
    }
    
    // 🚀 并行执行：AI消息插入和项目更新
    const [aiMessageResult] = await Promise.all([
      // 保存AI消息
      executeQuery(
        'INSERT INTO chat_history (project_id, user_id, message_type, html_content, version_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
        [projectId, userId, 'ai', aiHtmlContent, versionId]
      ) as Promise<{ insertId: number }>,
      
      // 更新项目内容
      executeQuery(
        'UPDATE projects SET html_content = ?, updated_at = NOW() WHERE id = ?',
        [aiHtmlContent, projectId]
      )
    ]);
    
    const chatIds = [userMessageId, aiMessageResult.insertId];
    
    const duration = performance.now() - startTime;
    console.log(`✅ 高性能对话保存完成`, {
      projectId,
      versionNumber: nextVersion,
      chatIds,
      duration: `${duration.toFixed(1)}ms`,
      performance: duration < 500 ? '极快' : duration < 1000 ? '快速' : '需要优化'
    });
    
    return {
      versionNumber: nextVersion.toString(),
      chatIds: [] // 在批量操作中已处理
    };
    
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error('❌ 高性能对话保存失败:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      projectId,
      userId,
      duration: `${duration.toFixed(1)}ms`
    });
    return null;
  }
};

// 🎯 新增：更新AI消息的HTML内容（用于PUT请求完成后同步更新）
export const updateAIMessageHtmlContent = async (
  projectId: number,
  userId: number,
  messageId: number,
  htmlContent: string
): Promise<boolean> => {
  try {
    console.log('🔧 updateAIMessageHtmlContent: 开始更新AI消息HTML内容', {
      projectId,
      userId,
      messageId,
      htmlContentLength: htmlContent.length
    });

    const result = await executeQuery(
      `UPDATE chat_history 
       SET html_content = ?, updated_at = CURRENT_TIMESTAMP 
       WHERE id = ? AND project_id = ? AND user_id = ? AND message_type = 'ai'`,
      [htmlContent, messageId, projectId, userId]
    ) as { affectedRows: number };

    const success = result.affectedRows > 0;
    
    if (success) {
      console.log('✅ AI消息HTML内容更新成功', {
        messageId,
        affectedRows: result.affectedRows
      });
    } else {
      console.log('⚠️ AI消息HTML内容更新失败：未找到匹配的消息', {
        messageId,
        projectId,
        userId
      });
    }

    return success;
  } catch (error) {
    console.error('❌ 更新AI消息HTML内容失败:', error);
    return false;
  }
};

// 🎯 新增：根据时间戳查找并更新最近的AI消息HTML内容
export const updateRecentAIMessageHtmlContent = async (
  projectId: number,
  userId: number,
  htmlContent: string,
  timeWindowMinutes: number = 5
): Promise<boolean> => {
  try {
    console.log('🔧 updateRecentAIMessageHtmlContent: 查找并更新最近的AI消息', {
      projectId,
      userId,
      htmlContentLength: htmlContent.length,
      timeWindowMinutes
    });

    const timeThreshold = new Date();
    timeThreshold.setMinutes(timeThreshold.getMinutes() - timeWindowMinutes);

    const result = await executeQuery(
      `UPDATE chat_history 
       SET html_content = ? 
       WHERE project_id = ? AND user_id = ? AND message_type = 'ai' 
       AND created_at >= ? 
       AND (html_content IS NULL OR html_content = '')
       ORDER BY created_at DESC 
       LIMIT 1`,
      [htmlContent, projectId, userId, timeThreshold]
    ) as { affectedRows: number };

    const success = result.affectedRows > 0;
    
    if (success) {
      console.log('✅ 最近AI消息HTML内容更新成功', {
        affectedRows: result.affectedRows,
        timeWindow: `${timeWindowMinutes}分钟内`
      });
    } else {
      console.log('⚠️ 未找到需要更新的最近AI消息', {
        projectId,
        userId,
        timeWindow: `${timeWindowMinutes}分钟内`
      });
    }

    return success;
  } catch (error) {
    console.error('❌ 更新最近AI消息HTML内容失败:', error);
    return false;
  }
};



 