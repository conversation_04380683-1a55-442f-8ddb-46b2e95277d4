import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    // 获取活动积分配置
    const configResult = await executeQuery(
      `SELECT setting_value FROM system_settings 
       WHERE setting_key = 'activity_points_config' AND is_active = 1`,
      []
    ) as { setting_value: string }[];

    let activityConfig = {
      invitation: { validity_days: 15, description: "邀请活动积分" },
      registration: { validity_days: 30, description: "注册奖励积分" },
      special_event: { validity_days: 7, description: "特殊活动积分" }
    };

    if (configResult.length > 0) {
      try {
        activityConfig = JSON.parse(configResult[0].setting_value);
      } catch (error) {
        console.error('解析活动积分配置失败:', error);
        // 使用默认配置
      }
    }

    return NextResponse.json({
      success: true,
      config: activityConfig
    });
  } catch (error) {
    console.error('获取活动积分配置失败:', error);
    return NextResponse.json(
      { 
        success: false,
        error: '获取活动积分配置失败',
        config: {
          invitation: { validity_days: 15, description: "邀请活动积分" },
          registration: { validity_days: 30, description: "注册奖励积分" },
          special_event: { validity_days: 7, description: "特殊活动积分" }
        }
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { config } = body;

    if (!config || typeof config !== 'object') {
      return NextResponse.json(
        { success: false, error: '无效的配置数据' },
        { status: 400 }
      );
    }

    // 更新活动积分配置
    await executeQuery(
      `INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category, is_active)
       VALUES ('activity_points_config', ?, 'json', '不同活动积分有效期配置', 'points', 1)
       ON DUPLICATE KEY UPDATE 
       setting_value = VALUES(setting_value),
       updated_at = NOW()`,
      [JSON.stringify(config)]
    );

    return NextResponse.json({
      success: true,
      message: '活动积分配置更新成功'
    });
  } catch (error) {
    console.error('更新活动积分配置失败:', error);
    return NextResponse.json(
      { success: false, error: '更新活动积分配置失败' },
      { status: 500 }
    );
  }
}
