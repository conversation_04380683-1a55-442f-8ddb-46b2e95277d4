"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Trash2, Edit3 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ElementToolbarProps {
  selectedElement: HTMLElement | null;
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  onDeleteElement: (element: HTMLElement) => void;
  onEditText: (element: HTMLElement) => void;
}

export function ElementToolbar({ 
  selectedElement, 
  iframeRef, 
  onDeleteElement, 
  onEditText 
}: ElementToolbarProps) {
  const [toolbarPosition, setToolbarPosition] = useState<{ x: number; y: number } | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const toolbarRef = useRef<HTMLDivElement>(null);

  // 🎯 计算工具栏位置
  const calculateToolbarPosition = useCallback(() => {
    if (!selectedElement || !iframeRef.current) {
      setIsVisible(false);
      return;
    }

    const iframe = iframeRef.current;
    const iframeRect = iframe.getBoundingClientRect();
    const elementRect = selectedElement.getBoundingClientRect();

    // 🎯 计算相对于页面的位置
    const x = iframeRect.left + elementRect.left + (elementRect.width / 2);
    const y = iframeRect.top + elementRect.top - 50; // 在元素上方50px

    // 🎯 确保工具栏不超出视窗边界
    const toolbarWidth = 120; // 预估工具栏宽度
    const adjustedX = Math.max(10, Math.min(x - toolbarWidth / 2, window.innerWidth - toolbarWidth - 10));
    const adjustedY = Math.max(10, y);

    setToolbarPosition({ x: adjustedX, y: adjustedY });
    setIsVisible(true);
  }, [selectedElement, iframeRef]);

  // 🎯 监听选中元素变化
  useEffect(() => {
    if (selectedElement) {
      calculateToolbarPosition();
    } else {
      setIsVisible(false);
    }
  }, [selectedElement, calculateToolbarPosition]);

  // 🎯 监听窗口大小变化和滚动
  useEffect(() => {
    const handleResize = () => {
      if (selectedElement) {
        calculateToolbarPosition();
      }
    };

    const handleScroll = () => {
      if (selectedElement) {
        calculateToolbarPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [selectedElement, calculateToolbarPosition]);

  // 🎯 处理删除元素
  const handleDelete = useCallback(() => {
    if (selectedElement) {
      onDeleteElement(selectedElement);
      setIsVisible(false);
    }
  }, [selectedElement, onDeleteElement]);

  // 🎯 处理编辑文本
  const handleEditText = useCallback(() => {
    if (selectedElement) {
      onEditText(selectedElement);
    }
  }, [selectedElement, onEditText]);

  // 🎯 检查元素是否包含文本
  const hasText = selectedElement?.textContent?.trim().length > 0;

  if (!isVisible || !toolbarPosition) {
    return null;
  }

  return (
    <div
      ref={toolbarRef}
      className="fixed z-[9999] bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 flex gap-1"
      style={{
        left: toolbarPosition.x,
        top: toolbarPosition.y,
        transform: 'translateX(-50%)',
      }}
    >
      {/* 删除按钮 */}
      <Button
        size="sm"
        variant="ghost"
        onClick={handleDelete}
        className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400"
        title="删除元素"
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      {/* 编辑文本按钮 - 只在有文本时显示 */}
      {hasText && (
        <Button
          size="sm"
          variant="ghost"
          onClick={handleEditText}
          className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
          title="编辑文本"
        >
          <Edit3 className="h-4 w-4" />
        </Button>
      )}

      {/* 小三角指示器 */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-200 dark:border-t-gray-700"></div>
    </div>
  );
}
