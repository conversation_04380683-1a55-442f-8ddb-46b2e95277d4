<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArtSphere | Contemporary Art Gallery - 修复测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Raleway:wght@300;400;600&display=swap');
        
        :root {
            --primary: #2a2a2a;
            --secondary: #e8c07d;
            --accent: #9b5de5;
            --light: #f8f9fa;
        }
        
        body {
            font-family: 'Raleway', sans-serif;
            background-color: var(--light);
            color: var(--primary);
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4 {
            font-family: 'Playfair Display', serif;
        }
        
        .hero {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1563089145-599997674d42?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
            min-height: 90vh;
        }
        
        .artwork-card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .artwork-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 20px rgba(0, 0, 0, 0.15);
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--secondary);
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .btn-primary {
            background-color: var(--secondary);
            color: var(--primary);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: var(--primary);
            color: var(--light);
        }
        
        .btn-outline {
            border: 2px solid var(--secondary);
            color: var(--secondary);
            transition: all 0.3s ease;
        }
        
        .btn-outline:hover {
            background-color: var(--secondary);
            color: var(--primary);
        }
    </style>
    <style id="loomrun-custom-styles" type="text/css">
      /* 这里将包含精确的CSS选择器，确保样式修改只影响选中元素 */
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="bg-white shadow-md fixed w-full z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <a href="javascript:void(0)" class="text-2xl font-bold text-primary">
                    <span class="text-secondary">Art</span>Sphere
                </a>
                
                <div class="hidden md:flex space-x-8">
                    <a href="javascript:void(0)" class="nav-link">Home</a>
                    <a href="javascript:void(0)" class="nav-link">Exhibitions</a>
                    <a href="javascript:void(0)" class="nav-link">Artists</a>
                    <a href="javascript:void(0)" class="nav-link">Collections</a>
                    <a href="javascript:void(0)" class="nav-link">Events</a>
                    <a href="javascript:void(0)" class="nav-link">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section - 保持原有背景效果 -->
    <section class="hero flex items-center justify-center text-white pt-20">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-5xl md:text-7xl font-bold mb-6">艺术在此绽放</h1>
            <!-- 这个p元素现在使用精确的CSS选择器，不会影响其他元素 -->
            <p class="text-xl md:text-2xl mb-10 max-w-2xl mx-auto">
                Discover contemporary masterpieces from emerging and established artists worldwide.
            </p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <button onclick="event.preventDefault()" class="btn-primary px-8 py-3 rounded-full font-semibold">View Current Exhibition</button>
                <button onclick="event.preventDefault()" class="btn-outline px-8 py-3 rounded-full font-semibold">Become a Member</button>
            </div>
        </div>
    </section>

    <!-- Featured Artists -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Featured Artists</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Meet the visionary creators shaping the contemporary art landscape.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <!-- Artist 1 -->
                <div class="artwork-card overflow-hidden rounded-lg shadow-lg">
                    <div class="h-80 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" 
                             alt="Artist" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-2">Marcus Chen</h3>
                        <p class="text-gray-600 mb-4">Mixed Media | New York</p>
                        <p class="text-gray-700 mb-4">Exploring the intersection of technology and traditional art forms.</p>
                        <button onclick="event.preventDefault()" class="btn-outline px-6 py-2 rounded-full text-sm">View Portfolio</button>
                    </div>
                </div>
                
                <!-- Artist 2 -->
                <div class="artwork-card overflow-hidden rounded-lg shadow-lg">
                    <div class="h-80 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" 
                             alt="Artist" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-2">Sophia Laurent</h3>
                        <p class="text-gray-600 mb-4">Oil Painting | Paris</p>
                        <p class="text-gray-700 mb-4">Revolutionizing classical techniques with modern perspectives.</p>
                        <button onclick="event.preventDefault()" class="btn-outline px-6 py-2 rounded-full text-sm">View Portfolio</button>
                    </div>
                </div>
                
                <!-- Artist 3 -->
                <div class="artwork-card overflow-hidden rounded-lg shadow-lg">
                    <div class="h-80 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" 
                             alt="Artist" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-2">Kwame Osei</h3>
                        <p class="text-gray-600 mb-4">Sculpture | Accra</p>
                        <p class="text-gray-700 mb-4">Transforming recycled materials into powerful cultural statements.</p>
                        <button onclick="event.preventDefault()" class="btn-outline px-6 py-2 rounded-full text-sm">View Portfolio</button>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <button onclick="event.preventDefault()" class="btn-primary px-8 py-3 rounded-full font-semibold">View All Artists</button>
            </div>
        </div>
    </section>

    <script>
        // 模拟手动编辑功能的测试
        function testPreciseStyleApplication() {
            console.log('🧪 测试精确样式应用...');
            
            // 模拟选中hero区域的p元素
            const heroDescription = document.querySelector('.hero p');
            if (heroDescription) {
                console.log('✅ 找到hero描述元素', {
                    tagName: heroDescription.tagName,
                    className: heroDescription.className,
                    textContent: heroDescription.textContent.substring(0, 50) + '...'
                });
                
                // 这里应该调用修复后的StyleManager.applyStylesToCSS方法
                // 确保样式只应用到这个特定元素，不影响其他p元素
            }
        }
        
        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', testPreciseStyleApplication);
    </script>
</body>
</html>
