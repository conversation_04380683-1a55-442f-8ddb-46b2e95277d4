import { NextRequest, NextResponse } from "next/server";
import { loginWithPhone } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    // 生成测试手机号
    const testPhone = `138${Math.floor(10000000 + Math.random() * 90000000)}`;
    const testCode = "123456"; // 测试验证码
    
    console.log(`🧪 开始创建测试用户，手机号: ${testPhone}`);

    // 先插入验证码记录（模拟发送验证码）
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);
    
    await executeQuery(
      'INSERT INTO phone_verifications (phone, code, expires_at) VALUES (?, ?, ?)',
      [testPhone, testCode, expiresAt]
    );

    console.log(`📱 验证码记录已插入: ${testCode}`);

    // 模拟客户端信息
    const ipAddress = '127.0.0.1';
    const userAgent = 'Test User Agent - LoomRun Registration Test';

    // 调用登录接口（会自动创建新用户并发放积分）
    const result = await loginWithPhone(testPhone, testCode, ipAddress, userAgent);

    if (result) {
      console.log(`✅ 测试用户创建成功: ID=${result.user.id}, 昵称=${result.user.nickname}`);
      
      // 查询用户的积分记录
      const pointsHistory = await executeQuery(
        `SELECT 
          pt.id, pt.transaction_type, pt.points_amount, pt.source_type, 
          pt.description, pt.created_at
        FROM points_transactions pt 
        WHERE pt.user_id = ? 
        ORDER BY pt.created_at DESC`,
        [result.user.id]
      ) as any[];

      // 查询注册日志
      const registrationLog = await executeQuery(
        `SELECT 
          rl.id, rl.registration_type, rl.points_awarded, 
          rl.ip_address, rl.user_agent, rl.created_at
        FROM registration_logs rl 
        WHERE rl.user_id = ?`,
        [result.user.id]
      ) as any[];

      return NextResponse.json({
        success: true,
        message: "测试用户创建成功",
        data: {
          user: {
            id: result.user.id,
            nickname: result.user.nickname,
            phone: result.user.phone,
            points: result.user.points,
            total_earned_points: result.user.total_earned_points,
            invite_code: result.user.invite_code
          },
          pointsHistory: pointsHistory,
          registrationLog: registrationLog,
          testInfo: {
            phone: testPhone,
            code: testCode,
            ipAddress: ipAddress,
            userAgent: userAgent
          }
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: "测试用户创建失败",
        error: "登录函数返回null"
      }, { status: 500 });
    }

  } catch (error) {
    console.error("创建测试用户失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
