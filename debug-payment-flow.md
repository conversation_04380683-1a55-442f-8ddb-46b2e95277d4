# 支付流程调试指南

## 🔍 问题分析

### 1. **订单过期问题**
- **原因**: 订单创建时 `order_expires_at` 时间设置错误
- **表现**: 订单一创建就显示"已过期"
- **修复**: 修正了时区处理和时间格式化

### 2. **支付弹窗消失问题**
- **原因**: 支付弹窗作为订阅弹窗的子组件，当订阅弹窗关闭时支付弹窗也被销毁
- **表现**: 点击"立即支付"后支付弹窗消失
- **修复**: 将支付弹窗移到顶层组件管理

## 🛠️ 修复内容

### 1. **订单创建API修复**
- 修复了 `app/api/orders/subscription/route.ts` 中的时间设置
- 修复了 `app/api/orders/recharge/route.ts` 中的时间设置
- 添加了调试日志来跟踪时间设置

### 2. **支付弹窗架构重构**
- 将 `PaymentModal` 从 `SubscriptionModal` 中移出
- 在 `TopUserMenu` 组件中统一管理两个弹窗
- 通过回调函数实现弹窗间的通信

### 3. **时间处理优化**
```javascript
// 修复前（有问题）
const orderExpiresAt = new Date(Date.now() + expireMinutes * 60 * 1000);

// 修复后（正确）
const now = new Date();
const orderExpiresAt = new Date(now.getTime() + expireMinutes * 60 * 1000);
const formatDateTime = (date) => {
  return date.getFullYear() + '-' +
    String(date.getMonth() + 1).padStart(2, '0') + '-' +
    String(date.getDate()).padStart(2, '0') + ' ' +
    String(date.getHours()).padStart(2, '0') + ':' +
    String(date.getMinutes()).padStart(2, '0') + ':' +
    String(date.getSeconds()).padStart(2, '0');
};
```

## 🧪 测试步骤

### 1. **测试订单创建**
1. 打开订阅积分弹窗
2. 选择任意套餐
3. 点击"立即支付"
4. 检查控制台日志，确认时间设置正确

### 2. **测试支付流程**
1. 订单创建成功后，订阅弹窗应该关闭
2. 支付弹窗应该独立显示
3. 支付弹窗中应该显示正确的倒计时
4. 点击"模拟支付成功"应该能正常完成支付

### 3. **测试重复订单**
1. 创建订单但不支付
2. 再次点击"立即支付"
3. 应该提示有未完成订单
4. 选择"继续支付"应该打开支付弹窗

## 🔧 调试工具

### 1. **控制台日志**
- 订单创建时会输出时间设置日志
- 支付弹窗会输出倒计时更新日志
- 查看这些日志可以确认时间设置是否正确

### 2. **数据库检查**
```sql
-- 检查最新订单的时间设置
SELECT 
  id, order_no, status, 
  created_at, 
  order_expires_at,
  TIMESTAMPDIFF(MINUTE, created_at, order_expires_at) as expire_minutes,
  TIMESTAMPDIFF(SECOND, NOW(), order_expires_at) as remaining_seconds
FROM membership_orders 
ORDER BY created_at DESC 
LIMIT 5;
```

### 3. **系统设置检查**
```sql
-- 检查订单过期时间设置
SELECT setting_key, setting_value 
FROM system_settings 
WHERE setting_key = 'order_expire_minutes';
```

## 📋 预期结果

### ✅ 正常流程
1. 订单创建时间和过期时间相差30分钟
2. 支付弹窗显示正确的倒计时（29:xx）
3. 支付弹窗独立显示，不会因为订阅弹窗关闭而消失
4. 模拟支付成功后显示成功页面

### ❌ 异常情况
1. 如果订单立即过期，检查时区设置
2. 如果支付弹窗消失，检查组件层级关系
3. 如果倒计时不准确，检查时间格式化

## 🚀 后续优化

### 1. **性能优化**
- 考虑使用 React.memo 优化组件渲染
- 添加支付弹窗的懒加载

### 2. **用户体验**
- 添加支付进度指示器
- 优化错误提示信息
- 添加支付超时提醒

### 3. **监控和日志**
- 添加支付成功率统计
- 记录用户支付行为
- 监控订单过期情况

## 💡 注意事项

1. **时区一致性**: 确保前后端时区设置一致
2. **组件生命周期**: 注意弹窗组件的挂载和卸载
3. **状态管理**: 避免状态冲突和内存泄漏
4. **错误处理**: 完善各种异常情况的处理

## 🎯 成功标准

- ✅ 订单创建后有30分钟有效期
- ✅ 支付弹窗独立显示且功能正常
- ✅ 倒计时准确显示剩余时间
- ✅ 模拟支付能够成功完成
- ✅ 支付成功后积分正确发放
