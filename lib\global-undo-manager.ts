/**
 * 🎯 全局撤销管理器 - 合理的元素撤销系统
 *
 * 核心特性：
 * 1. 记录每个元素的原始状态
 * 2. 撤销 = 恢复元素到修改前的原始状态
 * 3. 不记录中间调整过程，避免无数次撤销
 * 4. 一次撤销 = 一个元素完全恢复
 */

export interface ElementOriginalState {
  elementSelector: string;       // 元素选择器
  elementId?: string;           // 元素ID（如果有）
  elementClassName?: string;    // 元素类名（用于辅助定位）
  originalStyles: Record<string, string>; // 原始样式值
  modifiedStyles: Record<string, string>; // 当前修改的样式值
  timestamp: number;            // 首次修改时间戳
  lastModified: number;         // 最后修改时间戳
}

export interface GlobalUndoState {
  modifiedElements: ElementOriginalState[];
}

export class GlobalUndoManager {
  private static instance: GlobalUndoManager | null = null;
  private modifiedElements: Map<string, ElementOriginalState> = new Map();
  private maxHistorySize: number = 50; // 最多记录50个元素的修改
  private onStateChange?: (canUndo: boolean, canRedo: boolean) => void;
  private iframe: HTMLIFrameElement | null = null;
  private isLocked: boolean = false;
  private lockOwner: string = '';

  private constructor() {
    console.log('🎯 全局撤销管理器初始化');
  }

  /**
   * 获取单例实例
   */
  static getInstance(): GlobalUndoManager {
    if (!GlobalUndoManager.instance) {
      GlobalUndoManager.instance = new GlobalUndoManager();
    }
    return GlobalUndoManager.instance;
  }

  /**
   * 锁定管理器，防止被其他组件干扰
   */
  lock(owner: string): boolean {
    if (this.isLocked && this.lockOwner !== owner) {
      console.warn('❌ 全局撤销管理器已被锁定', {
        currentOwner: this.lockOwner,
        requestOwner: owner
      });
      return false;
    }

    this.isLocked = true;
    this.lockOwner = owner;
    console.log('🔒 全局撤销管理器已锁定', { owner });
    return true;
  }

  /**
   * 解锁管理器
   */
  unlock(owner: string): boolean {
    if (this.isLocked && this.lockOwner !== owner) {
      console.warn('❌ 无权解锁全局撤销管理器', {
        currentOwner: this.lockOwner,
        requestOwner: owner
      });
      return false;
    }

    this.isLocked = false;
    this.lockOwner = '';
    console.log('🔓 全局撤销管理器已解锁', { owner });
    return true;
  }

  /**
   * 设置iframe引用
   */
  setIframe(iframe: HTMLIFrameElement, owner: string = 'unknown') {
    // 🎯 关键修复：检查锁定状态
    if (this.isLocked && this.lockOwner !== owner) {
      console.warn('❌ 无法设置iframe：管理器已被锁定', {
        currentOwner: this.lockOwner,
        requestOwner: owner
      });
      return;
    }

    // 🎯 关键修复：检查iframe是否有效
    if (!iframe || !iframe.contentDocument) {
      console.warn('❌ 设置无效的iframe引用');
      return;
    }

    // 🎯 关键修复：记录iframe的来源信息
    const iframeInfo = {
      src: iframe.src,
      title: iframe.title,
      hasDocument: !!iframe.contentDocument,
      documentURL: iframe.contentDocument?.URL || 'unknown'
    };

    console.log('🎯 全局撤销管理器：设置iframe引用', {
      owner,
      previousIframe: !!this.iframe,
      newIframeInfo: iframeInfo,
      isLocked: this.isLocked,
      lockOwner: this.lockOwner
    });

    this.iframe = iframe;
  }

  /**
   * 设置状态变化回调
   */
  setOnStateChange(callback: (canUndo: boolean, canRedo: boolean) => void) {
    this.onStateChange = callback;
  }

  /**
   * 🎯 精确元素定位系统 - 生成唯一的元素选择器
   */
  private generateElementSelector(element: HTMLElement): string {
    // 优先使用ID
    if (element.id) {
      return `#${element.id}`;
    }

    // 🎯 核心策略：始终使用位置选择器确保唯一性
    return this.generateUniquePathSelector(element);
  }

  /**
   * 🎯 生成唯一的路径选择器（类似浏览器开发者工具）
   */
  private generateUniquePathSelector(element: HTMLElement): string {
    const path: string[] = [];
    let currentElement: HTMLElement | null = element;

    while (currentElement && currentElement !== document.body) {
      let selector = currentElement.tagName.toLowerCase();

      // 如果有ID，直接使用ID并停止向上遍历
      if (currentElement.id) {
        selector = `#${currentElement.id}`;
        path.unshift(selector);
        break;
      }

      // 🎯 关键：计算在同类型兄弟元素中的位置
      const parent = currentElement.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter(
          child => child.tagName === currentElement!.tagName
        );

        if (siblings.length > 1) {
          const index = siblings.indexOf(currentElement);
          selector += `:nth-of-type(${index + 1})`;
        }
      }

      path.unshift(selector);
      currentElement = currentElement.parentElement;
    }

    const finalSelector = path.join(' > ');

    console.log('🎯 生成唯一路径选择器', {
      element: element.tagName,
      className: element.className,
      textContent: element.textContent?.substring(0, 20) + '...',
      selector: finalSelector,
      pathLength: path.length
    });

    return finalSelector;
  }

  /**
   * 根据选择器查找元素
   */
  private findElementBySelector(selector: string): HTMLElement | null {
    if (!this.iframe || !this.iframe.contentDocument) {
      console.warn('❌ 无法查找元素：iframe未设置', {
        hasIframe: !!this.iframe,
        hasDocument: !!this.iframe?.contentDocument
      });
      return null;
    }

    // 🎯 关键调试：记录当前iframe信息
    console.log('🔍 查找元素详细信息', {
      selector,
      iframeTitle: this.iframe.title,
      iframeSrc: this.iframe.src,
      documentURL: this.iframe.contentDocument.URL,
      documentElementCount: this.iframe.contentDocument.querySelectorAll('*').length
    });

    try {
      // 🎯 首先尝试原始选择器
      let element = this.iframe.contentDocument.querySelector(selector) as HTMLElement;

      if (element) {
        console.log('✅ 元素定位成功（原始选择器）', { selector, element: element.tagName });
        return element;
      }

      // 🎯 如果原始选择器失败，尝试添加高亮类的选择器
      if (selector.startsWith('.') && !selector.includes('selected-element-highlight')) {
        const highlightSelector = selector + '.selected-element-highlight';
        element = this.iframe.contentDocument.querySelector(highlightSelector) as HTMLElement;

        if (element) {
          console.log('✅ 元素定位成功（高亮选择器）', {
            originalSelector: selector,
            highlightSelector,
            element: element.tagName
          });
          return element;
        }
      }

      // 🎯 如果还是失败，尝试移除高亮类的选择器
      if (selector.includes('selected-element-highlight')) {
        const cleanSelector = selector.replace('.selected-element-highlight', '');
        element = this.iframe.contentDocument.querySelector(cleanSelector) as HTMLElement;

        if (element) {
          console.log('✅ 元素定位成功（清理选择器）', {
            originalSelector: selector,
            cleanSelector,
            element: element.tagName
          });
          return element;
        }
      }

      console.warn('❌ 元素定位失败', { selector });
      return null;
    } catch (error) {
      console.error('❌ 元素查找出错', { selector, error });

      // 🎯 关键修复：如果选择器语法错误，尝试简化选择器
      if (error instanceof DOMException && error.name === 'SyntaxError') {
        console.log('🔧 尝试简化选择器以修复语法错误');

        try {
          // 尝试使用标签名查找
          if (selector.startsWith('.')) {
            // 如果有多个类，尝试只使用第一个类
            const firstClass = selector.split('.')[1];
            if (firstClass) {
              const simpleSelector = `.${firstClass}`;
              element = this.iframe.contentDocument.querySelector(simpleSelector) as HTMLElement;

              if (element) {
                console.log('✅ 元素定位成功（简化选择器）', {
                  originalSelector: selector,
                  simpleSelector,
                  element: element.tagName
                });
                return element;
              }
            }
          }
        } catch (fallbackError) {
          console.error('❌ 简化选择器也失败', { fallbackError });
        }
      }

      return null;
    }
  }

  /**
   * 🎯 新的合理撤销逻辑：记录元素的原始状态，而不是每次变化
   */
  recordElementModification(
    element: HTMLElement,
    property: string,
    originalValue: string,
    currentValue: string
  ): string {
    const elementSelector = this.generateElementSelector(element);
    const elementKey = elementSelector;

    // 检查是否已经记录了这个元素
    let elementState = this.modifiedElements.get(elementKey);

    if (!elementState) {
      // 🎯 首次修改：记录原始状态
      elementState = {
        elementSelector,
        elementId: element.id || undefined,
        elementClassName: element.className || undefined,
        originalStyles: { [property]: originalValue },
        modifiedStyles: { [property]: currentValue },
        timestamp: Date.now(),
        lastModified: Date.now()
      };

      this.modifiedElements.set(elementKey, elementState);

      console.log('📝 新元素修改已记录', {
        elementSelector,
        property,
        originalValue,
        currentValue,
        isFirstModification: true
      });
    } else {
      // 🎯 后续修改：只更新当前值，保持原始值不变
      if (!elementState.originalStyles[property]) {
        elementState.originalStyles[property] = originalValue;
      }
      elementState.modifiedStyles[property] = currentValue;
      elementState.lastModified = Date.now();

      console.log('📝 元素修改已更新', {
        elementSelector,
        property,
        originalValue: elementState.originalStyles[property],
        currentValue,
        isFirstModification: false
      });
    }

    // 限制记录的元素数量
    if (this.modifiedElements.size > this.maxHistorySize) {
      // 删除最旧的记录
      const oldestKey = Array.from(this.modifiedElements.keys())[0];
      this.modifiedElements.delete(oldestKey);
    }

    this.notifyStateChange();
    return elementKey;
  }

  /**
   * 🎯 新的合理撤销逻辑：撤销最近修改的元素到原始状态
   */
  undo(): boolean {
    if (!this.canUndo()) {
      console.log('❌ 无法撤销：没有可撤销的元素');
      return false;
    }

    try {
      // 找到最近修改的元素
      const sortedElements = Array.from(this.modifiedElements.entries())
        .sort(([, a], [, b]) => b.lastModified - a.lastModified);

      if (sortedElements.length === 0) {
        console.log('❌ 没有可撤销的元素');
        return false;
      }

      const [elementKey, elementState] = sortedElements[0];

      console.log('🎯 开始撤销元素到原始状态', {
        elementSelector: elementState.elementSelector,
        originalStyles: elementState.originalStyles,
        modifiedStyles: elementState.modifiedStyles,
        modifiedPropertiesCount: Object.keys(elementState.modifiedStyles).length
      });

      // 查找目标元素
      const element = this.findElementBySelector(elementState.elementSelector);
      if (!element) {
        console.error('❌ 撤销失败：无法找到目标元素', {
          selector: elementState.elementSelector
        });
        return false;
      }

      // 🎯 关键：恢复所有修改的属性到原始状态
      let restoredCount = 0;
      for (const [property, originalValue] of Object.entries(elementState.originalStyles)) {
        if (originalValue) {
          // 有原始值，设置回原始值
          element.style.setProperty(property, originalValue, 'important');
          console.log('🎯 属性已恢复到原始值', {
            property,
            originalValue,
            currentValue: element.style.getPropertyValue(property)
          });
        } else {
          // 🎯 关键修复：原始值为空，完全移除内联样式
          element.style.removeProperty(property);
          console.log('🎯 属性已移除（恢复到透明/默认）', {
            property,
            removedInlineStyle: true,
            currentComputedValue: this.iframe?.contentWindow ?
              this.iframe.contentWindow.getComputedStyle(element).getPropertyValue(property) : 'unknown'
          });
        }
        restoredCount++;
      }

      // 从记录中移除这个元素
      this.modifiedElements.delete(elementKey);

      console.log('✅ 元素撤销成功', {
        elementSelector: elementState.elementSelector,
        restoredProperties: restoredCount,
        remainingElements: this.modifiedElements.size
      });

      this.notifyStateChange();
      return true;
    } catch (error) {
      console.error('❌ 元素撤销失败:', error);
      return false;
    }
  }

  /**
   * 重做操作（新逻辑下不支持重做）
   */
  redo(): boolean {
    console.log('ℹ️ 新的撤销逻辑不支持重做，撤销即完全恢复元素到原始状态');
    return false;
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return this.modifiedElements.size > 0;
  }

  /**
   * 检查是否可以重做（新逻辑下不支持重做）
   */
  canRedo(): boolean {
    return false; // 新的撤销逻辑不支持重做，因为撤销就是完全恢复
  }

  /**
   * 获取当前状态
   */
  getState(): GlobalUndoState {
    return {
      modifiedElements: Array.from(this.modifiedElements.values())
    };
  }

  /**
   * 获取历史记录摘要（用于调试）
   */
  getHistorySummary(): Array<{
    elementSelector: string;
    modifiedProperties: string[];
    originalStyles: Record<string, string>;
    modifiedStyles: Record<string, string>;
    firstModified: string;
    lastModified: string;
  }> {
    return Array.from(this.modifiedElements.values()).map(element => ({
      elementSelector: element.elementSelector,
      modifiedProperties: Object.keys(element.modifiedStyles),
      originalStyles: element.originalStyles,
      modifiedStyles: element.modifiedStyles,
      firstModified: new Date(element.timestamp).toLocaleTimeString(),
      lastModified: new Date(element.lastModified).toLocaleTimeString()
    }));
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.modifiedElements.clear();
    this.notifyStateChange();
    console.log('🧹 全局撤销历史已清空');
  }

  /**
   * 标准化颜色值用于比较
   */
  private normalizeColorForComparison(color: string): string {
    if (!color) return '';

    // 如果是十六进制格式，转换为RGB格式进行比较
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      if (hex.length === 6) {
        const r = parseInt(hex.slice(0, 2), 16);
        const g = parseInt(hex.slice(2, 4), 16);
        const b = parseInt(hex.slice(4, 6), 16);
        return `rgb(${r}, ${g}, ${b})`;
      }
    }

    // 如果已经是RGB格式，标准化空格
    if (color.startsWith('rgb(')) {
      return color.replace(/\s+/g, ' ').replace(/,\s*/g, ', ');
    }

    return color;
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange() {
    if (this.onStateChange) {
      this.onStateChange(this.canUndo(), this.canRedo());
    }
  }

  /**
   * 销毁实例（用于测试）
   */
  static destroy() {
    GlobalUndoManager.instance = null;
  }
}
