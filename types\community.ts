// 社区项目相关类型定义

export interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount?: number;
  isFavorited?: boolean;
  
  // 新增预览图相关字段（可选，兼容现有数据）
  previewImageUrl?: string | null;
  previewImageType?: 'auto' | 'manual' | 'upload' | 'static';
  previewMetadata?: any;
  customTitle?: string; // 为了API兼容性保留，实际上就是title字段
  screenshotConfig?: any;
}

export interface PreviewImage {
  dataUrl: string;
  blob: Blob;
  cropArea?: CropArea;
  type: 'auto' | 'manual' | 'upload';
}

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenshotConfig {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'png' | 'jpeg' | 'webp';
  devicePixelRatio?: number;
  delay?: number;
}

export interface ShareFormData {
  projectId: number;
  htmlContent: string;
  customTitle: string;
  previewImage?: File;
  previewImageType?: 'auto' | 'manual' | 'upload';
  cropArea?: CropArea;
} 