import { NextRequest, NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const phone = searchParams.get('phone') || '15422635413';
    const code = searchParams.get('code') || '735468';
    
    console.log(`🧪 测试验证码验证: 手机号=${phone}, 验证码=${code}`);
    
    // 查询所有相关的验证码记录
    const allCodes = await executeQuery(
      `SELECT id, code, expires_at, is_used, created_at,
              TIMESTAMPDIFF(MINUTE, created_at, NOW()) as minutes_ago,
              (expires_at > NOW()) as is_not_expired,
              (TIMESTAMPDIFF(MINUTE, created_at, NOW()) < 10) as within_10_minutes
       FROM phone_verifications 
       WHERE phone = ? 
       ORDER BY created_at DESC LIMIT 5`,
      [phone]
    ) as any[];
    
    console.log(`📋 该手机号的验证码记录:`, allCodes);
    
    // 测试原始查询
    const originalQuery = await executeQuery(
      `SELECT id, expires_at FROM phone_verifications 
       WHERE phone = ? AND code = ? AND expires_at > NOW() AND is_used = FALSE
       ORDER BY created_at DESC LIMIT 1`,
      [phone, code]
    ) as any[];
    
    // 测试修正后的查询
    const modifiedQuery = await executeQuery(
      `SELECT id, expires_at, created_at FROM phone_verifications 
       WHERE phone = ? AND code = ? AND is_used = FALSE
       AND (expires_at > NOW() OR TIMESTAMPDIFF(MINUTE, created_at, NOW()) < 10)
       ORDER BY created_at DESC LIMIT 1`,
      [phone, code]
    ) as any[];
    
    // 测试最简单的查询
    const simpleQuery = await executeQuery(
      `SELECT id, expires_at, created_at, is_used FROM phone_verifications 
       WHERE phone = ? AND code = ?
       ORDER BY created_at DESC LIMIT 1`,
      [phone, code]
    ) as any[];
    
    return NextResponse.json({
      success: true,
      data: {
        phone: phone,
        code: code,
        allRecords: allCodes,
        originalQueryResult: originalQuery,
        modifiedQueryResult: modifiedQuery,
        simpleQueryResult: simpleQuery,
        currentTime: new Date().toISOString(),
        analysis: {
          hasRecords: allCodes.length > 0,
          latestRecord: allCodes[0] || null,
          originalQueryWorks: originalQuery.length > 0,
          modifiedQueryWorks: modifiedQuery.length > 0,
          simpleQueryWorks: simpleQuery.length > 0
        }
      }
    });

  } catch (error) {
    console.error("验证码测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
