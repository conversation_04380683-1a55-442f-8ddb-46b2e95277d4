-- 更新邀请码长度设置
-- 建议使用8位或更长的邀请码以避免重复

-- 查看当前设置
SELECT 
    setting_key,
    setting_value as current_length,
    description
FROM system_settings 
WHERE setting_key = 'invitation_code_length';

-- 更新邀请码长度为8位（推荐）
UPDATE system_settings 
SET setting_value = '8', 
    updated_at = NOW() 
WHERE setting_key = 'invitation_code_length';

-- 如果您想使用10位（更安全）
-- UPDATE system_settings 
-- SET setting_value = '10', 
--     updated_at = NOW() 
-- WHERE setting_key = 'invitation_code_length';

-- 如果您想使用12位（极其安全）
-- UPDATE system_settings 
-- SET setting_value = '12', 
--     updated_at = NOW() 
-- WHERE setting_key = 'invitation_code_length';

-- 验证更新结果
SELECT 
    setting_key,
    setting_value as new_length,
    description,
    updated_at
FROM system_settings 
WHERE setting_key = 'invitation_code_length';

-- 查看现有用户邀请码长度分布
SELECT 
    LENGTH(invite_code) as code_length,
    COUNT(*) as count,
    GROUP_CONCAT(invite_code ORDER BY id DESC LIMIT 5) as examples
FROM users 
WHERE invite_code IS NOT NULL 
GROUP BY LENGTH(invite_code)
ORDER BY code_length;

-- 邀请码容量分析
SELECT 
    '4位' as length_type,
    POW(36, 4) as total_combinations,
    '1,679,616 (1.68M)' as formatted_capacity,
    '适合小规模应用' as recommendation
UNION ALL
SELECT 
    '6位' as length_type,
    POW(36, 6) as total_combinations,
    '2,176,782,336 (2.18B)' as formatted_capacity,
    '适合中等规模应用' as recommendation
UNION ALL
SELECT 
    '8位' as length_type,
    POW(36, 8) as total_combinations,
    '2,821,109,907,456 (2.82T)' as formatted_capacity,
    '推荐：适合大规模应用' as recommendation
UNION ALL
SELECT 
    '10位' as length_type,
    POW(36, 10) as total_combinations,
    '3,656,158,440,062,976 (3.66P)' as formatted_capacity,
    '超大规模应用' as recommendation
UNION ALL
SELECT 
    '12位' as length_type,
    POW(36, 12) as total_combinations,
    '4,738,381,338,321,616,896 (4.74E)' as formatted_capacity,
    '企业级应用' as recommendation;
