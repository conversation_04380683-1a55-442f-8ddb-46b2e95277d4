/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 01/08/2025 09:18:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for points_packages
-- ----------------------------
DROP TABLE IF EXISTS `points_packages`;
CREATE TABLE `points_packages`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `package_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `package_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `points_amount` int NOT NULL,
  `original_price` decimal(10, 2) NOT NULL,
  `discount_price` decimal(10, 2) NOT NULL,
  `bonus_points` int NOT NULL DEFAULT 0,
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `display_order` int NOT NULL DEFAULT 999,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_package_key`(`package_key` ASC) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_display_order`(`display_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of points_packages
-- ----------------------------
INSERT INTO `points_packages` VALUES (1, 'basic_100', '入门包', 200, 20.00, 19.90, 0, '200积分充值包，适合轻度使用', 1, 1, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `points_packages` VALUES (2, 'standard_500', '标准包', 600, 60.00, 55.00, 100, '600积分充值包，赠送100积分，最受欢迎', 1, 2, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `points_packages` VALUES (3, 'premium_1000', '高级包', 1200, 120.00, 105.00, 300, '1200积分充值包，赠送300积分，超值优惠', 1, 3, '2025-07-30 03:24:25', '2025-08-01 00:51:37');

SET FOREIGN_KEY_CHECKS = 1;
