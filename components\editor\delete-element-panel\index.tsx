"use client";

import { useState, useEffect } from "react";
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, Undo2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DeleteElementPanelProps {
  deletedElementInfo: {
    tagName: string;
    className: string;
    id: string;
    textContent: string;
    outerHTML: string;
    parentElement: HTMLElement | null;
    nextSibling: Node | null;
  } | null;
  onRestoreElement: () => void;
}

export function DeleteElementPanel({
  deletedElementInfo,
  onRestoreElement
}: DeleteElementPanelProps) {
  if (!deletedElementInfo) {
    return null;
  }

  return (
    <Card className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400 text-sm">
          <Trash2 className="h-4 w-4" />
          元素已删除
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 元素信息 */}
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">标签:</span>
            <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded">
              &lt;{deletedElementInfo.tagName.toLowerCase()}&gt;
            </span>
          </div>

          {deletedElementInfo.className && (
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">类名:</span>
              <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded text-xs max-w-32 truncate">
                {deletedElementInfo.className}
              </span>
            </div>
          )}

          {deletedElementInfo.id && (
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">ID:</span>
              <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded">
                #{deletedElementInfo.id}
              </span>
            </div>
          )}

          <div className="flex justify-between items-start">
            <span className="text-gray-600 dark:text-gray-400">内容:</span>
            <span className="text-xs text-gray-700 dark:text-gray-300 max-w-32 text-right">
              {deletedElementInfo.textContent}
            </span>
          </div>
        </div>

        {/* 状态信息 */}
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-red-700 dark:text-red-300">
              <p className="font-medium mb-1">元素已删除</p>
              <p>此元素已从预览中移除。点击"保存"应用更改，或点击"重置"恢复元素。</p>
            </div>
          </div>
        </div>

        {/* 恢复按钮 */}
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onRestoreElement}
            className="w-full h-8 text-xs border-blue-300 text-blue-600 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20"
          >
            <Undo2 className="h-3 w-3 mr-1" />
            恢复元素
          </Button>
        </div>

        {/* 提示信息 */}
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center pt-1 border-t border-red-200 dark:border-red-800">
          点击"保存"后删除将永久生效，点击"重置"可恢复元素
        </div>
      </CardContent>
    </Card>
  );
}
