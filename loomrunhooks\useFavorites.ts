import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface FavoriteProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  favoritedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount: number;
  isFavorited: boolean;
}

interface FavoritesResponse {
  favorites: FavoriteProject[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function useFavorites() {
  const [favorites, setFavorites] = useState<FavoriteProject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取收藏列表
  const fetchFavorites = useCallback(async (limit = 20, offset = 0) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/community/favorites?limit=${limit}&offset=${offset}`,
        {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: FavoritesResponse = await response.json();
      
      if (offset === 0) {
        setFavorites(data.favorites);
      } else {
        setFavorites(prev => [...prev, ...data.favorites]);
      }

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取收藏列表失败';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 切换收藏状态
  const toggleFavorite = useCallback(async (projectId: number, isFavorited: boolean) => {
    try {
      const response = await fetch('/api/community/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: isFavorited ? 'add' : 'remove'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '收藏操作失败');
      }

      const data = await response.json();
      
      // 更新本地收藏列表
      if (isFavorited) {
        // 如果是添加收藏，不需要立即更新列表，因为需要完整的项目信息
        toast.success('收藏成功');
      } else {
        // 如果是取消收藏，从列表中移除
        setFavorites(prev => prev.filter(fav => fav.id !== projectId));
        toast.success('取消收藏成功');
      }

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '收藏操作失败';
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  // 清空收藏列表
  const clearFavorites = useCallback(() => {
    setFavorites([]);
    setError(null);
  }, []);

  return {
    favorites,
    isLoading,
    error,
    fetchFavorites,
    toggleFavorite,
    clearFavorites
  };
} 