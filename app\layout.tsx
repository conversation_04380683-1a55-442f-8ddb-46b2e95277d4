/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Metadata, Viewport } from "next";


import TanstackProvider from "@/components/providers/tanstack-query-provider";
import { ThemeLanguageProvider } from "@/components/providers/theme-language-provider";
import { ThemeScript } from "@/components/theme-script";
import "@/styles/globals.css";
import { Toaster } from "@/components/ui/sonner";
import AppContext from "@/components/contexts/app-context";

export const metadata: Metadata = {
  title: "LoomRun - AI驱动的网站开发与原型设计平台",
  description:
    "专业的AI辅助开发工具，提供高效的网站构建和原型设计解决方案。快速将创意转化为可部署的产品。",
  openGraph: {
    title: "LoomRun - AI驱动的网站开发与原型设计平台",
    description:
      "专业的AI辅助开发工具，提供高效的网站构建和原型设计解决方案。快速将创意转化为可部署的产品。",
    url: "https://loomrun.top",
    siteName: "LoomRun",
    images: [
      {
        url: "https://loomrun.top/banner.png",
        width: 1200,
        height: 630,
        alt: "LoomRun Open Graph Image",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    title: "LoomRun - AI驱动的网站开发与原型设计平台",
    description:
      "专业的AI辅助开发工具，提供高效的网站构建和原型设计解决方案。快速将创意转化为可部署的产品。",
    images: ["https://loomrun.top/banner.png"],
    creator: "@loomrun",
  },
  metadataBase: new URL("https://loomrun.top"),
  applicationName: "LoomRun",
  appleWebApp: {
    capable: true,
    title: "LoomRun",
    statusBarStyle: "black-translucent",
  },
  icons: {
    icon: [
      // 浅色模式图标（默认）
      { url: "/ghost-icons/light/ghost-icon-light-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/ghost-icons/light/ghost-icon-light-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/ghost-icons/light/ghost-icon-light-48x48.png", sizes: "48x48", type: "image/png" },
      { url: "/ghost-icons/light/ghost-icon-light-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/ghost-icons/light/ghost-icon-light-512x512.png", sizes: "512x512", type: "image/png" },
      // 兜底方案
      { url: "/favicon_io/favicon.ico" },
    ],
    shortcut: "/favicon_io/favicon.ico",
    apple: "/ghost-icons/light/ghost-icon-light-256x256.png",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#f5f3f0",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <ThemeScript />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        {/* 浅色模式图标（默认） */}
        <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/light/ghost-icon-light-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/light/ghost-icon-light-16x16.png" />
        <link rel="icon" type="image/png" sizes="48x48" href="/ghost-icons/light/ghost-icon-light-48x48.png" />

        {/* 深色模式图标 */}
        <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/dark/ghost-icon-dark-32x32.png" media="(prefers-color-scheme: dark)" />
        <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/dark/ghost-icon-dark-16x16.png" media="(prefers-color-scheme: dark)" />
        <link rel="icon" type="image/png" sizes="48x48" href="/ghost-icons/dark/ghost-icon-dark-48x48.png" media="(prefers-color-scheme: dark)" />

        {/* 高分辨率图标 */}
        <link rel="icon" type="image/png" sizes="192x192" href="/ghost-icons/light/ghost-icon-light-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/ghost-icons/light/ghost-icon-light-512x512.png" />

        {/* Apple Touch 图标 */}
        <link rel="apple-touch-icon" sizes="180x180" href="/ghost-icons/light/ghost-icon-light-256x256.png" />

        {/* 兜底方案 */}
        <link rel="icon" type="image/x-icon" href="/favicon_io/favicon.ico" />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body
        className="antialiased bg-background min-h-screen"
        style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}
        suppressHydrationWarning={true}
      >
        <Toaster richColors position="bottom-center" />
        <TanstackProvider>
          <ThemeLanguageProvider>
            <AppContext>{children}</AppContext>
          </ThemeLanguageProvider>
        </TanstackProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 移除Next.js开发工具
              function removeDevTools() {
                const selectors = [
                  '[data-nextjs-dialog-overlay]',
                  '[data-nextjs-dialog]',
                  '[data-nextjs-dialog-left-right]',
                  '[data-nextjs-dialog-backdrop]',
                  'div[id^="__next-dev-indicator"]',
                  'div[data-nextjs-dev-overlay]',
                  '.__next-dev-indicator',
                  '.__next-dev-overlay',
                  '[class*="__next-dev"]',
                  '[class*="nextjs-dev"]',
                  '[data-nextjs-dev]'
                ];
                
                selectors.forEach(selector => {
                  const elements = document.querySelectorAll(selector);
                  elements.forEach(el => {
                    // 确保不删除Popover相关元素
                    if (!el.closest('[data-radix-portal]') && !el.closest('[data-radix-popper-content-wrapper]')) {
                      el.style.display = 'none';
                      el.style.visibility = 'hidden';
                      el.style.opacity = '0';
                      el.style.pointerEvents = 'none';
                      el.remove();
                    }
                  });
                });
                
                // 移除高z-index的固定定位元素（但排除Popover）
                const allFixedElements = document.querySelectorAll('div[style*="position: fixed"]');
                allFixedElements.forEach(el => {
                  const style = el.getAttribute('style') || '';
                  // 确保不删除Popover或其他重要的UI元素
                  if (style.includes('z-index') && (
                    style.includes('2147483647') || 
                    style.includes('999999') || 
                    style.includes('99999')
                  ) && !el.closest('[data-radix-popper-content-wrapper]') && 
                      !el.closest('[data-radix-portal]') && 
                      !el.hasAttribute('data-radix-popover-content') &&
                      !el.querySelector('[data-radix-popover-content]')) {
                    el.remove();
                  }
                });
              }
              
              // 立即执行
              removeDevTools();
              
              // 定期检查并移除
              setInterval(removeDevTools, 100);
              
              // LoomRun 品牌哲学彩蛋
              console.log('%c🚀 LoomRun - 当编织美学遇上暴力效率', 'color: #2563EB; font-size: 16px; font-weight: bold;');
              console.log('%c编织逻辑，运行万物 - 您正在使用数字世界的织梦术', 'color: #7DD3FC; font-size: 12px;');
              console.log('%c输入 loom.run() 查看隐藏功能 ✨', 'color: #F97316; font-size: 10px;');
              
              // 添加全局彩蛋函数
              window.loom = {
                run: () => {
                  console.log('%c🌌 织梦术师模式已激活', 'color: #4ADE80; font-size: 14px; font-weight: bold;');
                  console.log('%c前三天编织逻辑（Loom），后三天运行万物（Run）', 'color: #7DD3FC;');
                  console.log('%c而第七天... 您以天尊之姿，笑看众生用此域织就自己的宇宙', 'color: #F97316;');
                  return '🎭 欢迎来到 LoomRun 的数字纪元';
                }
              };
              
              // DOM变化时执行
              if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver(removeDevTools);
                observer.observe(document.body, { 
                  childList: true, 
                  subtree: true 
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
