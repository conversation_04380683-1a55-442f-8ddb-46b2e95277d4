"use client";

import { useState, useEffect } from "react";
import "./animations.css";
import {
  X,
  Crown,
  Zap,
  Gift,
  Clock,
  Star,
  CreditCard,
  Coins,
  TrendingUp,
  Plus,
  Minus,
  Sparkles,
  Calendar,
  Target
} from "lucide-react";
import { GhostLogo } from "@/components/ui/ghost-logo";

// 🔧 优化：新的计划数据结构，支持月度/年度合并显示
interface SubscriptionPlan {
  plan_type: 'free' | 'pro' | 'max';
  plan_name: string;
  points_included: number;
  points_validity_days: number;
  features: Record<string, any>;
  display_order: number;
  monthly: {
    plan_key: string;
    duration_months: number;
    original_price: number;
    discount_price: number;
    savings: number;
    discount_percent: number;
  } | null;
  yearly: {
    plan_key: string;
    duration_months: number;
    original_price: number;
    discount_price: number;
    monthly_price: string;
    savings: number;
    discount_percent: number;
  } | null;
}

interface PointsPackage {
  id: number;
  package_key: string;
  package_name: string;
  points_amount: number;
  original_price: number;
  discount_price: number;
  bonus_points: number;
  description: string;
  display_order: number;
  total_points: number;
  savings: number;
  discount_percent: number;
  price_per_point: string;
  has_bonus: boolean;
}

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenPayment?: (orderId: number) => void;
}

export default function SubscriptionModal({ isOpen, onClose, onOpenPayment }: SubscriptionModalProps) {
  const [activeTab, setActiveTab] = useState<'subscription' | 'recharge'>('subscription');
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [pointsPackages, setPointsPackages] = useState<PointsPackage[]>([]);
  const [subscriptionEnabled, setSubscriptionEnabled] = useState(true);
  const [rechargeEnabled, setRechargeEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  // 🔧 优化：改为选择计划类型和购买时长
  const [selectedPlanType, setSelectedPlanType] = useState<'free' | 'pro' | 'max' | null>('pro');
  const [selectedPackage, setSelectedPackage] = useState<string | null>('package_500');
  const [selectedDuration, setSelectedDuration] = useState<'monthly' | 'yearly'>('monthly');
  const [quantity, setQuantity] = useState<number | string>(1);

  // 移除支付弹窗状态，改为使用回调

  // 获取数字类型的数量，用于计算
  const getNumericQuantity = (): number => {
    if (typeof quantity === 'string') {
      const parsed = parseInt(quantity);
      return isNaN(parsed) ? 1 : Math.max(1, Math.min(999, parsed));
    }
    return Math.max(1, Math.min(999, quantity));
  };

  // 🔧 修复：使用真实API数据替代硬编码
  useEffect(() => {
    if (isOpen) {
      loadSubscriptionData();
      loadPointsPackagesData();
    }
  }, [isOpen]);

  // 加载订阅计划数据
  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/subscription/plans');
      const result = await response.json();

      if (result.success) {
        setSubscriptionEnabled(result.data.enabled);
        setSubscriptionPlans(result.data.plans || []);

        // 🔧 优化：设置默认选中的计划类型
        if (result.data.plans && result.data.plans.length > 0) {
          const proPlan = result.data.plans.find((p: SubscriptionPlan) => p.plan_type === 'pro');
          setSelectedPlanType(proPlan?.plan_type || result.data.plans[0].plan_type);
        }
      } else {
        setSubscriptionEnabled(false);
        setSubscriptionPlans([]);
      }
    } catch (error) {
      console.error('加载订阅计划失败:', error);
      setSubscriptionEnabled(false);
      setSubscriptionPlans([]);
    }
  };

  // 加载积分套餐数据
  const loadPointsPackagesData = async () => {
    try {
      const response = await fetch('/api/points/packages');
      const result = await response.json();

      if (result.success) {
        setRechargeEnabled(result.data.enabled);
        setPointsPackages(result.data.packages || []);

        // 设置默认选中的套餐
        if (result.data.packages && result.data.packages.length > 0) {
          const standardPkg = result.data.packages.find((p: PointsPackage) => p.package_key.includes('standard'));
          setSelectedPackage(standardPkg?.package_key || result.data.packages[0].package_key);
        }
      } else {
        setRechargeEnabled(false);
        setPointsPackages([]);
      }
    } catch (error) {
      console.error('加载积分套餐失败:', error);
      setRechargeEnabled(false);
      setPointsPackages([]);
    } finally {
      setLoading(false);
    }
  };

  const getPlanTypeInfo = (type: string) => {
    switch (type) {
      case 'free':
        return { 
          icon: Gift, 
          color: 'text-emerald-600', 
          bgColor: 'bg-emerald-50 border-emerald-200',
          gradientFrom: 'from-emerald-500',
          gradientTo: 'to-green-600'
        };
      case 'pro':
        return { 
          icon: Crown, 
          color: 'text-blue-600', 
          bgColor: 'bg-blue-50 border-blue-200',
          gradientFrom: 'from-blue-500',
          gradientTo: 'to-indigo-600'
        };
      case 'max':
        return { 
          icon: Star, 
          color: 'text-purple-600', 
          bgColor: 'bg-purple-50 border-purple-200',
          gradientFrom: 'from-purple-500',
          gradientTo: 'to-pink-600'
        };
      default:
        return { 
          icon: Zap, 
          color: 'text-gray-600', 
          bgColor: 'bg-gray-50 border-gray-200',
          gradientFrom: 'from-gray-500',
          gradientTo: 'to-gray-600'
        };
    }
  };

  const handleSubscribe = async (planKey: string) => {
    if (!planKey) return;

    try {
      setLoading(true);

      // 直接尝试创建订单，让后端处理重复订单检测
      const response = await fetch('/api/orders/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planKey: planKey,
        }),
      });

      const result = await response.json();

      if (result.success) {
        const orderId = parseInt(result.data.orderId);
        // 通过回调通知父组件打开支付弹窗
        onOpenPayment?.(orderId);
        // 关闭订阅弹窗
        onClose();
      } else if (result.pendingOrder) {
        // 有pending订单，直接打开支付弹窗并传递pending订单信息
        const orderId = parseInt(result.pendingOrder.orderId);
        onOpenPayment?.(orderId);
        // 关闭订阅弹窗
        onClose();
      } else {
        console.error('订阅订单创建失败:', result);
        alert(result.message || '创建订单失败');
      }
    } catch (error) {
      console.error('创建订阅订单失败:', error);
      alert('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleRecharge = async (packageKey: string) => {
    if (!packageKey) return;

    try {
      setLoading(true);

      // 直接尝试创建订单，让后端处理重复订单检测
      const response = await fetch('/api/orders/recharge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageKey: packageKey,
          quantity: getNumericQuantity(),
        }),
      });

      const result = await response.json();

      if (result.success) {
        const orderId = parseInt(result.data.orderId);
        // 通过回调通知父组件打开支付弹窗
        onOpenPayment?.(orderId);
        // 关闭订阅弹窗
        onClose();
      } else if (result.pendingOrder) {
        // 有pending订单，直接打开支付弹窗并传递pending订单信息
        const orderId = parseInt(result.pendingOrder.orderId);
        onOpenPayment?.(orderId);
        // 关闭订阅弹窗
        onClose();
      } else {
        console.error('充值订单创建失败:', result);
        alert(result.message || '创建订单失败');
      }
    } catch (error) {
      console.error('创建充值订单失败:', error);
      alert('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 🔧 优化：获取当前选中的计划和对应时长的价格信息
  const getCurrentPlan = () => {
    return subscriptionPlans.find(plan => plan.plan_type === selectedPlanType);
  };

  const getCurrentPlanPricing = () => {
    const plan = getCurrentPlan();
    if (!plan) return null;
    return selectedDuration === 'monthly' ? plan.monthly : plan.yearly;
  };

  const getCurrentPackage = () => {
    return pointsPackages.find(pkg => pkg.package_key === selectedPackage);
  };

  const calculateTotal = () => {
    if (activeTab === 'subscription') {
      const pricing = getCurrentPlanPricing();
      if (!pricing) return 0;
      return Number(pricing.discount_price); // 订阅会员固定数量为1
    } else {
      const pkg = getCurrentPackage();
      if (!pkg) return 0;

      const numQuantity = getNumericQuantity();
      let basePrice = Number(pkg.discount_price) * numQuantity;

      // 批量折扣逻辑
      if (numQuantity >= 5) {
        basePrice *= 0.9; // 10% 折扣
      } else if (numQuantity >= 3) {
        basePrice *= 0.95; // 5% 折扣
      } else if (numQuantity >= 2) {
        basePrice *= 0.98; // 2% 折扣
      }

      return basePrice;
    }
  };

  // 计算折扣金额
  const calculateDiscount = () => {
    if (activeTab === 'recharge') {
      const pkg = getCurrentPackage();
      if (!pkg) return 0;

      const originalTotal = Number(pkg.discount_price) * getNumericQuantity();
      const discountedTotal = calculateTotal();
      return originalTotal - discountedTotal;
    }
    return 0;
  };

  // 获取批量折扣百分比
  const getBulkDiscountPercent = () => {
    if (activeTab === 'recharge') {
      const numQuantity = getNumericQuantity();
      if (numQuantity >= 5) return 10;
      if (numQuantity >= 3) return 5;
      if (numQuantity >= 2) return 2;
    }
    return 0;
  };

  // 获取积分有效期信息
  const getPointsValidityInfo = () => {
    const now = new Date();
    const startDate = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });

    // 积分包通常是永久有效，但这里可以根据实际业务需求调整
    return {
      startDate,
      endDate: '永久有效',
      isLifetime: true
    };
  };

  // 计算原价总额
  const getOriginalTotal = () => {
    if (activeTab === 'subscription') {
      const pricing = getCurrentPlanPricing();
      if (!pricing) return 0;
      return Number(pricing.original_price); // 订阅会员固定数量为1
    } else {
      const pkg = getCurrentPackage();
      return pkg ? Number(pkg.original_price) * getNumericQuantity() : 0;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0"
        onClick={onClose}
      />

      {/* 弹窗内容 - 放大15% */}
      <div className="absolute left-1/2 top-[10vh] transform -translate-x-1/2 w-full max-w-6xl h-[80vh] max-h-[800px] min-h-[580px] bg-white dark:bg-[#0f0f0f] rounded-2xl shadow-2xl overflow-hidden z-10 mx-4 border border-gray-200/50 dark:border-gray-600/50 backdrop-blur-xl">
        <style jsx>{`
          .subscription-modal-switch {
            position: relative;
            width: 320px;
            height: 48px;
            box-sizing: border-box;
            padding: 4px;
            background: #0d0d0d;
            border-radius: 8px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 0 0 rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
          }

          /* 浅色模式优化 */
          :global(.light) .subscription-modal-switch,
          :global([data-theme="light"]) .subscription-modal-switch {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
          }

          .subscription-modal-switch-slider {
            position: absolute;
            top: 4px;
            left: 4px;
            width: calc(50% - 4px);
            height: calc(100% - 8px);
            background: linear-gradient(135deg, #1b1c1c, #2a2b2b);
            border-radius: 4px;
            box-shadow:
              inset 0 1px 0 0 rgba(255, 255, 255, 0.15),
              0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
          }

          /* 浅色模式滑块优化 */
          :global(.light) .subscription-modal-switch-slider,
          :global([data-theme="light"]) .subscription-modal-switch-slider {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border: 1px solid #dee2e6;
            box-shadow:
              inset 0 1px 0 0 rgba(255, 255, 255, 0.8),
              0 2px 8px rgba(0, 0, 0, 0.15),
              0 1px 3px rgba(0, 0, 0, 0.1);
          }

          .subscription-modal-switch-slider.checked {
            left: calc(50% + 0px);
          }

          .subscription-modal-switch-option {
            position: relative;
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #888;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
            user-select: none;
            border-radius: 4px;
          }

          .subscription-modal-switch-option:hover {
            color: #aaa;
          }

          .subscription-modal-switch-option.active {
            color: #fff;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }

          .subscription-modal-switch-option.inactive {
            color: #666;
            font-weight: 400;
          }

          .subscription-modal-switch-option.inactive:hover {
            color: #888;
          }

          /* 浅色模式选项文字优化 */
          :global(.light) .subscription-modal-switch-option,
          :global([data-theme="light"]) .subscription-modal-switch-option {
            color: #6c757d;
          }

          :global(.light) .subscription-modal-switch-option:hover,
          :global([data-theme="light"]) .subscription-modal-switch-option:hover {
            color: #495057;
          }

          :global(.light) .subscription-modal-switch-option.active,
          :global([data-theme="light"]) .subscription-modal-switch-option.active {
            color: #212529;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
          }

          :global(.light) .subscription-modal-switch-option.inactive,
          :global([data-theme="light"]) .subscription-modal-switch-option.inactive {
            color: #adb5bd;
            font-weight: 400;
          }

          :global(.light) .subscription-modal-switch-option.inactive:hover,
          :global([data-theme="light"]) .subscription-modal-switch-option.inactive:hover {
            color: #6c757d;
          }

          .subscription-modal-switch-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #555;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #444;
          }

          .subscription-modal-switch-option:hover .subscription-modal-switch-indicator {
            background: #777;
            border-color: #666;
          }

          .subscription-modal-switch-option.active .subscription-modal-switch-indicator {
            background: #fff;
            border-color: #fff;
            box-shadow:
              0 0 12px rgba(255, 255, 255, 0.6),
              0 2px 4px rgba(0, 0, 0, 0.2);
            transform: scale(1.2);
          }

          /* 浅色模式指示器优化 */
          :global(.light) .subscription-modal-switch-indicator,
          :global([data-theme="light"]) .subscription-modal-switch-indicator {
            background: #dee2e6;
            border: 1px solid #ced4da;
          }

          :global(.light) .subscription-modal-switch-option:hover .subscription-modal-switch-indicator,
          :global([data-theme="light"]) .subscription-modal-switch-option:hover .subscription-modal-switch-indicator {
            background: #adb5bd;
            border-color: #6c757d;
          }

          :global(.light) .subscription-modal-switch-option.active .subscription-modal-switch-indicator,
          :global([data-theme="light"]) .subscription-modal-switch-option.active .subscription-modal-switch-indicator {
            background: #212529;
            border-color: #212529;
            box-shadow:
              0 0 12px rgba(33, 37, 41, 0.3),
              0 2px 4px rgba(0, 0, 0, 0.15);
            transform: scale(1.2);
          }

          .subscription-modal-switch-option.active.subscription .subscription-modal-switch-indicator {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-color: #3b82f6;
            box-shadow:
              0 0 16px rgba(59, 130, 246, 0.8),
              0 2px 8px rgba(59, 130, 246, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          .subscription-modal-switch-option.active.recharge .subscription-modal-switch-indicator {
            background: linear-gradient(135deg, #10b981, #047857);
            border-color: #10b981;
            box-shadow:
              0 0 16px rgba(16, 185, 129, 0.8),
              0 2px 8px rgba(16, 185, 129, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          /* 浅色模式激活状态指示器优化 */
          :global(.light) .subscription-modal-switch-option.active.subscription .subscription-modal-switch-indicator,
          :global([data-theme="light"]) .subscription-modal-switch-option.active.subscription .subscription-modal-switch-indicator {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-color: #3b82f6;
            box-shadow:
              0 0 16px rgba(59, 130, 246, 0.4),
              0 2px 8px rgba(59, 130, 246, 0.2),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
          }

          :global(.light) .subscription-modal-switch-option.active.recharge .subscription-modal-switch-indicator,
          :global([data-theme="light"]) .subscription-modal-switch-option.active.recharge .subscription-modal-switch-indicator {
            background: linear-gradient(135deg, #10b981, #047857);
            border-color: #10b981;
            box-shadow:
              0 0 16px rgba(16, 185, 129, 0.4),
              0 2px 8px rgba(16, 185, 129, 0.2),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
          }
            box-shadow:
              0 0 16px rgba(16, 185, 129, 0.8),
              0 2px 8px rgba(16, 185, 129, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          .subscription-modal-switch-option svg {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }

          .subscription-modal-switch-option.active svg {
            transform: scale(1.1);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
          }

          .subscription-modal-switch-option.active.subscription svg {
            color: #93c5fd;
          }

          .subscription-modal-switch-option.active.recharge svg {
            color: #6ee7b7;
          }

          .subscription-modal-switch-option.inactive svg {
            opacity: 0.7;
          }

          /* 彻底隐藏数字输入框的上下箭头 - 多重保险 */
          input[type="number"]::-webkit-outer-spin-button,
          input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none !important;
            appearance: none !important;
            margin: 0 !important;
            display: none !important;
            opacity: 0 !important;
            pointer-events: none !important;
            position: absolute !important;
            right: -9999px !important;
          }

          input[type="number"] {
            -moz-appearance: textfield !important;
            appearance: textfield !important;
          }

          .quantity-input::-webkit-outer-spin-button,
          .quantity-input::-webkit-inner-spin-button {
            -webkit-appearance: none !important;
            appearance: none !important;
            margin: 0 !important;
            display: none !important;
            opacity: 0 !important;
            pointer-events: none !important;
            position: absolute !important;
            right: -9999px !important;
            width: 0 !important;
            height: 0 !important;
          }

          .quantity-input {
            -moz-appearance: textfield !important;
            appearance: textfield !important;
          }

          /* Firefox 特殊处理 */
          .quantity-input[type="number"] {
            -moz-appearance: textfield !important;
          }

          /* 通用数字输入框处理 */
          input[type="number"].quantity-input {
            -webkit-appearance: textfield !important;
            -moz-appearance: textfield !important;
            appearance: textfield !important;
          }

          /* 动画支付按钮样式 - 按照原始效果实现 */
          .animated-pay-btn {
            position: relative;
            overflow: hidden;
            transition-duration: 0.3s;
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.103);
          }

          .animated-pay-btn::before {
            width: 100%;
            height: 100%;
            position: absolute;
            content: "";
            background-color: white;
            border-radius: 50%;
            left: -100%;
            top: 0;
            transition-duration: 0.3s;
            mix-blend-mode: difference;
          }

          .animated-pay-btn:hover::before {
            transition-duration: 0.3s;
            transform: translate(100%, -50%);
            border-radius: 0;
          }

          .animated-pay-btn:active {
            transform: translate(5px, 5px);
            transition-duration: 0.3s;
          }
        `}</style>
        {loading ? (
          <div className="flex items-center justify-center h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-[#0f0f0f] dark:to-[#0f0f0f]">
            <div className="flex flex-col items-center gap-6">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-500 dark:border-t-blue-400 rounded-full animate-spin"></div>
                <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-500 dark:border-r-purple-400 rounded-full animate-spin animation-delay-150"></div>
              </div>
              <p className="text-gray-600 dark:text-gray-300 font-medium">正在加载精彩内容...</p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* 顶部栏 - 减少30%高度 */}
            <div className="relative px-8 py-3 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 dark:from-[#0f0f0f] dark:via-[#0f0f0f] dark:to-[#0f0f0f] border-b border-gray-200/50 dark:border-gray-600/30">
              <button
                onClick={onClose}
                className="absolute top-2 right-6 w-8 h-8 rounded-full bg-white/80 dark:bg-[#1a1a1a]/80 hover:bg-white dark:hover:bg-[#2a2a2a] border border-gray-200/50 dark:border-gray-600/50 flex items-center justify-center transition-all duration-200 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 shadow-md z-10"
              >
                <X className="w-5 h-5" />
              </button>

              <div className="flex items-center gap-3">
                <div className="transition-all duration-300 ease-out hover:scale-110 hover:-translate-y-0.5 hover:drop-shadow-lg">
                  <GhostLogo size={32} />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                    获取更多
                    <span
                      className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 bg-clip-text text-transparent font-bold tracking-tight transition-all duration-300 drop-shadow-sm ml-1 mr-1"
                      style={{
                        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                        fontWeight: '700',
                        letterSpacing: '-0.025em'
                      }}
                    >
                      LoomRun
                    </span>
                    积分
                  </h1>
                </div>
              </div>
            </div>

            {/* 主要内容区域 */}
            <div className="flex flex-1 min-h-0">
              {/* 左侧主要内容区 */}
              <div className="flex-1 flex flex-col min-h-0">
                {/* 标签页切换 - 滑块风格 */}
                <div className="px-6 py-3 bg-gradient-to-r from-gray-50/30 to-gray-100/30 dark:from-[#0f0f0f] dark:to-[#0f0f0f] border-b border-gray-200/30 dark:border-gray-600/20 flex-shrink-0">
                  <div className="flex justify-center">
                    <div className="subscription-modal-switch">
                      {/* 滑动背景 */}
                      <div className={`subscription-modal-switch-slider ${activeTab === 'recharge' ? 'checked' : ''}`}></div>

                      {/* 订阅会员选项 */}
                      <div
                        className={`subscription-modal-switch-option subscription ${
                          activeTab === 'subscription' ? 'active' : 'inactive'
                        }`}
                        onClick={() => setActiveTab('subscription')}
                      >
                        <div className="subscription-modal-switch-indicator"></div>
                        <Crown className="w-4 h-4" />
                        <span>订阅会员</span>
                      </div>

                      {/* 充值积分选项 */}
                      <div
                        className={`subscription-modal-switch-option recharge ${
                          activeTab === 'recharge' ? 'active' : 'inactive'
                        }`}
                        onClick={() => setActiveTab('recharge')}
                      >
                        <div className="subscription-modal-switch-indicator"></div>
                        <CreditCard className="w-4 h-4" />
                        <span>充值积分</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 内容区域 - 放大布局 */}
                <div className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-gray-50/20 to-white dark:from-[#0f0f0f] dark:to-[#0f0f0f]">
                {activeTab === 'subscription' ? (
                  <div className="space-y-6">
                    {/* 订阅计划选择 */}
                    <div>
                      <div className="flex items-center gap-3 mb-5">
                        <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">选择会员类型</h3>
                      </div>
                      <div className="grid grid-cols-3 gap-5">
                        {subscriptionPlans.map((plan) => {
                          const isSelected = selectedPlanType === plan.plan_type;
                          const isPopular = plan.plan_type === 'pro';

                          // 🔧 优化：根据选择的时长获取对应的价格信息
                          const currentPlan = selectedDuration === 'monthly' ? plan.monthly : plan.yearly;
                          if (!currentPlan) return null; // 如果没有对应时长的计划，不显示

                          return (
                            <div
                              key={plan.plan_type}
                              onClick={() => setSelectedPlanType(plan.plan_type)}
                              className={`relative cursor-pointer rounded-xl p-4 transition-all duration-300 group min-h-[242px] ${
                                isSelected
                                  ? 'bg-white dark:bg-[#1a1a1a] border-2 border-blue-500 dark:border-blue-400 shadow-lg transform scale-[1.02] ring-2 ring-blue-100 dark:ring-blue-900/30'
                                  : 'bg-white/80 dark:bg-[#1a1a1a]/80 backdrop-blur-sm border-2 border-gray-200/50 dark:border-gray-600/50 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-md'
                              }`}
                            >
                              {/* 推荐标签 */}
                              {isPopular && (
                                <div className="absolute -top-2 -right-2 z-10">
                                  <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-md">
                                    🔥 推荐
                                  </div>
                                </div>
                              )}

                              {/* 卡片头部：套餐名 - 居中显示 */}
                              <div className="text-center mb-5">
                                <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                  {plan.plan_type === 'free' ? '免费版' : plan.plan_type === 'pro' ? 'Pro版' : 'Max版'}
                                </h4>
                              </div>

                              {/* 核心信息：价格 - 突出显示 */}
                              <div className="text-center mb-5">
                                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                                  ¥{currentPlan.discount_price}
                                </div>
                                {/* 智能显示：只有真正有优惠时才显示原价和优惠信息 */}
                                {Number(currentPlan.original_price) > Number(currentPlan.discount_price) && (
                                  <div className="flex items-center justify-center gap-2 mb-2">
                                    <span className="text-sm text-gray-400 dark:text-gray-500 line-through">
                                      原价 ¥{currentPlan.original_price}
                                    </span>
                                    <span className="text-xs bg-gradient-to-r from-red-500 to-orange-500 text-white px-2 py-1 rounded-full font-bold">
                                      省{Math.round(((Number(currentPlan.original_price) - Number(currentPlan.discount_price)) / Number(currentPlan.original_price)) * 100)}%
                                    </span>
                                  </div>
                                )}
                              </div>

                              {/* 积分权益信息 */}
                              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 border border-blue-100 dark:border-blue-800/30 text-center">
                                <div className="text-lg font-bold text-blue-600 dark:text-blue-400 mb-1">
                                  {plan.points_included.toLocaleString()} 积分
                                </div>
                                <div className="text-xs text-blue-500 dark:text-blue-300">
                                  有效期 {plan.points_validity_days} 天
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* 订阅时长选择 - 专业化设计 */}
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-1 h-6 bg-gradient-to-b from-emerald-500 to-teal-600 rounded-full"></div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">选择订阅时长</h3>
                      </div>

                      {/* 时长选择卡片 */}
                      <div className="grid grid-cols-2 gap-5 max-w-2xl">
                        {/* 月度订阅 */}
                        <div
                          onClick={() => setSelectedDuration('monthly')}
                          className={`relative cursor-pointer rounded-2xl p-5 transition-all duration-300 group ${
                            selectedDuration === 'monthly'
                              ? 'bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 border-2 border-blue-500 dark:border-blue-400 shadow-lg ring-2 ring-blue-100 dark:ring-blue-900/30'
                              : 'bg-white/80 dark:bg-[#1a1a1a]/80 backdrop-blur-sm border-2 border-gray-200/50 dark:border-gray-600/50 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <div className={`w-5 h-5 rounded-full border-2 transition-all duration-300 ${
                                selectedDuration === 'monthly'
                                  ? 'border-blue-500 bg-blue-500'
                                  : 'border-gray-300 dark:border-gray-600'
                              }`}>
                                {selectedDuration === 'monthly' && (
                                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                                )}
                              </div>
                              <span className="text-lg font-bold text-gray-900 dark:text-white">月度订阅</span>
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">灵活选择</div>
                          </div>
                          <div className="space-y-2">
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 按月付费，随时可取消
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 积分当月有效，用完即止
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 适合轻度使用用户
                            </div>
                          </div>
                        </div>

                        {/* 年度订阅 */}
                        <div
                          onClick={() => setSelectedDuration('yearly')}
                          className={`relative cursor-pointer rounded-2xl p-5 transition-all duration-300 group ${
                            selectedDuration === 'yearly'
                              ? 'bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30 border-2 border-purple-500 dark:border-purple-400 shadow-lg ring-2 ring-purple-100 dark:ring-purple-900/30'
                              : 'bg-white/80 dark:bg-[#1a1a1a]/80 backdrop-blur-sm border-2 border-gray-200/50 dark:border-gray-600/50 hover:border-purple-300 dark:hover:border-purple-500 hover:shadow-md'
                          }`}
                        >
                          {/* 优惠标签 */}
                          <div className="absolute -top-2 -right-2">
                            <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse">
                              🎉 超值优惠
                            </div>
                          </div>

                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <div className={`w-5 h-5 rounded-full border-2 transition-all duration-300 ${
                                selectedDuration === 'yearly'
                                  ? 'border-purple-500 bg-purple-500'
                                  : 'border-gray-300 dark:border-gray-600'
                              }`}>
                                {selectedDuration === 'yearly' && (
                                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                                )}
                              </div>
                              <span className="text-lg font-bold text-gray-900 dark:text-white">年度订阅</span>
                            </div>
                            <div className="text-sm font-bold text-green-600 dark:text-green-400">省更多</div>
                          </div>
                          <div className="space-y-2">
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 年付享受折扣优惠
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 积分按月发放，每月重置有效期
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              • 适合长期使用用户
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>


                  </div>
                ) : (
                  <div className="space-y-5">
                    <div className="flex items-center gap-3 mb-5">
                      <div className="w-1 h-6 bg-gradient-to-b from-emerald-500 to-teal-600 rounded-full"></div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">选择积分包</h3>
                    </div>

                    <div className="grid grid-cols-3 gap-5">
                      {pointsPackages.map((pkg) => {
                        const isSelected = selectedPackage === pkg.package_key;
                        const isPopular = pkg.package_key === 'standard_500';
                        const discountPercent = Number(pkg.original_price) > Number(pkg.discount_price) ?
                          Math.round(((Number(pkg.original_price) - Number(pkg.discount_price)) / Number(pkg.original_price)) * 100) : 0;

                        return (
                          <div
                            key={pkg.package_key}
                            onClick={() => setSelectedPackage(pkg.package_key)}
                            className={`relative cursor-pointer rounded-xl p-4 transition-all duration-300 group min-h-[242px] ${
                              isSelected
                                ? 'bg-white dark:bg-[#1a1a1a] border-2 border-emerald-500 dark:border-emerald-400 shadow-lg transform scale-[1.02] ring-2 ring-emerald-100 dark:ring-emerald-900/30'
                                : 'bg-white/80 dark:bg-[#1a1a1a]/80 backdrop-blur-sm border-2 border-gray-200/50 dark:border-gray-600/50 hover:border-emerald-300 dark:hover:border-emerald-500 hover:shadow-md'
                            }`}
                          >


                            {/* 热门标签 */}
                            {isPopular && (
                              <div className="absolute -top-1 -left-1 z-10">
                                <div className="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-md flex items-center gap-1">
                                  <Star className="w-2 h-2" />
                                  热门
                                </div>
                              </div>
                            )}

                            {/* 卡片头部：包名 - 居中显示 */}
                            <div className="text-center mb-5">
                              <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{pkg.package_name}</h4>
                            </div>

                            {/* 核心信息：价格 - 突出显示 */}
                            <div className="text-center mb-5">
                              <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                                ¥{Number(pkg.discount_price).toFixed(2)}
                              </div>
                              {/* 智能显示：只有真正有优惠时才显示原价和优惠信息 */}
                              {discountPercent > 0 && (
                                <div className="flex items-center justify-center gap-2 mb-2">
                                  <span className="text-sm text-gray-400 dark:text-gray-500 line-through">
                                    原价 ¥{Number(pkg.original_price).toFixed(2)}
                                  </span>
                                  <span className="text-xs bg-gradient-to-r from-red-500 to-orange-500 text-white px-2 py-1 rounded-full font-bold">
                                    省{discountPercent}%
                                  </span>
                                </div>
                              )}
                            </div>

                            {/* 积分权益信息 */}
                            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg p-3 border border-emerald-100 dark:border-emerald-800/30 text-center">
                              <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400 mb-1">
                                {pkg.points_amount.toLocaleString()} 积分
                                {pkg.bonus_points > 0 && (
                                  <span className="text-sm text-emerald-500 dark:text-emerald-300 ml-1">
                                    +{pkg.bonus_points}
                                  </span>
                                )}
                              </div>
                              <div className="text-xs text-emerald-500 dark:text-emerald-300">
                                永久有效
                              </div>
                            </div>


                          </div>
                        );
                      })}
                    </div>

                    {/* 购买数量选择 */}
                    <div className="mt-6">
                      <div className="flex items-center gap-3 mb-5">
                        <div className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full"></div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">购买数量</h3>
                        {getNumericQuantity() > 1 && (
                          <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-sm font-bold ml-auto">
                            🎉 优惠 {getNumericQuantity() >= 5 ? '10%' : getNumericQuantity() >= 3 ? '5%' : '2%'} OFF
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-center gap-3">
                        <button
                          onClick={() => setQuantity(Math.max(1, getNumericQuantity() - 1))}
                          className="w-12 h-12 rounded-xl border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 flex items-center justify-center hover:border-emerald-500 dark:hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-all duration-200 bg-white/80 dark:bg-[#1a1a1a]/80 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={getNumericQuantity() <= 1}
                        >
                          <Minus className="w-5 h-5" />
                        </button>

                        <div className="relative">
                          <input
                            type="number"
                            min="1"
                            max="999"
                            value={quantity}
                            onChange={(e) => {
                              // 在输入过程中允许空值和临时无效值，提升用户体验
                              const inputValue = e.target.value;
                              if (inputValue === '') {
                                // 允许用户清空输入框
                                setQuantity('');
                                return;
                              }
                              const numValue = parseInt(inputValue);
                              if (!isNaN(numValue)) {
                                // 只在有效数字时更新，但不限制范围（在输入过程中）
                                setQuantity(numValue);
                              }
                            }}
                            onBlur={(e) => {
                              // 失焦时才进行最终验证和修正
                              const inputValue = e.target.value;
                              if (inputValue === '' || isNaN(parseInt(inputValue))) {
                                setQuantity(1); // 空值或无效值默认为1
                              } else {
                                const numValue = parseInt(inputValue);
                                setQuantity(Math.max(1, Math.min(999, numValue))); // 限制在有效范围内
                              }
                            }}
                            onFocus={(e) => {
                              // 聚焦时选中所有文本，方便用户重新输入
                              e.target.select();
                            }}
                            className="quantity-input w-20 h-12 border-2 border-emerald-300 dark:border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-900 dark:text-emerald-100 rounded-xl text-center font-bold text-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
                          />
                        </div>

                        <button
                          onClick={() => setQuantity(Math.min(999, getNumericQuantity() + 1))}
                          className="w-12 h-12 rounded-xl border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 flex items-center justify-center hover:border-emerald-500 dark:hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-all duration-200 bg-white/80 dark:bg-[#1a1a1a]/80 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={getNumericQuantity() >= 999}
                        >
                          <Plus className="w-5 h-5" />
                        </button>
                      </div>



                      {/* 数量提示 */}
                      <div className="mt-4 text-center">
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          支持购买 1-999 个，可直接输入数量
                        </p>
                      </div>

                      {/* 批量优惠提示 */}
                      {getNumericQuantity() <= 2 && (
                        <div className="mt-4 bg-blue-50/50 dark:bg-blue-900/10 rounded-lg p-4 border border-blue-200/30 dark:border-blue-700/30">
                          <p className="text-sm text-blue-700 dark:text-blue-300 text-center">
                            💡 购买3个及以上享受<strong>5%折扣</strong>，5个及以上享受<strong>10%折扣</strong>
                          </p>
                        </div>
                      )}

                      {/* 当前优惠提示 */}
                      {getNumericQuantity() >= 3 && (
                        <div className="mt-4 bg-green-50/50 dark:bg-green-900/10 rounded-lg p-4 border border-green-200/30 dark:border-green-700/30">
                          <p className="text-sm text-green-700 dark:text-green-300 text-center">
                            🎉 当前享受 <strong>{getNumericQuantity() >= 5 ? '10%' : '5%'}</strong> 批量优惠！
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧订单信息 - 放大设计 */}
            <div className="w-96 bg-gray-50/80 dark:bg-[#0f0f0f] border-l border-gray-200/50 dark:border-gray-600/30 flex flex-col min-h-0">
              <div className="flex-1 overflow-y-auto">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-[#0f0f0f] flex-shrink-0">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white tracking-wide">
                    订单详情
                  </h3>
                </div>

                {activeTab === 'subscription' ? (
                  <div className="space-y-0">
                    {getCurrentPlan() && (
                      <>
                        {/* 商品明细 - 表格样式 */}
                        <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                            商品明细
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-medium text-gray-900 dark:text-white">
                                  {getCurrentPlan()?.plan_name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {selectedDuration === 'monthly' ? '月度订阅' : '年度订阅'}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-mono text-gray-900 dark:text-white">
                                  ¥{getCurrentPlanPricing()?.original_price}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  原价
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 积分权益 - 简洁列表 */}
                        <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                            积分权益
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">每月积分</span>
                              <span className="font-mono text-gray-900 dark:text-white">
                                {getCurrentPlan()?.points_included?.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">有效期</span>
                              <span className="font-mono text-gray-900 dark:text-white">
                                {getCurrentPlan()?.points_validity_days}天
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">发放方式</span>
                              <span className="font-mono text-gray-900 dark:text-white">
                                {selectedDuration === 'monthly' ? '立即到账' : '按月发放'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="space-y-0">
                    {getCurrentPackage() && (
                      <>
                        {/* 商品明细 - 表格样式 */}
                        <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                            商品明细
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-medium text-gray-900 dark:text-white">
                                  {getCurrentPackage()?.package_name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  积分套餐 × {getNumericQuantity()}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-mono text-gray-900 dark:text-white">
                                  ¥{Number(getCurrentPackage()?.original_price).toFixed(2)}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  单价
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 积分权益 - 简洁列表 */}
                        <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                            积分权益
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">获得积分</span>
                              <span className="font-mono font-semibold text-gray-900 dark:text-white">
                                {((getCurrentPackage()?.points_amount || 0) * getNumericQuantity()).toLocaleString()}
                              </span>
                            </div>
                            {getCurrentPackage()?.bonus_points && getCurrentPackage()!.bonus_points > 0 && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600 dark:text-gray-400">赠送积分</span>
                                <span className="font-mono text-emerald-600 dark:text-emerald-400">
                                  +{((getCurrentPackage()?.bonus_points || 0) * getNumericQuantity()).toLocaleString()}
                                </span>
                              </div>
                            )}
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 dark:text-gray-400">有效期</span>
                              <span className="font-mono text-gray-900 dark:text-white">永久有效</span>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )}

                {/* 价格明细 - PI单据样式 */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                    价格明细
                  </div>

                  <div className="space-y-2">
                    {activeTab === 'subscription' ? (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">商品原价</span>
                          <span className="font-mono text-gray-900 dark:text-white">¥{getOriginalTotal().toFixed(2)}</span>
                        </div>

                        {selectedDuration === 'yearly' && getCurrentPlanPricing()?.discount_percent && getCurrentPlanPricing()!.discount_percent > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">限时优惠 (-{getCurrentPlanPricing()?.discount_percent}%)</span>
                            <span className="font-mono text-emerald-600 dark:text-emerald-400">-¥{(getOriginalTotal() - calculateTotal()).toFixed(2)}</span>
                          </div>
                        )}

                        {selectedDuration === 'monthly' && getCurrentPlanPricing()?.discount_percent && getCurrentPlanPricing()!.discount_percent > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">限时优惠 (-{getCurrentPlanPricing()?.discount_percent}%)</span>
                            <span className="font-mono text-emerald-600 dark:text-emerald-400">-¥{(getOriginalTotal() - calculateTotal()).toFixed(2)}</span>
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">商品小计</span>
                          <span className="font-mono text-gray-900 dark:text-white">¥{(Number(getCurrentPackage()?.original_price || 0) * getNumericQuantity()).toFixed(2)}</span>
                        </div>

                        {Number(getCurrentPackage()?.original_price || 0) > Number(getCurrentPackage()?.discount_price || 0) && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">限时优惠 (-{Math.round(((Number(getCurrentPackage()?.original_price || 0) - Number(getCurrentPackage()?.discount_price || 0)) / Number(getCurrentPackage()?.original_price || 1)) * 100)}%)</span>
                            <span className="font-mono text-emerald-600 dark:text-emerald-400">-¥{((Number(getCurrentPackage()?.original_price || 0) - Number(getCurrentPackage()?.discount_price || 0)) * getNumericQuantity()).toFixed(2)}</span>
                          </div>
                        )}

                        {getBulkDiscountPercent() > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">批量优惠 (-{getBulkDiscountPercent()}%)</span>
                            <span className="font-mono text-emerald-600 dark:text-emerald-400">-¥{((Number(getCurrentPackage()?.discount_price || 0) * getNumericQuantity()) - calculateTotal()).toFixed(2)}</span>
                          </div>
                        )}
                      </>
                    )}

                    {/* 总计 - PI单据样式 */}
                    <div className="border-t border-gray-300 dark:border-gray-600 pt-3 mt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-base font-bold text-gray-900 dark:text-white uppercase tracking-wide">合计</span>
                        <div className="text-right">
                          <div className="text-2xl font-bold font-mono text-gray-900 dark:text-white">¥{calculateTotal().toFixed(2)}</div>
                          {getOriginalTotal() > calculateTotal() && (
                            <div className="text-sm text-gray-500 dark:text-gray-400 line-through font-mono">¥{getOriginalTotal().toFixed(2)}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-5 flex flex-col justify-end bg-white/90 dark:bg-[#0f0f0f]">
                {/* 支付区域 */}
                <div className="space-y-5">
                  {/* 支付按钮 - 动画效果 */}
                  <button
                    onClick={activeTab === 'subscription' ? () => handleSubscribe(getCurrentPlanPricing()?.plan_key || '') : () => handleRecharge(selectedPackage || '')}
                    className="animated-pay-btn w-full h-14 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl shadow-lg flex items-center justify-center gap-3 relative overflow-hidden cursor-pointer border-none transition-all duration-300"
                  >
                    <CreditCard className="w-5 h-5 z-10 relative" />
                    <span className="text-xl z-10 relative">立即支付 ¥{calculateTotal().toFixed(2)}</span>
                  </button>

                  {/* 支付方式提示 - 专业尺寸 */}
                  <div className="flex items-center justify-center gap-3">
                    <div className="flex items-center gap-1.5 bg-white/80 dark:bg-[#1a1a1a]/80 px-2.5 py-1.5 rounded border border-gray-200/50 dark:border-gray-600/50">
                      <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">支</span>
                      </div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">支付宝</span>
                    </div>
                    <div className="flex items-center gap-1.5 bg-white/80 dark:bg-[#1a1a1a]/80 px-2.5 py-1.5 rounded border border-gray-200/50 dark:border-gray-600/50">
                      <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">微</span>
                      </div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">微信支付</span>
                    </div>
                  </div>

                  {/* 协议提示 - 专业尺寸 */}
                  <div className="text-center bg-gray-50/60 dark:bg-[#1a1a1a]/30 rounded p-2.5 border border-gray-200/20 dark:border-gray-600/20">
                    <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
                      点击支付即表示同意
                      <span className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline">用户协议</span>
                      和
                      <span className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline">隐私政策</span>
                    </p>
                  </div>
                </div>
              </div>
              </div>
            </div>
            </div>
          </div>
        )}
      </div>

    </div>
  );
}