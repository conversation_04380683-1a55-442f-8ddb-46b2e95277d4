import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getUserByToken } from '@/lib/auth-service';
import { createSimpleProject } from '@/lib/auth-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 开始从 HTML 创建项目...');
    
    // 验证用户身份
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      console.log('❌ 未授权: 没有token');
      return NextResponse.json({ message: "请先登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      console.log('❌ 未授权: 无效token');
      return NextResponse.json({ message: "请先登录" }, { status: 401 });
    }

    // 解析请求数据
    const { htmlContent, figmaInfo } = await request.json();
    
    if (!htmlContent || typeof htmlContent !== 'string') {
      console.error('❌ 无效的 HTML 内容');
      return NextResponse.json(
        { 
          success: false,
          error: '请提供有效的 HTML 内容' 
        },
        { status: 400 }
      );
    }

    console.log('📝 HTML 内容长度:', htmlContent.length);
    console.log('📊 Figma 信息:', figmaInfo);

    // 构建项目标题和提示词
    const projectTitle = figmaInfo?.fileName || 'Figma 导入项目';
    const promptText = `从 Figma 导入: ${projectTitle}${figmaInfo?.originalUrl ? ` (${figmaInfo.originalUrl})` : ''}`;

    console.log('🏗️ 创建项目:', { projectTitle, promptText });

    // 创建项目
    const project = await createSimpleProject(user.id, htmlContent, [promptText]);
    
    if (!project) {
      console.log('❌ 项目创建失败');
      return NextResponse.json(
        { 
          success: false,
          error: "项目创建失败，请重试" 
        },
        { status: 500 }
      );
    }

    console.log('✅ 项目创建成功:', project.id);

    // 返回成功结果
    return NextResponse.json(
      {
        success: true,
        project: {
          id: project.id,
          title: project.title,
          html_content: project.html_content,
          created_at: project.created_at,
          updated_at: project.updated_at
        },
        message: '项目创建成功'
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('❌ 项目创建失败:', error);
    
    let errorMessage = '项目创建失败，请重试';
    if (error instanceof Error) {
      if (error.message.includes('Unauthorized')) {
        errorMessage = '请先登录再创建项目';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
} 