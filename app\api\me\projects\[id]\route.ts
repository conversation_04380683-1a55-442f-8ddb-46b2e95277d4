import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { 
  getUserByToken, 
  getProject, 
  updateProject,
  getProjectVersions,
  getProjectChatHistory,
  switchToProjectVersion
} from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";
import { initDatabase } from "@/lib/mysql";
import mysql from "mysql2/promise";

// 初始化数据库
initDatabase().catch(console.error);

// 获取单个项目详情（包括版本和聊天历史）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // 获取项目基本信息
    const project = await getProject(projectId, user.id);
    if (!project) {
      return NextResponse.json({ message: "Project not found" }, { status: 404 });
    }

    // 获取项目版本
    const versions = await getProjectVersions(projectId, user.id);
    
    // 获取聊天历史
    const chatHistory = await getProjectChatHistory(projectId, user.id);

    return NextResponse.json({
      ok: true,
      project,
      versions,
      chatHistory
    });
  } catch (error) {
    console.error("获取项目详情失败:", error);
    return NextResponse.json(
      { ok: false, error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 更新项目
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { html_content, prompts, title } = await request.json();
    
    // 支持单独更新标题
    if (title !== undefined && (html_content === undefined && prompts === undefined)) {
      // 只更新标题
      const result = await executeQuery(
        'UPDATE projects SET title = ?, updated_at = NOW() WHERE id = ? AND user_id = ?',
        [title, projectId, user.id]
      ) as { affectedRows: number };
      
      if (result.affectedRows > 0) {
        return NextResponse.json({ ok: true, message: "Project title updated successfully" });
      } else {
        return NextResponse.json(
          { ok: false, message: "Failed to update project title" },
          { status: 500 }
        );
      }
    }
    
    // 更新完整项目内容
    if (!html_content) {
      return NextResponse.json(
        { message: "HTML content is required" },
        { status: 400 }
      );
    }

    const success = await updateProject(projectId, user.id, html_content, prompts || []);
    
    if (success) {
      return NextResponse.json({ ok: true, message: "Project updated successfully" });
    } else {
      return NextResponse.json(
        { ok: false, message: "Failed to update project" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("更新项目失败:", error);
    return NextResponse.json(
      { ok: false, error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 切换项目版本
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // 检查是否是手动编辑保存
    if (body.action === 'manual_edit' && body.html_content) {
      console.log('🔧 手动编辑保存:', {
        projectId,
        userId: user.id,
        htmlLength: body.html_content.length,
        targetVersionNumber: body.version_number
      });

      // 🎯 关键修复：根据指定版本号获取版本，而不是活跃版本
      let targetVersionResult;
      if (body.version_number) {
        // 如果指定了版本号，获取该版本
        targetVersionResult = await executeQuery(
          `SELECT id, version_number FROM project_versions
           WHERE project_id = ? AND version_number = ? AND user_id = ?
           ORDER BY version_number DESC LIMIT 1`,
          [projectId, body.version_number, user.id]
        ) as { id: number; version_number: number }[];
      } else {
        // 兜底：如果没有指定版本号，使用活跃版本
        targetVersionResult = await executeQuery(
          `SELECT id, version_number FROM project_versions
           WHERE project_id = ? AND is_active = true
           ORDER BY version_number DESC LIMIT 1`,
          [projectId]
        ) as { id: number; version_number: number }[];
      }

      if (targetVersionResult.length === 0) {
        return NextResponse.json(
          { message: body.version_number ? `Version ${body.version_number} not found` : "No active version found" },
          { status: 404 }
        );
      }

      const targetVersion = targetVersionResult[0];

      // 🎯 关键修复：更新指定版本的HTML内容
      const updateResult = await executeQuery(
        `UPDATE project_versions
         SET html_content = ?
         WHERE id = ? AND project_id = ?`,
        [body.html_content, targetVersion.id, projectId]
      ) as { affectedRows: number };

      if (updateResult.affectedRows > 0) {
        // 🎯 关键修复：检查目标版本是否为活跃版本，如果是则同步更新项目表
        // 这样可以保持项目表的html_content始终是活跃版本的内容
        const isActiveVersionResult = await executeQuery(
          `SELECT is_active FROM project_versions
           WHERE id = ? AND project_id = ?`,
          [targetVersion.id, projectId]
        ) as { is_active: boolean }[];

        if (isActiveVersionResult.length > 0 && isActiveVersionResult[0].is_active) {
          await executeQuery(
            `UPDATE projects
             SET html_content = ?, updated_at = NOW()
             WHERE id = ? AND user_id = ?`,
            [body.html_content, projectId, user.id]
          );
          console.log('✅ 同步更新了项目表内容（活跃版本）');
        } else {
          console.log('ℹ️ 编辑的是历史版本，不更新项目表内容');
        }

        // 🔧 关键修复：同步更新聊天历史中对应版本的AI消息HTML内容
        const chatUpdateResult = await executeQuery(
          `UPDATE chat_history
           SET html_content = ?
           WHERE project_id = ? AND user_id = ? AND version_id = ? AND message_type = 'ai'`,
          [body.html_content, projectId, user.id, targetVersion.id]
        ) as { affectedRows: number };

        console.log('✅ 手动编辑保存成功:', {
          versionId: targetVersion.id,
          versionNumber: targetVersion.version_number,
          chatHistoryUpdated: chatUpdateResult.affectedRows > 0,
          chatRowsUpdated: chatUpdateResult.affectedRows
        });

        return NextResponse.json({
          ok: true,
          message: "Manual edit saved successfully",
          version: {
            id: targetVersion.id,
            version_number: targetVersion.version_number
          }
        });
      } else {
        return NextResponse.json(
          { ok: false, message: "Failed to save manual edit" },
          { status: 500 }
        );
      }
    }

    // 原有的版本切换逻辑
    const { versionId } = body;
    
    if (!versionId) {
      return NextResponse.json(
        { message: "Version ID is required for version switching" },
        { status: 400 }
      );
    }

    const success = await switchToProjectVersion(projectId, user.id, versionId);
    
    if (success) {
      return NextResponse.json({ ok: true, message: "Version switched successfully" });
    } else {
      return NextResponse.json(
        { ok: false, message: "Failed to switch version" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("PATCH操作失败:", error);
    return NextResponse.json(
      { ok: false, error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 删除项目 - 优化版本：事务处理 + 精准删除 + 性能监控
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = performance.now();
  
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = parseInt(id);

    if (isNaN(projectId) || projectId <= 0) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    // 🔍 验证项目存在性和所有权（快速查询，无锁定）
    const projectResult = await executeQuery(
      'SELECT id, title FROM projects WHERE id = ? AND user_id = ?',
      [projectId, user.id]
    ) as mysql.RowDataPacket[];

    if (projectResult.length === 0) {
      return NextResponse.json(
        { error: "Project not found or access denied", ok: false },
        { status: 404 }
      );
    }

    const project = projectResult[0];
    
    // 🗑️ 高效删除 - 利用外键级联删除（无需事务）
    const deleteResult = await executeQuery(
      'DELETE FROM projects WHERE id = ? AND user_id = ?',
      [projectId, user.id]
    ) as mysql.ResultSetHeader;

    // ✅ 验证删除结果
    if (deleteResult.affectedRows === 0) {
      return NextResponse.json(
        { error: "Failed to delete project", ok: false },
        { status: 500 }
      );
    }
    
    const duration = performance.now() - startTime;
    
    // 📊 性能日志
    console.log(`✅ 项目删除成功: ${project.title} (ID:${projectId}) - ${duration.toFixed(1)}ms`, {
      projectId,
      userId: user.id,
      duration: `${duration.toFixed(1)}ms`
    });

    return NextResponse.json({ 
      ok: true,
      deleted: {
        project: 1
      }
    }, { status: 200 });

  } catch (error) {
    const duration = performance.now() - startTime;
    
    console.error(`❌ 项目删除失败 (${duration.toFixed(1)}ms):`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      projectId: params ? (await params).id : 'unknown',
      userId: 'unknown'
    });

    // 🔧 错误分类处理
    if (error instanceof Error) {
      if (error.message.includes('ECONNRESET') || error.message.includes('PROTOCOL_CONNECTION_LOST')) {
        return NextResponse.json(
          { error: "数据库连接异常，请重试", ok: false },
          { status: 503 }
        );
      }
      
      if (error.message.includes('ER_LOCK_WAIT_TIMEOUT')) {
        return NextResponse.json(
          { error: "操作超时，请重试", ok: false },
          { status: 408 }
        );
      }
    }

    return NextResponse.json(
      { error: "服务器内部错误", ok: false },
      { status: 500 }
    );
  }
} 