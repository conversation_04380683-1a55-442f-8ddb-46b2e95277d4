# LoomRun 邀请系统实现文档

## 系统概述

LoomRun 邀请系统是一个完整的用户邀请奖励机制，支持用户通过专属邀请链接邀请新用户注册，并获得积分奖励。

## 核心功能

### 1. 邀请链接生成
- 每个用户拥有唯一的邀请码
- 可生成专属邀请链接：`https://loomrun.top?invite=ABCD1234`
- 支持邀请次数限制

### 2. 邀请奖励机制
- 新用户通过邀请链接注册成功后，邀请者获得积分奖励
- 积分类型：活动积分（有效期可配置）
- 奖励金额可通过系统设置调整

### 3. 公告横幅系统
- 顶部显示邀请活动公告
- 可通过系统设置控制显示/隐藏
- 点击可展开详细邀请界面

## 数据库结构

### 核心表结构

#### users 表新增字段
```sql
- invite_code: varchar(20) - 用户邀请码
- invited_by_user_id: int - 邀请者用户ID
- invitation_count: int - 邀请成功次数
```

#### user_invitations 表
```sql
- id: 邀请记录ID
- inviter_user_id: 邀请者用户ID
- invited_user_id: 被邀请者用户ID
- invite_code: 使用的邀请码
- invitation_status: 邀请状态 (pending/registered/expired)
- points_awarded: 奖励积分数量
- registered_at: 注册时间
```

#### system_settings 表相关配置
```sql
- invitation_enabled: 邀请功能开关
- invitation_points_per_user: 每邀请一个用户获得的积分
- max_invitations_per_user: 每个用户最多可邀请的用户数量
- show_invitation_banner: 显示邀请活动横幅
```

## API 接口

### 邀请相关接口

#### 1. 获取邀请统计
```
GET /api/me/invitation/stats
```

#### 2. 生成邀请链接
```
POST /api/me/invitation/generate-link
```

#### 3. 验证邀请码
```
POST /api/invitation/validate
Body: { "inviteCode": "ABCD1234" }
```

#### 4. 获取公告设置
```
GET /api/system/announcement-settings
```

### 登录接口更新
登录接口现在支持邀请码参数：
```
POST /api/auth/login
Body: { 
  "type": "phone", 
  "phone": "13800138000", 
  "code": "123456",
  "inviteCode": "ABCD1234"  // 可选
}
```

## 前端组件

### 1. AnnouncementBanner 组件
- 位置：页面顶部
- 功能：显示邀请活动信息，提供参与入口
- 路径：`components/announcement-banner/index.tsx`

### 2. useInviteCode Hook
- 功能：处理URL中的邀请码参数
- 自动验证邀请码有效性
- 路径：`loomrunhooks/useInviteCode.ts`

### 3. AuthModal 更新
- 支持显示邀请码状态
- 登录时自动包含邀请码参数

## 业务流程

### 邀请流程
1. 用户A点击公告横幅或邀请按钮
2. 系统生成用户A的专属邀请链接
3. 用户A分享邀请链接给用户B
4. 用户B通过邀请链接访问网站
5. 系统自动识别并验证邀请码
6. 用户B注册成功后，系统自动发放积分给用户A

### 积分发放流程
1. 验证邀请码有效性
2. 检查邀请者邀请次数限制
3. 创建邀请记录
4. 计算积分有效期
5. 创建积分余额记录
6. 创建积分交易记录
7. 更新用户积分和邀请计数

## 系统配置

### 关键配置项
```sql
-- 邀请功能开关
invitation_enabled = 1

-- 每邀请一个用户获得的积分
invitation_points_per_user = 100

-- 每个用户最多可邀请的用户数量
max_invitations_per_user = 10

-- 显示邀请活动横幅
show_invitation_banner = 1

-- 活动积分有效期天数
activity_points_validity_days = 15
```

## 测试接口

### 邀请系统测试
```
GET /api/test/invitation-system
```
该接口会测试所有邀请系统功能并返回详细结果。

## 部署说明

### 1. 数据库更新
执行以下SQL脚本：
- `scripts/add_invitation_system.sql`
- `scripts/add_invitation_banner_settings.sql`

### 2. 环境变量
确保设置正确的 `NEXT_PUBLIC_BASE_URL` 用于生成邀请链接。

### 3. 功能验证
1. 访问测试接口验证系统功能
2. 检查公告横幅显示
3. 测试完整的邀请注册流程

## 注意事项

1. **安全性**：邀请码验证包含防重复邀请机制
2. **性能**：使用数据库事务确保数据一致性
3. **用户体验**：邀请状态实时反馈，错误处理完善
4. **扩展性**：系统设计支持未来功能扩展

## 监控和日志

系统会记录以下关键事件：
- 邀请链接生成
- 邀请码验证
- 邀请注册成功
- 积分发放结果

所有操作都有详细的控制台日志输出，便于调试和监控。
