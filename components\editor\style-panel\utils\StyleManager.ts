import { TailwindCleaner } from './TailwindCleaner';
import { ColorUtils } from './ColorUtils';

// 样式管理工具类 - 保持HTML结构清洁，防止Tailwind污染
export class StyleManager {
  private static customStylesMap = new Map<string, Record<string, string>>();
  private static styleCounter = 0;

  // 生成唯一的样式类名
  static generateStyleClass(elementId?: string): string {
    this.styleCounter++;
    return elementId ? `custom-${elementId}-${this.styleCounter}` : `custom-style-${this.styleCounter}`;
  }

  // 提取元素的自定义样式
  static extractCustomStyles(element: HTMLElement): Record<string, string> {
    const customStyles: Record<string, string> = {};
    const inlineStyle = element.style;
    
    // 常用的可提取样式属性
    const extractableProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity', 'transform', 'box-shadow'
    ];

    extractableProps.forEach(prop => {
      const value = inlineStyle.getPropertyValue(prop);
      if (value && value.trim()) {
        customStyles[prop] = value;
      }
    });

    return customStyles;
  }

  // 清理元素的内联样式（公共方法）
  static cleanInlineStyles(element: HTMLElement, propsToClean: string[]): void {
    propsToClean.forEach(prop => {
      element.style.removeProperty(prop);
    });
    console.log(`✅ StyleManager: 已清理内联样式`, { 
      element: element.tagName, 
      cleanedProps: propsToClean 
    });
  }

  // 生成CSS规则字符串
  static generateCSSRule(className: string, styles: Record<string, string>): string {
    const cssProps = Object.entries(styles)
      .map(([prop, value]) => `  ${prop}: ${value};`)
      .join('\n');
    
    return `.${className} {\n${cssProps}\n}`;
  }

  // 获取或创建style标签（支持iframe）- 修复版本
  static getOrCreateStyleTag(): HTMLStyleElement {
    // 优先在iframe中查找和创建style标签
    const iframe = document.querySelector('iframe');
    const targetDoc = (iframe && iframe.contentDocument) ? iframe.contentDocument : document;

    // 🔧 修复：验证文档完整性
    if (!targetDoc || !targetDoc.documentElement) {
      console.warn('⚠️ 目标文档不完整，使用主文档');
      return this.createStyleTagInDocument(document);
    }

    let styleTag = targetDoc.getElementById('loomrun-custom-styles') as HTMLStyleElement;

    if (!styleTag) {
      return this.createStyleTagInDocument(targetDoc);
    }

    return styleTag;
  }

  // 🔧 新增：在指定文档中安全地创建样式标签
  private static createStyleTagInDocument(targetDoc: Document): HTMLStyleElement {
    try {
      const styleTag = targetDoc.createElement('style');
      styleTag.id = 'loomrun-custom-styles';
      styleTag.type = 'text/css';

      // 🔧 安全地确保head存在
      if (!targetDoc.head) {
        const head = targetDoc.createElement('head');

        // 🔧 安全地插入head
        if (targetDoc.documentElement) {
          targetDoc.documentElement.insertBefore(head, targetDoc.body);
        } else {
          // 如果documentElement不存在，创建基本HTML结构
          console.warn('⚠️ 文档结构不完整，创建基本HTML结构');
          const html = targetDoc.createElement('html');
          const body = targetDoc.createElement('body');
          html.appendChild(head);
          html.appendChild(body);
          targetDoc.appendChild(html);
        }
      }

      targetDoc.head.appendChild(styleTag);
      console.log('✅ 在目标文档中创建了样式标签', {
        isIframe: targetDoc !== document,
        documentTitle: targetDoc.title || 'untitled',
        hasDocumentElement: !!targetDoc.documentElement,
        hasHead: !!targetDoc.head
      });

      return styleTag;
    } catch (error) {
      console.error('❌ 创建样式标签失败，回退到主文档:', error);
      // 回退到主文档
      if (targetDoc !== document) {
        return this.createStyleTagInDocument(document);
      }
      throw error;
    }
  }

  // 🔧 优化：将CSS属性转换为TailwindCSS类（使用ColorUtils）
  static convertToTailwindClass(property: string, value: string): string | null {
    // 🔧 优先使用ColorUtils处理颜色属性
    const colorClass = ColorUtils.convertColorToTailwindClass(property, value);
    if (colorClass) {
      return colorClass;
    }

    // 🔧 处理非颜色属性：清理空格
    const cleanValue = value.replace(/\s+/g, '');

    // 尺寸属性转换
    if (property === 'width') {
      return `w-[${cleanValue}]`;
    }
    if (property === 'height') {
      return `h-[${cleanValue}]`;
    }
    if (property === 'padding') {
      return `p-[${cleanValue}]`;
    }
    if (property === 'margin') {
      return `m-[${cleanValue}]`;
    }

    // 字体属性转换
    if (property === 'font-size' || property === 'fontSize') {
      return `text-[${cleanValue}]`;
    }
    if (property === 'font-weight' || property === 'fontWeight') {
      return `font-[${cleanValue}]`;
    }

    // 边框属性转换
    if (property === 'border-radius' || property === 'borderRadius') {
      return `rounded-[${cleanValue}]`;
    }
    if (property === 'border-width' || property === 'borderWidth') {
      return `border-[${cleanValue}]`;
    }

    // 其他常用属性
    if (property === 'opacity') {
      return `opacity-[${cleanValue}]`;
    }

    return null; // 不支持的属性返回null，使用CSS类
  }

  // 🔧 增强：优先使用TailwindCSS类，回退到CSS类
  static applyStylesToCSS(element: HTMLElement, styles: Record<string, string>): string {
    console.log('🎨 StyleManager: 开始应用样式，优先使用TailwindCSS类', { styles });

    const tailwindClasses: string[] = [];
    const cssStyles: Record<string, string> = {};

    // 分离可以转换为TailwindCSS类的属性和需要CSS的属性
    Object.entries(styles).forEach(([property, value]) => {
      const tailwindClass = this.convertToTailwindClass(property, value);
      if (tailwindClass) {
        tailwindClasses.push(tailwindClass);
        console.log(`🎯 转换为TailwindCSS类: ${property}: ${value} → ${tailwindClass}`);
      } else {
        cssStyles[property] = value;
        console.log(`📝 使用CSS样式: ${property}: ${value}`);
      }
    });

    // 🔧 修正：移除冲突的TailwindCSS类（使用新的方法名）
    Object.keys(styles).forEach(property => {
      this.removeConflictingTailwindClasses(element, property, styles[property]);
    });

    // 添加新的TailwindCSS类
    if (tailwindClasses.length > 0) {
      tailwindClasses.forEach(cls => {
        if (!element.classList.contains(cls)) {
          element.classList.add(cls);
        }
      });
      console.log(`✅ 已添加TailwindCSS类: ${tailwindClasses.join(' ')}`);
    }

    // 处理剩余的CSS样式
    let customClass = '';
    if (Object.keys(cssStyles).length > 0) {
      // 检查元素是否已有自定义类
      customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-')) || '';

      // 如果没有自定义类，创建一个
      if (!customClass) {
        customClass = this.generateStyleClass(element.id);
        element.classList.add(customClass);
      }

      // 存储样式映射
      this.customStylesMap.set(customClass, cssStyles);

      // 获取style标签
      const styleTag = this.getOrCreateStyleTag();

      // 生成CSS规则
      const cssRule = this.generateCSSRule(customClass, cssStyles);

      // 更新style标签内容
      this.updateStyleTagContent(styleTag, customClass, cssRule);

      console.log(`✅ CSS样式已应用到类 ${customClass}`, { cssStyles });
    }

    // 🔧 修复：立即清理内联样式，避免重复
    // 使用requestAnimationFrame确保DOM更新后执行
    requestAnimationFrame(() => {
      this.cleanInlineStyles(element, Object.keys(styles));
      console.log('🧹 内联样式已清理，只保留TailwindCSS类和CSS类', {
        element: element.tagName,
        tailwindClasses,
        customClass,
        remainingInlineStyles: element.style.cssText
      });
    });

    console.log(`✅ StyleManager: 样式应用完成`, {
      tailwindClasses: tailwindClasses.length,
      cssStyles: Object.keys(cssStyles).length,
      totalClasses: element.className
    });

    return customClass || tailwindClasses[0] || '';
  }

  // 🔧 增强：智能移除冲突的TailwindCSS类
  static removeConflictingTailwindClasses(element: HTMLElement, property: string, newValue: string): void {
    const classesToRemove: string[] = [];

    // 根据属性类型移除所有相关的类（包括标准类和动态类）
    element.classList.forEach(cls => {
      switch (property) {
        case 'background-color':
        case 'backgroundColor':
          // 移除所有背景色相关类
          if (cls.startsWith('bg-[') ||
              cls.startsWith('bg-') && !cls.includes('gradient') && !cls.includes('opacity')) {
            classesToRemove.push(cls);
          }
          break;

        case 'color':
          // 移除所有文字颜色相关类
          if (cls.startsWith('text-[') ||
              (cls.startsWith('text-') && !cls.includes('align') && !cls.includes('decoration') &&
               !cls.includes('transform') && !cls.includes('overflow') && !cls.includes('wrap'))) {
            // 排除非颜色的text-类，如text-center, text-lg等
            if (cls.match(/^text-(red|blue|green|yellow|purple|pink|indigo|gray|black|white|orange|teal|cyan|lime|emerald|sky|violet|fuchsia|rose)-\d+$/) ||
                cls.startsWith('text-[#') || cls.startsWith('text-[rgb') || cls.startsWith('text-[hsl')) {
              classesToRemove.push(cls);
            }
          }
          break;

        case 'font-size':
        case 'fontSize':
          // 移除所有字体大小相关类
          if (cls.startsWith('text-[') && (cls.includes('px') || cls.includes('rem') || cls.includes('em')) ||
              cls.match(/^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)$/)) {
            classesToRemove.push(cls);
          }
          break;

        case 'font-weight':
        case 'fontWeight':
          // 移除所有字体粗细相关类
          if (cls.startsWith('font-[') ||
              cls.match(/^font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)$/)) {
            classesToRemove.push(cls);
          }
          break;

        case 'width':
          // 移除所有宽度相关类
          if (cls.startsWith('w-[') || cls.startsWith('w-')) {
            classesToRemove.push(cls);
          }
          break;

        case 'height':
          // 移除所有高度相关类
          if (cls.startsWith('h-[') || cls.startsWith('h-')) {
            classesToRemove.push(cls);
          }
          break;

        case 'border-radius':
        case 'borderRadius':
          // 移除所有圆角相关类
          if (cls.startsWith('rounded-[') || cls.startsWith('rounded')) {
            classesToRemove.push(cls);
          }
          break;
      }
    });

    // 批量移除冲突的类
    classesToRemove.forEach(cls => {
      element.classList.remove(cls);
    });

    if (classesToRemove.length > 0) {
      console.log(`🗑️ 已移除冲突的TailwindCSS类 (${property}): ${classesToRemove.join(' ')}`);
    }
  }

  // 🔧 向后兼容：旧方法名的别名
  static removeOldTailwindClasses(element: HTMLElement, properties: string[]): void {
    console.warn('⚠️ removeOldTailwindClasses 已废弃，请使用 removeConflictingTailwindClasses');
    properties.forEach(property => {
      // 对于旧的调用方式，我们需要从元素的样式中获取值
      const value = element.style.getPropertyValue(property) || '';
      this.removeConflictingTailwindClasses(element, property, value);
    });
  }

  // 更新style标签内容
  private static updateStyleTagContent(styleTag: HTMLStyleElement, className: string, newRule: string): void {
    let content = styleTag.textContent || '';
    
    // 移除旧的规则（如果存在）
    const oldRuleRegex = new RegExp(`\\.${className}\\s*\\{[^}]*\\}`, 'g');
    content = content.replace(oldRuleRegex, '');
    
    // 添加新规则
    content += '\n' + newRule;
    
    // 清理多余的空行
    content = content.replace(/\n\s*\n/g, '\n').trim();
    
    styleTag.textContent = content;
  }

  // 重置元素样式
  static resetElementStyles(element: HTMLElement): void {
    // 找到自定义类
    const customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-'));
    
    if (customClass) {
      // 从元素移除自定义类
      element.classList.remove(customClass);
      
      // 从映射中移除
      this.customStylesMap.delete(customClass);
      
      // 从CSS中移除规则
      const styleTag = this.getOrCreateStyleTag();
      let content = styleTag.textContent || '';
      const ruleRegex = new RegExp(`\\.${customClass}\\s*\\{[^}]*\\}`, 'g');
      content = content.replace(ruleRegex, '');
      styleTag.textContent = content.trim();
    }
    
    // 清理任何剩余的内联样式
    const commonProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity'
    ];
    
    this.cleanInlineStyles(element, commonProps);
  }

  // 获取元素的有效样式（包括CSS类和内联样式）
  static getEffectiveStyles(element: HTMLElement): Record<string, string> {
    const computedStyles = window.getComputedStyle(element);
    const inlineStyles = element.style;
    const customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-'));
    
    const effectiveStyles: Record<string, string> = {};
    const commonProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity'
    ];

    commonProps.forEach(prop => {
      // 优先级：内联样式 > 自定义CSS类 > 计算样式
      let value = inlineStyles.getPropertyValue(prop);
      
      if (!value && customClass) {
        const customStyles = this.customStylesMap.get(customClass);
        if (customStyles && customStyles[prop]) {
          value = customStyles[prop];
        }
      }
      
      if (!value) {
        value = computedStyles.getPropertyValue(prop);
      }
      
      if (value && value.trim() && !this.isDefaultValue(prop, value)) {
        effectiveStyles[prop] = value;
      }
    });

    return effectiveStyles;
  }

  // 检查是否为默认值
  private static isDefaultValue(prop: string, value: string): boolean {
    const defaultValues = {
      'opacity': '1',
      'font-weight': '400',
      'color': 'rgba(0, 0, 0, 0)',
      'background-color': 'rgba(0, 0, 0, 0)',
      'padding': '0px',
      'margin': '0px',
      'border': 'none',
      'border-radius': '0px'
    };

    return value === 'auto' || 
           value === 'initial' || 
           value === 'normal' || 
           value === 'none' ||
           value === 'transparent' ||
           value === defaultValues[prop as keyof typeof defaultValues];
  }

  // 导出当前的自定义样式为CSS字符串
  static exportCustomCSS(): string {
    const styleTag = this.getOrCreateStyleTag();
    return styleTag.textContent || '';
  }

  // 清理所有自定义样式
  static clearAllCustomStyles(): void {
    this.customStylesMap.clear();
    const styleTag = document.getElementById('loomrun-custom-styles');
    if (styleTag) {
      styleTag.textContent = '';
    }
  }

  // 清理HTML中的Tailwind重复内容 - 新增方法
  static cleanTailwindDuplicates(html: string): string {
    console.log('🧹 StyleManager: 开始清理Tailwind重复内容...');
    let cleanedHTML = TailwindCleaner.cleanHTML(html);

    // 🔧 强制确保关键标签不被压缩（最终保障）
    cleanedHTML = cleanedHTML
      .replace(/(<\/body>)(<\/html>)/g, '$1\n$2')
      .replace(/(<\/(?:head|title|style|script)>)(<)/g, '$1\n$2')
      // 🔧 强制修复：确保 </html> 前面没有多余空格
      .replace(/\s+(<\/html>)/g, '\n$1');

    return cleanedHTML;
  }

  // 实时清理Tailwind重复内容 - 用于样式编辑过程中
  static realtimeCleanTailwind(html: string): string {
    return TailwindCleaner.realtimeClean(html);
  }

  // 确保HTML包含正确的Tailwind支持
  static ensureTailwindSupport(html: string): string {
    return TailwindCleaner.ensureSingleTailwindCDN(html);
  }

  // 🔧 新增：检测并修复错误的TailwindCSS引入方式
  static fixTailwindUsage(html: string): {
    html: string;
    wasFixed: boolean;
    message: string;
  } {
    const result = TailwindCleaner.fixTailwindUsage(html);

    if (result.wasFixed) {
      console.log('🔧 StyleManager: TailwindCSS引入方式已修复', {
        hasLinkTag: result.hasLinkTag,
        hasScriptTag: result.hasScriptTag,
        message: result.message
      });
    }

    return {
      html: result.html,
      wasFixed: result.wasFixed,
      message: result.message
    };
  }

  // 🔧 新增：检测TailwindCSS引入问题
  static detectTailwindIssues(html: string): {
    hasIssues: boolean;
    issues: string[];
    recommendation: string;
  } {
    const detection = TailwindCleaner.detectTailwindUsage(html);

    return {
      hasIssues: detection.issues.length > 0,
      issues: detection.issues,
      recommendation: detection.recommendation
    };
  }
} 