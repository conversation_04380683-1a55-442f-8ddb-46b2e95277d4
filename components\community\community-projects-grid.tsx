"use client";
import { useState, useEffect, useCallback, useMemo, memo } from "react";
import { Button } from "@/components/ui/button";
import { CommunityCard } from "./community-card";
import { Loader2, RefreshCw } from "lucide-react";
import { useFavorites } from "@/loomrunhooks/useFavorites";
import { useScrollOptimization } from "@/loomrunhooks/useScrollOptimization";
import { useUser } from "@/loomrunhooks/useUser";
import { GhostLogo } from "@/components/ui/ghost-logo";

interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount?: number;
  isFavorited?: boolean;
}

interface CommunityProjectsGridProps {
  onPreview: (project: CommunityProject) => void;
  onOpen: (project: CommunityProject) => void;
  onOpenCommunityPage?: () => void;
  onOpenContactModal?: () => void;
}

// 配置常量 - 优化加载性能
const ITEMS_PER_ROW = 3;
const ROWS_PER_LOAD = 6; // 减少到6行，降低初始加载时间
const ITEMS_PER_LOAD = ITEMS_PER_ROW * ROWS_PER_LOAD; // 18个项目

// 🚀 高性能缓存实现
class CommunityCache {
  private static cache = new Map<string, { data: any; expiry: number; etag?: string }>();
  private static readonly TTL = 5 * 60 * 1000; // 5分钟缓存

  static set(key: string, data: any, etag?: string): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.TTL,
      etag
    });
  }

  static get(key: string): { data: any; etag?: string } | null {
    const item = this.cache.get(key);
    if (!item || Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    return { data: item.data, etag: item.etag };
  }

  static clear(): void {
    this.cache.clear();
  }

  static getStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

// 🎯 优化的网络请求函数
const fetchCommunityProjects = async (
  offset = 0, 
  limit = ITEMS_PER_LOAD,
  useCache = true
): Promise<{
  projects: CommunityProject[];
  pagination: any;
  fromCache: boolean;
}> => {
  const cacheKey = `community:projects:${offset}:${limit}`;
  
  // 🔍 检查缓存
  if (useCache) {
    const cached = CommunityCache.get(cacheKey);
    if (cached) {
      console.log('✅ 社区项目缓存命中', { offset, limit, cacheSize: CommunityCache.getStats().size });
      return { ...cached.data, fromCache: true };
    }
  }

  // 🚀 网络请求优化
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时 - 临时修复直到数据库优化完成

  try {
    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };

    // 🎯 添加缓存控制头
    if (!useCache) {
      headers['Cache-Control'] = 'no-cache';
      headers['Pragma'] = 'no-cache';
    }

    const response = await fetch(
      `/api/community/projects?limit=${limit}&offset=${offset}`,
      {
        headers,
        signal: controller.signal,
        // 🔥 启用浏览器缓存
        cache: useCache ? 'default' : 'no-cache'
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const result = {
      projects: data.projects || [],
      pagination: data.pagination || {},
      fromCache: false
    };

    // 🔥 缓存成功结果
    const etag = response.headers.get('ETag');
    CommunityCache.set(cacheKey, result, etag || undefined);

    console.log('✅ 社区项目网络请求成功', { 
      offset, 
      limit, 
      count: result.projects.length,
      responseTime: response.headers.get('X-Response-Time') || 'unknown'
    });

    return result;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        console.warn('⚠️ 社区项目请求超时');
        throw new Error('请求超时，请检查网络连接');
      }
    }
    
    console.error('❌ 社区项目网络请求失败:', error);
    throw error;
  }
};

// 🎯 记忆化的卡片组件
const MemoizedCommunityCard = memo(CommunityCard, (prevProps, nextProps) => {
  return (
    prevProps.project.id === nextProps.project.id &&
    prevProps.project.favoritesCount === nextProps.project.favoritesCount &&
    prevProps.project.isFavorited === nextProps.project.isFavorited
  );
});

export function CommunityProjectsGrid({
  onPreview,
  onOpen,
  onOpenCommunityPage,
  onOpenContactModal
}: CommunityProjectsGridProps) {
  const [projects, setProjects] = useState<CommunityProject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentOffset, setCurrentOffset] = useState(0);
  const [lastLoadTime, setLastLoadTime] = useState(0);

  const { toggleFavorite } = useFavorites();
  const { user } = useUser();

  // 🔐 包装打开项目函数，添加登录检查
  const handleOpenWithAuth = useCallback((project: CommunityProject) => {
    if (!user) {
      console.log('⚠️ CommunityProjectsGrid: 用户未登录，显示登录弹窗');
      // 触发登录弹窗
      window.dispatchEvent(new CustomEvent('show-login-modal'));
      return;
    }
    onOpen(project);
  }, [user, onOpen]);

  // 🎯 滚动优化
  const { restoreScrollPosition } = useScrollOptimization({
    containerId: 'community-projects-grid',
    throttleMs: 16,
    enableCache: true
  });

  // 🎯 防止频繁请求的限流
  const canMakeRequest = useCallback(() => {
    const now = Date.now();
    return now - lastLoadTime > 1000; // 1秒限流
  }, [lastLoadTime]);

  // 🎯 优化的加载函数
  const loadCommunityProjects = useCallback(async (
    offset = 0, 
    isLoadMore = false,
    forceRefresh = false
  ) => {
    // 🛡️ 防止重复请求
    if (!canMakeRequest() && !forceRefresh) {
      console.log('🛑 社区项目请求被限流');
      return;
    }

    // 🛡️ 防止同时多个加载请求
    if ((isLoadMore && isLoadingMore) || (!isLoadMore && isLoading)) {
      console.log('🛑 社区项目正在加载中');
      return;
    }

    try {
      if (isLoadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      console.log('🚀 加载社区项目 [优化版]', { offset, limit: ITEMS_PER_LOAD, isLoadMore, forceRefresh });

      const result = await fetchCommunityProjects(offset, ITEMS_PER_LOAD, !forceRefresh);
      const { projects: newProjects, pagination, fromCache } = result;

      console.log('✅ 社区项目加载成功', { 
        count: newProjects.length, 
        offset, 
        hasMore: pagination.hasMore ?? newProjects.length === ITEMS_PER_LOAD,
        fromCache,
        cacheStats: CommunityCache.getStats()
      });

      // 🔄 更新状态
      if (isLoadMore) {
        setProjects(prev => [...prev, ...newProjects]);
      } else {
        setProjects(newProjects);
      }

      setHasMore(pagination.hasMore ?? newProjects.length === ITEMS_PER_LOAD);
      setCurrentOffset(offset + newProjects.length);
      setLastLoadTime(Date.now());

    } catch (error) {
      console.error('❌ 加载社区项目失败:', error);
      const errorMessage = error instanceof Error ? error.message : '加载失败';
      setError(errorMessage);
      
      // 🎯 错误时不清空现有数据
      if (!isLoadMore) {
        setProjects([]);
      }
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [canMakeRequest, isLoading, isLoadingMore]);

  // 🎯 优化的收藏处理
  const handleToggleFavorite = useCallback(async (projectId: number, isFavorited: boolean) => {
    try {
      const result = await toggleFavorite(projectId, isFavorited);
      
      // 🔄 乐观更新本地状态
      setProjects(prev => prev.map(project => 
        project.id === projectId 
          ? { 
              ...project, 
              isFavorited: result.isFavorited,
              favoritesCount: result.favoritesCount 
            }
          : project
      ));

      // 🗑️ 清除相关缓存
      CommunityCache.clear();

      return result;
    } catch (error) {
      console.error('❌ 收藏操作失败:', error);
      throw error;
    }
  }, [toggleFavorite]);

  // 🎯 加载更多处理
  const handleLoadMore = useCallback(() => {
    if (hasMore && !isLoadingMore && canMakeRequest()) {
      loadCommunityProjects(currentOffset, true);
    }
  }, [currentOffset, hasMore, isLoadingMore, loadCommunityProjects, canMakeRequest]);

  // 🎯 刷新处理
  const handleRefresh = useCallback(() => {
    CommunityCache.clear();
    setCurrentOffset(0);
    loadCommunityProjects(0, false, true);
  }, [loadCommunityProjects]);

  // 🚀 初始化加载 - 优化依赖
  useEffect(() => {
    if (projects.length === 0 && !isLoading) {
      loadCommunityProjects(0, false);
      
      // 🔄 恢复滚动位置
      const timer = setTimeout(() => {
        restoreScrollPosition();
      }, 150);
      
      return () => clearTimeout(timer);
    }
  }, []); // 空依赖数组，只在组件挂载时运行

  // 🎯 记忆化的渲染函数 - 响应式网格
  const renderLoadingGrid = useMemo(() => (
    <div className="community-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
      {Array.from({ length: 6 }, (_, i) => (
        <div key={i} className="aspect-video bg-muted rounded-lg animate-pulse" />
      ))}
    </div>
  ), []);

  const renderErrorState = useMemo(() => (
    <div className="col-span-3 flex flex-col items-center justify-center py-8 text-center">
      <div className="text-muted-foreground mb-4">
        <span className="text-lg">😅</span>
      </div>
      <p className="text-sm text-muted-foreground mb-4">
        {error || '加载出错了'}
      </p>
      <Button 
        onClick={handleRefresh} 
        variant="outline" 
        size="sm"
        className="flex items-center gap-2"
      >
        <RefreshCw className="w-4 h-4" />
        重试
      </Button>
    </div>
  ), [error, handleRefresh]);

  const renderEmptyState = useMemo(() => (
    <div className="col-span-1 sm:col-span-2 lg:col-span-3 flex flex-col items-center justify-center py-8 text-center">
      <div className="text-muted-foreground mb-4">
        <span className="text-lg">🎨</span>
      </div>
      <p className="text-sm text-muted-foreground mb-2">
        还没有社区项目
      </p>
      <p className="text-xs text-muted-foreground">
        成为第一个分享作品的开发者！
      </p>
    </div>
  ), []);

  return (
    <div className="community-cards-container" id="community-projects-grid">
      {/* 🎯 卡片容器 */}
      <div>
        {/* 🎯 头部区域 - 保持左右布局 */}
        <div className="flex items-start justify-between gap-2 mb-4 sm:mb-6">
          <div className="flex-1 min-w-0">
            <h2 className="text-sm sm:text-base md:text-lg font-semibold text-gray-900 dark:text-white mb-1">
              LoomRun 社区精选
            </h2>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-neutral-400 leading-relaxed">
              探索大家用 LoomRun 开发的精彩作品
            </p>
          </div>
          <div className="flex-shrink-0">
            {onOpenCommunityPage && (
              <Button
                onClick={onOpenCommunityPage}
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center gap-1 text-xs sm:text-sm font-medium hover:bg-secondary/50 px-2 sm:px-3 py-1 sm:py-1.5 rounded-md"
              >
                浏览全部 →
              </Button>
            )}
          </div>
        </div>

        {/* 🎯 项目网格 */}
        <div>
          {isLoading ? (
            renderLoadingGrid
          ) : error && projects.length === 0 ? (
            <div className="community-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {renderErrorState}
            </div>
          ) : projects.length === 0 ? (
            <div className="community-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {renderEmptyState}
            </div>
          ) : (
          <>
            {/* 项目卡片网格 - 响应式布局 */}
            <div className="community-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {projects.map((project, index) => (
                <MemoizedCommunityCard
                  key={`${project.id}-${index}`}
                  project={project}
                  onPreview={onPreview}
                  onOpen={handleOpenWithAuth}
                  onToggleFavorite={handleToggleFavorite}
                />
              ))}
            </div>

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="flex justify-center pt-4">
                <Button
                  onClick={handleLoadMore}
                  disabled={isLoadingMore}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  {isLoadingMore ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      加载中...
                    </>
                  ) : (
                    '加载更多'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
        </div>
      </div>

      {/* 🏢 企业级页脚区域 */}
      <footer className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] mt-20">
        {/* 主页脚内容 */}
        <div className="relative border-2 border-border/60 rounded-lg mx-4 overflow-hidden">
          {/* 网格背景 */}
          <div 
            className="absolute inset-0 opacity-30 dark:opacity-20"
            style={{
              backgroundImage: `
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '24px 24px'
            }}
          />
          <div className="relative max-w-7xl mx-auto px-6 py-8">
            {/* 主要内容区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-12 mb-6">
              {/* 品牌区域 */}
              <div className="lg:col-span-1">
                <div className="flex items-center gap-3 mb-6">
                  {/* LoomRun 小精灵图标 */}
                  <div
                    className="flex items-center justify-center"
                    style={{
                      width: '85px',      // 容器宽度
                      height: '69px',     // 容器高度
                      transform: 'translate(0px, -10px)', // 容器位置调整 (x, y)
                    }}
                  >
                    <GhostLogo size={60} />
                  </div>
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 bg-clip-text text-transparent">
                    LoomRun
                  </span>
                </div>


                {/* 社交媒体链接 */}
                <div className="flex items-center gap-4 ml-[10%]">
                  <a
                    href="https://www.douyin.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center hover:scale-110 transition-transform duration-200"
                    aria-label="抖音"
                  >
                    <img src="/douyin.svg" alt="抖音" className="w-8 h-8" />
                  </a>
                  <a
                    href="https://www.kuaishou.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center hover:scale-110 transition-transform duration-200"
                    aria-label="快手"
                  >
                    <img src="/kuaishou.svg" alt="快手" className="w-8 h-8" />
                  </a>
                  <a
                    href="https://www.xiaohongshu.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center hover:scale-110 transition-transform duration-200"
                    aria-label="小红书"
                  >
                    <img src="/xiaohongshu.svg" alt="小红书" className="w-8 h-8" />
                  </a>
                  <a
                    href="https://www.bilibili.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center hover:scale-110 transition-transform duration-200"
                    aria-label="哔哩哔哩"
                  >
                    <img src="/bilibili.svg" alt="哔哩哔哩" className="w-8 h-8" />
                  </a>
                </div>
              </div>

              

              {/* 导航链接区域 + 交流群二维码 */}
              <div className="lg:col-span-3 grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* 导航链接 */}
                <div className="lg:col-span-2 grid grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-base font-semibold text-foreground mb-4">产品服务</h4>
                    <ul className="space-y-3">
                      <li><button onClick={() => {
                        const aiInput = document.querySelector('.ai-input-container');
                        if (aiInput) {
                          aiInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                      }} className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">AI网站生成</button></li>
                      <li><a href="http://localhost:3141/features" target="_blank" className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">功能介绍</a></li>
                      <li><button onClick={() => onOpenCommunityPage?.()} className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">社区作品</button></li>
                      <li><a href="https://github.com/abnb0208/CodeTime" target="_blank" rel="noopener noreferrer" className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">开源项目</a></li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-base font-semibold text-foreground mb-4">帮助支持</h4>
                    <ul className="space-y-3">
                      <li><a href="#" className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">使用指南</a></li>
                      <li><a href="#" className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">常见问题</a></li>
                      <li><a href="#" className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">视频教程</a></li>
                      <li><button onClick={() => onOpenContactModal?.()} className="text-base text-muted-foreground hover:text-blue-600 hover:font-medium transition-all duration-200">联系客服</button></li>
                    </ul>
                  </div>
                </div>

                {/* 交流群二维码 - 可调整参数 */}
                <div className="flex flex-col items-center lg:items-start">
                  <h4 className="text-sm font-semibold text-foreground mb-4">加入交流群</h4>
                  {/* 二维码容器 - 可调整参数 */}
                  <div
                    className="flex items-center justify-center"
                    style={{
                      width: '160px',     // 容器宽度
                      height: '160px',    // 容器高度
                      transform: 'translate(0px, 0px)', // 容器位置调整 (x, y)
                    }}
                  >
                    <img
                      src="/qr-code-group.png"
                      alt="LoomRun交流群二维码"
                      className="object-contain"
                      style={{
                        width: '140px',    // 二维码宽度
                        height: '140px',   // 二维码高度
                        transform: 'scale(1) translate(0px, -20px)', // 二维码缩放和位置微调
                        transformOrigin: 'center'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 分割线 */}
            <div className="border-t border-border/40 mb-2 -mt-6"></div>

            {/* 底部信息区域 */}
            <div className="flex flex-col lg:flex-row justify-between items-center gap-3 -mb-5">
              {/* 版权信息 */}
              <div className="text-center lg:text-left">
                <p className="text-base text-muted-foreground mb-0.5">
                  © 2025 上海万来云边科技服务有限公司 版权所有
                </p>
                <div className="flex flex-col sm:flex-row items-center gap-1 sm:gap-3 text-sm text-muted-foreground">
                  <a href="#" className="hover:text-blue-600 hover:font-medium transition-all duration-200 cursor-pointer">浙ICP备14039628号-2</a>
                  <span className="hidden sm:inline text-border">|</span>
                  <a href="#" className="hover:text-blue-600 hover:font-medium transition-all duration-200 cursor-pointer">浙公网安备33011002011644号</a>
                </div>
              </div>

              {/* 法律链接 */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <a href="/Legal page/loomrun-privacy-policy.html" target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 hover:font-medium transition-all duration-200">隐私政策</a>
                <a href="/Legal page/loomrun-terms-of-service.html" target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 hover:font-medium transition-all duration-200">服务条款</a>
                <a href="/Legal page/loomrun-cookie-policy.html" target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 hover:font-medium transition-all duration-200">Cookie政策</a>
              </div>
            </div>
            </div>
        </div>
      </footer>
    </div>
  );
}