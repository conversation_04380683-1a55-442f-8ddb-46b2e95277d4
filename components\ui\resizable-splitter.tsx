import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResizableSplitterProps {
  /** 分隔条方向 */
  direction?: 'horizontal' | 'vertical';
  /** 初始位置百分比 (0-100) */
  initialPosition?: number;
  /** 最小左侧/顶部区域宽度百分比 */
  minLeftSize?: number;
  /** 最大左侧/顶部区域宽度百分比 */
  maxLeftSize?: number;
  /** 当位置改变时的回调 */
  onPositionChange?: (position: number) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否禁用拖拽 */
  disabled?: boolean;
}

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  startPosition: number;
  containerRect: DOMRect;
  lastPosition: number;
}

export const ResizableSplitter: React.FC<ResizableSplitterProps> = ({
  direction = 'vertical',
  initialPosition = 50,
  minLeftSize = 17,
  maxLeftSize = 80,
  onPositionChange,
  className,
  disabled = false
}) => {
  // 🎯 State - 精简化管理
  const [currentPosition, setCurrentPosition] = useState<number>(initialPosition);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // 🎯 确保初始位置正确更新
  useEffect(() => {
    if (initialPosition !== currentPosition) {
      setCurrentPosition(initialPosition);
    }
  }, [initialPosition]);

  // 🎯 Refs - 优化拖拽状态管理
  const containerRef = useRef<HTMLDivElement>(null);
  const dragState = useRef<DragState | null>(null);

  // 🎯 双击重置到默认位置
  const handleDoubleClick = useCallback(() => {
    if (disabled) return;
    const defaultPosition = 20; // 默认重置为20%
    setCurrentPosition(defaultPosition);
    onPositionChange?.(defaultPosition);
  }, [disabled, onPositionChange]);

  // 🎯 1. 鼠标按下事件 - 开始拖拽
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;
    
    e.preventDefault();
    e.stopPropagation();

    // 🔥 获取容器信息
    const container = containerRef.current?.parentElement;
    if (!container) {
      console.error('拖拽失败：无法获取容器');
      return;
    }

    const containerRect = container.getBoundingClientRect();
    
    // 🎯 初始化拖拽状态
    dragState.current = {
      isDragging: true,
      startX: e.clientX,
      startY: e.clientY,
      startPosition: currentPosition,
      containerRect: containerRect,
      lastPosition: currentPosition
    };

    setIsDragging(true);
    
    // 🔥 关键优化：拖拽开始时优化DOM性能
    document.body.style.cursor = direction === 'vertical' ? 'col-resize' : 'row-resize';
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    
    // 🚀 性能优化：智能识别模式并优化
    const previewIframe = document.getElementById('preview-iframe');
    const codeEditor = document.querySelector('.code-scroll-optimized');
    const previewContainer = document.querySelector('.preview-container-optimized');
    
    // 针对预览模式的优化
    if (previewIframe) {
      previewIframe.style.pointerEvents = 'none';
      previewIframe.style.willChange = 'transform';
      previewIframe.style.transformStyle = 'preserve-3d';
    }
    
    // 🚀 针对代码编辑模式的专门优化
    if (codeEditor) {
      (codeEditor as HTMLElement).style.willChange = 'transform, contents';
      (codeEditor as HTMLElement).style.contain = 'layout style paint size';
      (codeEditor as HTMLElement).style.transformStyle = 'preserve-3d';
      // 暂停代码高亮更新
      (codeEditor as HTMLElement).setAttribute('data-drag-optimized', 'true');
    }
    
    if (previewContainer) {
      (previewContainer as HTMLElement).style.willChange = 'transform';
      (previewContainer as HTMLElement).style.contain = 'layout style paint';
    }
    
    // 🚀 全局性能模式
    document.documentElement.style.setProperty('--drag-performance-mode', '1');
    document.documentElement.style.setProperty('--disable-animations', '1');
    document.body.classList.add('dragging-active');

    // 🚀 Monaco编辑器特殊优化
    const monacoEditors = document.querySelectorAll('.monaco-editor');
    monacoEditors.forEach(editor => {
      (editor as HTMLElement).classList.add('drag-optimized');
    });

    console.log('🎯 拖拽开始 (高性能模式):', {
      startPos: currentPosition,
      hasCodeEditor: !!codeEditor,
      hasPreview: !!previewIframe,
      monacoCount: monacoEditors.length
    });
    
  }, [disabled, direction, currentPosition]);

  // 🎯 2. 鼠标移动事件 - 超高性能计算
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.current?.isDragging) return;

    // 🔥 检查鼠标按键状态
    if (e.buttons !== 1) {
      // 🔥 立即恢复性能设置
      const codeEditor = document.querySelector('.code-scroll-optimized');
      const previewIframe = document.getElementById('preview-iframe');
      const previewContainer = document.querySelector('.preview-container-optimized');
      
      if (codeEditor) {
        (codeEditor as HTMLElement).style.willChange = '';
        (codeEditor as HTMLElement).style.contain = '';
        (codeEditor as HTMLElement).style.transformStyle = '';
        (codeEditor as HTMLElement).removeAttribute('data-drag-optimized');
      }
      
      if (previewIframe) {
        previewIframe.style.pointerEvents = '';
        previewIframe.style.willChange = '';
        previewIframe.style.transformStyle = '';
      }
      
      if (previewContainer) {
        (previewContainer as HTMLElement).style.willChange = '';
        (previewContainer as HTMLElement).style.contain = '';
      }
      
      document.documentElement.style.removeProperty('--drag-performance-mode');
      document.documentElement.style.removeProperty('--disable-animations');
      document.body.classList.remove('dragging-active');
      
      // 🚀 恢复Monaco编辑器
      const monacoEditors = document.querySelectorAll('.monaco-editor.drag-optimized');
      monacoEditors.forEach(editor => {
        (editor as HTMLElement).classList.remove('drag-optimized');
      });
      
      if (dragState.current) {
        dragState.current = null;
        setIsDragging(false);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        document.body.style.webkitUserSelect = '';
      }
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    const { startX, startY, startPosition, containerRect } = dragState.current;
    const isVertical = direction === 'vertical';
    
    // 🎯 超高精度计算
    const currentX = e.clientX;
    const currentY = e.clientY;
    const delta = isVertical ? (currentX - startX) : (currentY - startY);
    
    // 🎯 转换为百分比变化
    const containerSize = isVertical ? containerRect.width : containerRect.height;
    const percentageChange = (delta / containerSize) * 100;
    
    // 🎯 计算新位置
    let newPosition = startPosition + percentageChange;
    newPosition = Math.max(minLeftSize, Math.min(maxLeftSize, newPosition));
    
    // 🔥 超高频更新优化：更小的阈值，更流畅的体验
    if (Math.abs(newPosition - dragState.current.lastPosition) > 0.001) {
      dragState.current.lastPosition = newPosition;
      
      // 🚀 双重优化：立即更新 + RAF批量渲染
      setCurrentPosition(newPosition);
      onPositionChange?.(newPosition);
      
      // 🎯 极少采样调试
      if (Math.random() < 0.005) {
        console.log('⚡ 超高性能拖拽');
      }
    }
    
  }, [direction, minLeftSize, maxLeftSize, onPositionChange]);

  // 🎯 3. 鼠标抬起事件 - 完整恢复
  const handleMouseUp = useCallback(() => {
    if (!dragState.current?.isDragging) return;

    // 🚀 完整恢复DOM性能设置
    const codeEditor = document.querySelector('.code-scroll-optimized');
    const previewIframe = document.getElementById('preview-iframe');
    const previewContainer = document.querySelector('.preview-container-optimized');
    
    if (codeEditor) {
      (codeEditor as HTMLElement).style.willChange = '';
      (codeEditor as HTMLElement).style.contain = '';
      (codeEditor as HTMLElement).style.transformStyle = '';
      (codeEditor as HTMLElement).removeAttribute('data-drag-optimized');
    }
    
    if (previewIframe) {
      previewIframe.style.pointerEvents = '';
      previewIframe.style.willChange = '';
      previewIframe.style.transformStyle = '';
    }
    
    if (previewContainer) {
      (previewContainer as HTMLElement).style.willChange = '';
      (previewContainer as HTMLElement).style.contain = '';
    }
    
    document.documentElement.style.removeProperty('--drag-performance-mode');
    document.documentElement.style.removeProperty('--disable-animations');
    document.body.classList.remove('dragging-active');

    // 🚀 恢复Monaco编辑器
    const monacoEditors = document.querySelectorAll('.monaco-editor.drag-optimized');
    monacoEditors.forEach(editor => {
      (editor as HTMLElement).classList.remove('drag-optimized');
    });

    console.log('✅ 高性能拖拽结束');

    // 🔥 清理拖拽状态
    dragState.current = null;
    setIsDragging(false);
    
    // 🔥 恢复全局样式
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';
    
  }, []);

  // 🎯 4. 全局事件监听 - 确保拖拽过程中的全局捕获
  useEffect(() => {
    if (isDragging) {
      console.log('🔗 绑定全局拖拽事件');
      
      // 🔥 处理特殊情况的函数
      const handleMouseLeave = () => {
        if (dragState.current?.isDragging) {
          console.log('🚪 鼠标离开窗口，结束拖拽');
          handleMouseUp();
        }
      };
      
      const handleVisibilityChange = () => {
        if (document.hidden && dragState.current?.isDragging) {
          console.log('👁️ 页面失去焦点，结束拖拽');
          handleMouseUp();
        }
      };
      
      // 🔥 防止干扰事件
      const preventDefaultEvents = (e: Event) => {
        if (dragState.current?.isDragging) {
          e.preventDefault();
          e.stopPropagation();
        }
      };
      
      // 🔥 关键：在document上监听，确保全局捕获
      document.addEventListener('mousemove', handleMouseMove, { passive: false, capture: true });
      document.addEventListener('mouseup', handleMouseUp, { passive: false, capture: true });
      document.addEventListener('mouseleave', handleMouseLeave, { passive: true });
      document.addEventListener('visibilitychange', handleVisibilityChange, { passive: true });
      document.addEventListener('contextmenu', preventDefaultEvents, { capture: true });
      document.addEventListener('selectstart', preventDefaultEvents, { capture: true });
      document.addEventListener('dragstart', preventDefaultEvents, { capture: true });

      return () => {
        console.log('🔗 清理全局拖拽事件');
        
        document.removeEventListener('mousemove', handleMouseMove, { capture: true });
        document.removeEventListener('mouseup', handleMouseUp, { capture: true });
        document.removeEventListener('mouseleave', handleMouseLeave);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        document.removeEventListener('contextmenu', preventDefaultEvents, { capture: true });
        document.removeEventListener('selectstart', preventDefaultEvents, { capture: true });
        document.removeEventListener('dragstart', preventDefaultEvents, { capture: true });
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 🎯 5. 组件清理
  useEffect(() => {
    return () => {
      // 🔥 确保组件卸载时清理所有状态
      dragState.current = null;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={cn(
        // 🎯 基础样式 - 更专业更精细
        "group relative flex-shrink-0 transition-colors duration-200",
        "resizable-splitter-optimized", // 性能优化类
        
        // 🎯 方向和尺寸
        direction === 'vertical' 
          ? "w-1 cursor-col-resize hover:w-1.5" // 精细化宽度
          : "h-1 cursor-row-resize hover:h-1.5",
        
        // 🎯 专业背景色 - 更低调
        "bg-border/30 hover:bg-border/50", // 进一步降低透明度
        
        // 🎯 拖拽状态 - 非常精细的选中效果
        isDragging && "bg-border/70 shadow-sm",
        
        // 🎯 过渡效果
        "transition-all duration-150 ease-out",
        
        className
      )}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      style={{
        zIndex: isDragging ? 50 : 10, // 拖拽时提升层级
      }}
    >
      {/* 🎯 专业拖拽指示器 - 更精细 */}
      <div
        className={cn(
          "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200",
          "drag-indicator-optimized", // 性能优化类
          direction === 'vertical' 
            ? "bg-gradient-to-r from-transparent via-blue-500/15 to-transparent" 
            : "bg-gradient-to-b from-transparent via-blue-500/15 to-transparent",
          isDragging && "opacity-100"
        )}
      />
      
      {/* 🎯 中心指示线 - 非常精细 */}
      <div
        className={cn(
          "absolute bg-blue-500/25 transition-opacity duration-200", // 更低调的颜色
          "drag-indicator-optimized", // 性能优化类
          direction === 'vertical' 
            ? "left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-px h-3" // 缩短高度
            : "left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-3 h-px", // 缩短宽度
          "opacity-0 group-hover:opacity-50", // 降低透明度
          isDragging && "opacity-70"
        )}
      />
    </div>
  );
};

export default ResizableSplitter; 