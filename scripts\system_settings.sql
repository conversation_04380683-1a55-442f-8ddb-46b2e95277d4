/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 01/08/2025 09:18:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for system_settings
-- ----------------------------
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `setting_type` enum('string','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_setting_key`(`setting_key` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_settings
-- ----------------------------
INSERT INTO `system_settings` VALUES (1, 'new_user_points_enabled', '1', 'boolean', '新用户注册送积分功能开关', 'points', 1, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (2, 'new_user_points_amount', '50', 'number', '新用户注册送积分数量', 'points', 1, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (3, 'recharge_service_enabled', '1', 'boolean', '充值服务功能开关', 'payment', 1, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (4, 'subscription_service_enabled', '1', 'boolean', '订阅服务功能开关', 'payment', 1, '2025-07-30 03:24:25', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (5, 'invitation_enabled', '1', 'boolean', '邀请功能开关', 'invitation', 1, '2025-07-30 03:30:19', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (6, 'invitation_points_per_user', '100', 'number', '每邀请一个用户获得的积分', 'invitation', 1, '2025-07-30 03:30:19', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (7, 'max_invitations_per_user', '3', 'number', '每个用户最多可邀请的用户数量', 'invitation', 1, '2025-07-30 03:30:19', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (8, 'invitation_code_length', '8', 'number', '邀请码长度', 'invitation', 1, '2025-07-30 03:30:19', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (9, 'invitation_expire_days', '30', 'number', '邀请码过期天数', 'invitation', 1, '2025-07-30 03:30:19', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (10, 'activity_points_validity_days', '30', 'number', '活动积分有效期天数', 'points', 1, '2025-07-30 04:09:16', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (13, 'points_consumption_priority', 'subscription,activity,recharge', 'string', '积分消费优先级顺序', 'points', 1, '2025-07-30 04:09:16', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (24, 'free_plan_enabled', '1', 'boolean', '免费版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (25, 'pro_plan_enabled', '1', 'boolean', 'Pro版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (26, 'max_plan_enabled', '1', 'boolean', 'Max版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (27, 'show_subscription_button', '1', 'boolean', '显示订阅积分按钮', 'ui', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (28, 'max_plan_custom_points_enabled', '1', 'boolean', 'Max版支持积分调整功能', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (29, 'max_plan_min_points', '2500', 'number', 'Max版最少积分数量', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (30, 'max_plan_max_points', '10000', 'number', 'Max版最多积分数量', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (31, 'max_plan_points_step', '500', 'number', 'Max版积分调整步长', 'subscription', 1, '2025-07-31 02:46:17', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (34, 'activity_points_config', '{\"invitation\":{\"validity_days\":365},\"registration\":{\"validity_days\":30}}', 'json', '不同活动积分有效期配置', 'points', 1, '2025-07-31 06:59:23', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (35, 'show_invitation_banner', '1', 'boolean', '显示邀请活动横幅', 'ui', 1, '2025-07-31 06:59:23', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (36, 'invitation_banner_text', '🎉 邀请好友注册，每成功邀请1人获得积分奖励！', 'string', '邀请横幅显示文字', 'ui', 1, '2025-07-31 06:59:23', '2025-08-01 00:51:37');
INSERT INTO `system_settings` VALUES (37, 'order_expire_minutes', '30', 'number', '订单自动过期时间（分钟）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (38, 'order_check_interval_minutes', '5', 'number', '订单过期检查定时任务间隔（分钟）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (39, 'payment_timeout_warning_minutes', '20', 'number', '支付超时提醒时间（分钟）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 08:21:19');
INSERT INTO `system_settings` VALUES (40, 'mock_payment_delay_seconds', '3', 'number', '模拟支付处理延迟时间（秒）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (41, 'subscription_order_prefix', 'SUB', 'string', '订阅订单编号前缀', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (42, 'recharge_order_prefix', 'RCH', 'string', '充值订单编号前缀', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (43, 'payment_methods_enabled', '[\"Alipay\",\"wechat\"]', 'json', '启用的支付方式列表', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:07:51');
INSERT INTO `system_settings` VALUES (44, 'payment_success_display_seconds', '5', 'number', '支付成功页面自动关闭时间（秒，0表示不自动关闭）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (45, 'duplicate_order_check_enabled', '1', 'boolean', '重复下单检测功能开关', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (46, 'order_status_sync_interval_seconds', '10', 'number', '支付页面订单状态同步间隔（秒）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');
INSERT INTO `system_settings` VALUES (47, 'recharge_points_default_validity_days', '0', 'number', '充值积分默认有效期（天，0表示永久）', 'payment', 1, '2025-08-01 06:01:49', '2025-08-01 06:01:49');

SET FOREIGN_KEY_CHECKS = 1;
