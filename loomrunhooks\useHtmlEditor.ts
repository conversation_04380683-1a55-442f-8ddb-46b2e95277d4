import { useState, useCallback } from "react";
import { HtmlHistory } from "@/types";

/**
 * 🎨 HTML 编辑器状态管理 Hook
 * 
 * 这个 hook 负责管理 LoomRun 编辑器的核心状态：
 * - HTML 内容的实时编辑状态
 * - 编辑历史记录的本地缓存
 * - AI 生成提示词的跟踪记录
 * 
 * @param defaultHtml - 初始 HTML 内容
 * @returns 编辑器状态和操作方法
 */
export const useHtmlEditor = (defaultHtml: string) => {
  // 🎯 主要 HTML 内容状态
  // 这是用户在编辑器中看到和编辑的实时内容
  const [html, setHtml] = useState<string>(defaultHtml);

  // 📚 HTML 编辑历史记录状态
  // 存储编辑过程中的历史版本，用于撤销/重做功能
  // 注意：这是本地缓存，不会保存到数据库
  const [htmlHistory, setHtmlHistory] = useState<HtmlHistory[]>([]);

  // 💬 AI 提示词历史记录状态
  // 跟踪用户输入的提示词，用于调试和优化 AI 生成效果
  const [prompts, setPrompts] = useState<string[]>([]);

  // 🔄 更新 HTML 内容的优化方法
  const updateHtml = useCallback((newHtml: string) => {
    if (newHtml !== html) {
      setHtml(newHtml);
    }
  }, [html]);

  // 📝 添加新提示词的方法
  const addPrompt = useCallback((prompt: string) => {
    if (prompt.trim()) {
      setPrompts(prev => [...prev, prompt.trim()]);
    }
  }, []);

  // 🧹 清空编辑器状态的方法
  const resetEditor = useCallback(() => {
    setHtml(defaultHtml);
    setHtmlHistory([]);
    setPrompts([]);
  }, [defaultHtml]);

  // 📊 获取编辑器统计信息
  const getEditorStats = useCallback(() => {
    return {
      htmlLength: html.length,
      historyCount: htmlHistory.length,
      promptCount: prompts.length,
      hasContent: html.trim() !== defaultHtml.trim()
    };
  }, [html, htmlHistory.length, prompts.length, defaultHtml]);

  return {
    // 🎯 核心状态
    html,
    setHtml,
    htmlHistory,
    setHtmlHistory,
    prompts,
    setPrompts,

    // 🛠️ 辅助方法
    updateHtml,
    addPrompt,
    resetEditor,
    getEditorStats,
  };
};
