import { executeQuery } from './mysql';
import { addPointsTransaction, getUserPoints } from './points-service';
import { calculateActivityPointsExpiry } from './activity-points-config';

export interface InvitationRecord {
  id: number;
  inviter_user_id: number;
  invited_user_id: number;
  invite_code: string;
  invitation_status: 'pending' | 'registered' | 'expired';
  points_awarded: number;
  registered_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface InvitationStats {
  total_invitations: number;
  successful_invitations: number;
  pending_invitations: number;
  total_points_earned: number;
  remaining_invitations: number;
}

// 获取系统邀请设置
export const getInvitationSettings = async () => {
  try {
    const settings = await executeQuery(
      `SELECT setting_key, setting_value, setting_type 
       FROM system_settings 
       WHERE category = 'invitation' AND is_active = 1`,
      []
    ) as { setting_key: string; setting_value: string; setting_type: string }[];

    const config: Record<string, any> = {};
    settings.forEach(setting => {
      let value: any = setting.setting_value;
      
      switch (setting.setting_type) {
        case 'boolean':
          value = setting.setting_value === '1' || setting.setting_value === 'true';
          break;
        case 'number':
          value = parseInt(setting.setting_value) || 0;
          break;
        case 'json':
          try {
            value = JSON.parse(setting.setting_value);
          } catch {
            value = setting.setting_value;
          }
          break;
      }
      
      config[setting.setting_key] = value;
    });

    return {
      invitation_enabled: config.invitation_enabled || false,
      invitation_points_per_user: config.invitation_points_per_user || 100,
      max_invitations_per_user: config.max_invitations_per_user || 10,
      invitation_code_length: config.invitation_code_length || 8,
      invitation_expire_days: config.invitation_expire_days || 30
    };
  } catch (error) {
    console.error('获取邀请设置失败:', error);
    return {
      invitation_enabled: false,
      invitation_points_per_user: 100,
      max_invitations_per_user: 10,
      invitation_code_length: 8,
      invitation_expire_days: 30
    };
  }
};

// 获取用户邀请统计
export const getUserInvitationStats = async (userId: number): Promise<InvitationStats> => {
  try {
    const [stats] = await executeQuery(
      `SELECT 
        COUNT(*) as total_invitations,
        SUM(CASE WHEN invitation_status = 'registered' THEN 1 ELSE 0 END) as successful_invitations,
        SUM(CASE WHEN invitation_status = 'pending' THEN 1 ELSE 0 END) as pending_invitations,
        SUM(CASE WHEN invitation_status = 'registered' THEN points_awarded ELSE 0 END) as total_points_earned
       FROM user_invitations 
       WHERE inviter_user_id = ?`,
      [userId]
    ) as any[];

    const settings = await getInvitationSettings();
    const remaining = Math.max(0, settings.max_invitations_per_user - (stats?.total_invitations || 0));

    return {
      total_invitations: stats?.total_invitations || 0,
      successful_invitations: stats?.successful_invitations || 0,
      pending_invitations: stats?.pending_invitations || 0,
      total_points_earned: stats?.total_points_earned || 0,
      remaining_invitations: remaining
    };
  } catch (error) {
    console.error('获取用户邀请统计失败:', error);
    return {
      total_invitations: 0,
      successful_invitations: 0,
      pending_invitations: 0,
      total_points_earned: 0,
      remaining_invitations: 0
    };
  }
};

// 生成邀请链接
export const generateInvitationLink = async (userId: number): Promise<{ success: boolean; inviteUrl?: string; message?: string }> => {
  try {
    const settings = await getInvitationSettings();
    
    if (!settings.invitation_enabled) {
      return { success: false, message: '邀请功能已关闭' };
    }

    // 检查用户邀请次数限制
    const stats = await getUserInvitationStats(userId);
    if (stats.remaining_invitations <= 0) {
      return { success: false, message: `您已达到最大邀请次数限制 (${settings.max_invitations_per_user}次)` };
    }

    // 获取用户的邀请码
    const userResult = await executeQuery(
      'SELECT invite_code FROM users WHERE id = ?',
      [userId]
    ) as { invite_code: string }[];

    if (!userResult.length || !userResult[0].invite_code) {
      return { success: false, message: '用户邀请码不存在' };
    }

    const inviteCode = userResult[0].invite_code;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL ||
                   (process.env.NODE_ENV === 'development' ? 'http://localhost:3141' : 'https://loomrun.top');
    const inviteUrl = `${baseUrl}?invite=${inviteCode}`;

    return { success: true, inviteUrl };
  } catch (error) {
    console.error('生成邀请链接失败:', error);
    return { success: false, message: '生成邀请链接失败' };
  }
};

// 验证邀请码
export const validateInviteCode = async (inviteCode: string): Promise<{ valid: boolean; inviterUserId?: number; message?: string }> => {
  try {
    if (!inviteCode || inviteCode.trim().length === 0) {
      return { valid: false, message: '邀请码不能为空' };
    }

    const settings = await getInvitationSettings();
    if (!settings.invitation_enabled) {
      return { valid: false, message: '邀请功能已关闭' };
    }

    // 查找邀请者
    const inviterResult = await executeQuery(
      'SELECT id, nickname FROM users WHERE invite_code = ? AND is_active = 1',
      [inviteCode.trim()]
    ) as { id: number; nickname: string }[];

    if (!inviterResult.length) {
      return { valid: false, message: '邀请码无效' };
    }

    const inviterUserId = inviterResult[0].id;

    // 检查邀请者是否还能邀请更多用户
    const stats = await getUserInvitationStats(inviterUserId);
    if (stats.remaining_invitations <= 0) {
      return { valid: false, message: '该邀请码已达到使用上限' };
    }

    return { valid: true, inviterUserId };
  } catch (error) {
    console.error('验证邀请码失败:', error);
    return { valid: false, message: '验证邀请码失败' };
  }
};

// 处理邀请注册成功
export const processInvitationRegistration = async (
  inviterUserId: number,
  invitedUserId: number,
  inviteCode: string
): Promise<{ success: boolean; pointsAwarded?: number; message?: string }> => {
  const mysql = require('mysql2/promise');
  let connection;

  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    await connection.beginTransaction();

    const settings = await getInvitationSettings();
    
    if (!settings.invitation_enabled) {
      await connection.rollback();
      return { success: false, message: '邀请功能已关闭' };
    }

    // 检查是否已经处理过这个邀请
    const [existingInvitation] = await connection.execute(
      'SELECT id FROM user_invitations WHERE invited_user_id = ? LIMIT 1',
      [invitedUserId]
    );

    if (existingInvitation.length > 0) {
      await connection.rollback();
      return { success: false, message: '该用户已被邀请过' };
    }

    const pointsAwarded = settings.invitation_points_per_user;
    const now = new Date();

    // 创建邀请记录
    const [invitationResult] = await connection.execute(
      `INSERT INTO user_invitations 
       (inviter_user_id, invited_user_id, invite_code, invitation_status, points_awarded, registered_at)
       VALUES (?, ?, ?, 'registered', ?, ?)`,
      [inviterUserId, invitedUserId, inviteCode, pointsAwarded, now]
    );

    // 获取邀请者当前积分
    const [inviterPoints] = await connection.execute(
      'SELECT points FROM users WHERE id = ?',
      [inviterUserId]
    );

    const currentPoints = inviterPoints[0]?.points || 0;
    const newPoints = currentPoints + pointsAwarded;

    // 计算积分有效期 - 使用活动积分配置工具
    const expiresAt = await calculateActivityPointsExpiry('invitation', now);
    const isPermanent = expiresAt === null;

    // 创建积分余额记录
    const [balanceResult] = await connection.execute(
      `INSERT INTO user_points_balance
       (user_id, points_type, points_amount, expires_at, source_order_id)
       VALUES (?, 'activity', ?, ?, ?)`,
      [inviterUserId, pointsAwarded, expiresAt, invitationResult.insertId]
    );

    // 创建积分交易记录
    await connection.execute(
      `INSERT INTO points_transactions
       (user_id, transaction_type, points_amount, balance_before, balance_after,
        source_type, points_type, expires_at, balance_record_id, source_id, description, metadata)
       VALUES (?, 'earn', ?, ?, ?, 'invitation', 'activity', ?, ?, ?, ?, ?)`,
      [
        inviterUserId,
        pointsAwarded,
        currentPoints,
        newPoints,
        expiresAt,
        balanceResult.insertId,
        invitedUserId.toString(),
        `邀请新用户注册奖励 ${pointsAwarded} 积分${isPermanent ? '（永久有效）' : ''}`,
        JSON.stringify({
          invited_user_id: invitedUserId,
          invite_code: inviteCode,
          invitation_id: invitationResult.insertId,
          points_expires_at: expiresAt ? expiresAt.toISOString() : null,
          is_permanent: isPermanent
        })
      ]
    );

    // 更新邀请者积分和邀请计数
    await connection.execute(
      `UPDATE users 
       SET points = ?, total_earned_points = total_earned_points + ?, invitation_count = invitation_count + 1
       WHERE id = ?`,
      [newPoints, pointsAwarded, inviterUserId]
    );

    // 更新被邀请者的邀请关系
    await connection.execute(
      'UPDATE users SET invited_by_user_id = ? WHERE id = ?',
      [inviterUserId, invitedUserId]
    );

    await connection.commit();

    console.log(`✅ 邀请奖励处理成功: 邀请者${inviterUserId} 获得${pointsAwarded}积分，被邀请者${invitedUserId}`);
    
    return { success: true, pointsAwarded };
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('处理邀请注册失败:', error);
    return { success: false, message: '处理邀请奖励失败' };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 获取用户邀请列表
export const getUserInvitations = async (userId: number): Promise<InvitationRecord[]> => {
  try {
    const invitations = await executeQuery(
      `SELECT ui.*, u.nickname as invited_user_nickname
       FROM user_invitations ui
       LEFT JOIN users u ON ui.invited_user_id = u.id
       WHERE ui.inviter_user_id = ?
       ORDER BY ui.created_at DESC`,
      [userId]
    ) as any[];

    return invitations;
  } catch (error) {
    console.error('获取用户邀请列表失败:', error);
    return [];
  }
};
