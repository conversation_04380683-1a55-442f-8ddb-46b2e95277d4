"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useThemeLanguage } from "@/components/providers/theme-language-provider"
import { cn } from "@/lib/utils"

interface ThemeToggleProps {
  className?: string;
  variant?: "default" | "outline" | "ghost" | "link" | "destructive" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  showLabel?: boolean;
}

export function ThemeToggle({ 
  className, 
  variant = "ghost", 
  size = "icon",
  showLabel = false 
}: ThemeToggleProps) {
  const { theme, changeTheme } = useThemeLanguage()

  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark'
    changeTheme(newTheme)
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      className={cn("transition-all duration-200", className)}
      title={theme === 'dark' ? '切换到浅色模式' : '切换到深色模式'}
    >
      {theme === 'dark' ? (
        <>
          <Sun className="h-4 w-4" />
          {showLabel && <span className="ml-2">浅色</span>}
        </>
      ) : (
        <>
          <Moon className="h-4 w-4" />
          {showLabel && <span className="ml-2">深色</span>}
        </>
      )}
      <span className="sr-only">
        {theme === 'dark' ? '切换到浅色模式' : '切换到深色模式'}
      </span>
    </Button>
  )
} 