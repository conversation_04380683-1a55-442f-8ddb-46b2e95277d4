"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EnhancedShareModal } from "@/components/editor/enhanced-share-modal";
import { Share2 } from "lucide-react";

interface ShareButtonProps {
  projectId: number;
  htmlContent: string;
  projectTitle?: string;
}

export function ShareButton({ projectId, htmlContent, projectTitle }: ShareButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsDialogOpen(true)}
        variant="outline"
        size="sm"
        className="bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground h-6 px-2 text-xs transition-colors duration-200"
      >
        <Share2 className="w-3 h-3 mr-1" />
        共享
      </Button>

      <EnhancedShareModal
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        projectId={projectId}
        htmlContent={htmlContent}
        projectTitle={projectTitle}
      />
    </>
  );
} 