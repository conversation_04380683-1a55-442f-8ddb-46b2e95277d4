import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { grantNewUserPoints } from "@/lib/points-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 测试发放新用户积分
    const success = await grantNewUserPoints(user.id);

    if (success) {
      return NextResponse.json({
        success: true,
        message: "新用户积分发放成功",
        data: {
          userId: user.id,
          nickname: user.nickname
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: "积分发放失败或用户已获得过注册积分"
      });
    }

  } catch (error) {
    console.error("测试积分发放失败:", error);
    return NextResponse.json({ 
      error: "服务器错误",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
