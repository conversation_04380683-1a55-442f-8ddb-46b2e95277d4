import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';
import { getNumberSetting } from '@/lib/points-service';

export async function POST(request: NextRequest) {
  try {
    // 简单的API密钥验证（可以在系统设置中配置）
    const { api_key } = await request.json();
    
    // 这里可以添加API密钥验证逻辑
    // const validApiKey = await getStringSetting('cron_api_key', 'default_key');
    // if (api_key !== validApiKey) {
    //   return NextResponse.json({ success: false, error: "无效的API密钥" }, { status: 401 });
    // }

    console.log('🕐 开始执行订单过期检查任务...');

    // 获取过期时间设置
    const expireMinutes = await getNumberSetting('order_expire_minutes', 30);
    const expireTime = new Date(Date.now() - expireMinutes * 60 * 1000);

    // 处理过期的订阅订单
    const subscriptionResult = await executeQuery(
      `UPDATE membership_orders 
       SET status = 'expired', updated_at = NOW()
       WHERE status = 'pending' AND order_expires_at < NOW()`,
      []
    ) as any;

    // 处理过期的充值订单
    const rechargeResult = await executeQuery(
      `UPDATE recharge_orders 
       SET status = 'expired', updated_at = NOW()
       WHERE status = 'pending' AND order_expires_at < NOW()`,
      []
    ) as any;

    const expiredSubscriptionCount = subscriptionResult.affectedRows || 0;
    const expiredRechargeCount = rechargeResult.affectedRows || 0;
    const totalExpired = expiredSubscriptionCount + expiredRechargeCount;

    console.log(`✅ 订单过期检查完成: 订阅订单${expiredSubscriptionCount}个, 充值订单${expiredRechargeCount}个, 总计${totalExpired}个订单已过期`);

    return NextResponse.json({
      success: true,
      data: {
        expired_subscription_orders: expiredSubscriptionCount,
        expired_recharge_orders: expiredRechargeCount,
        total_expired: totalExpired,
        check_time: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ 订单过期检查失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
