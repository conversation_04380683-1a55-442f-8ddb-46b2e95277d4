"use client";

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { useSettings, type Language, type Theme } from '@/loomrunhooks/useSettings';
import { useThemeLanguage } from '@/components/providers/theme-language-provider';
import { 
  Settings, 
  Globe, 
  Palette, 
  User, 
  Trash2, 
  LogOut, 
  CreditCard, 
  Crown, 
  FileText, 
  Shield,
  ChevronRight,
  Sun,
  Moon
} from 'lucide-react';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type SettingsTab = 'general' | 'account' | 'payment' | 'legal';

export default function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  const { 
    settings, 
    isLoading, 
    updateDataOptimization, 
    updateLanguage, 
    updateTheme 
  } = useSettings();
  const { changeTheme, changeLanguage } = useThemeLanguage();

  // 🌍 三语言支持辅助函数
  const getTranslation = (zh: string, en: string, ug: string) => {
    switch (settings.language) {
      case 'en': return en;
      case 'ug': return ug;
      default: return zh;
    }
  };

  const handleDeleteAllProjects = () => {
    const confirmMsg = getTranslation(
      '确认删除所有对话项目？此操作不可恢复。',
      'Are you sure you want to delete all conversation projects? This action cannot be undone.',
      'بارلىق سۆھبەت تۈرلىرىنى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلات قايتۇرۇلماس.'
    );
    const successMsg = getTranslation(
      '🗑️ 所有对话项目已删除',
      '🗑️ All conversation projects have been deleted',
      '🗑️ بارلىق سۆھبەت تۈرلىرى ئۆچۈرۈلدى'
    );
    
    if (confirm(confirmMsg)) {
      toast.success(successMsg);
    }
  };

  const handleLogout = () => {
    const confirmMsg = getTranslation(
      '确认注销账号？您将被登出系统。',
      'Are you sure you want to sign out? You will be logged out of the system.',
      'ھېساباتتىن چىقىشنى جەزملەشتۈرەمسىز؟ سىز سىستېمىدىن چىقىرىلىسىز.'
    );
    const successMsg = getTranslation(
      '👋 账号已注销',
      '👋 Account signed out',
      '👋 ھېسابات چىقىرىلدى'
    );
    
    if (confirm(confirmMsg)) {
      toast.success(successMsg);
      onClose();
    }
  };

  const handleUpgradeToPro = () => {
    const msg = getTranslation(
      '🚀 即将跳转到Pro订阅页面',
      '🚀 Redirecting to Pro subscription page',
      '🚀 Pro ئوبۇنا بېتىگە يۆنلەندۈرۈۋاتىدۇ'
    );
    toast.success(msg);
  };

  const tabs = [
    { 
      id: 'general', 
      label: getTranslation('通用设置', 'General', 'ئادەتتىكى تەڭشەك'), 
      icon: Settings 
    },
    { 
      id: 'account', 
      label: getTranslation('账号管理', 'Account', 'ھېسابات باشقۇرۇش'), 
      icon: User 
    },
    { 
      id: 'payment', 
      label: getTranslation('会员管理', 'Membership', 'ئەزالىق باشقۇرۇش'), 
      icon: CreditCard 
    },
    { 
      id: 'legal', 
      label: getTranslation('服务协议', 'Legal', 'مۇلازىمەت كېلىشىمى'), 
      icon: FileText 
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-4 sm:space-y-6">
            {/* 语言设置 */}
            <div className="space-y-2 sm:space-y-3">
              <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                <Globe className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div className="min-w-0 flex-1">
                  <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                    {getTranslation('语言', 'Language', 'تىل')}
                  </h3>
                  <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                    {getTranslation('选择您的界面语言', 'Choose your interface language', 'كۆرۈنۈش تىلىنى تاللاڭ')}
                  </p>
                </div>
              </div>
              <Select value={settings.language} onValueChange={async (value: Language) => {
                // 先更新本地语言显示
                changeLanguage(value);
                
                // 然后保存到服务器
                const success = await updateLanguage(value);
                if (success) {
                  // 触发设置变更事件
                  window.dispatchEvent(new CustomEvent('settingsChanged', { 
                    detail: { language: value } 
                  }));
                  // 移除成功提示，静默保存
                } else {
                  toast.error(getTranslation('保存失败，请重试', 'Failed to save settings, please try again', 'ساقلاش مەغلۇپ بولدى، قايتا سىناڭ'));
                }
              }}>
                <SelectTrigger className="w-full h-9 sm:h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">简体中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="ug">ئۇيغۇرچە</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="border-t border-slate-200 dark:border-slate-700"></div>

            {/* 主题设置 */}
            <div className="space-y-2 sm:space-y-3">
              <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                <Palette className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 flex-shrink-0 mt-0.5" />
                <div className="min-w-0 flex-1">
                  <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                    {getTranslation('主题', 'Theme', 'ئۇسلۇب')}
                  </h3>
                  <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                    {getTranslation('选择您喜欢的界面主题', 'Choose your preferred interface theme', 'ياقتۇرغان كۆرۈنۈش ئۇسلۇبىنى تاللاڭ')}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                {[
                  { 
                    value: 'light', 
                    label: getTranslation('浅色', 'Light', 'ئۇچۇق'), 
                    icon: Sun 
                  },
                  { 
                    value: 'dark', 
                    label: getTranslation('深色', 'Dark', 'قارا'), 
                    icon: Moon 
                  },
                ].map(({ value, label, icon: Icon }) => (
                  <Button
                    key={value}
                    variant={settings.theme === value ? 'default' : 'outline'}
                    size="sm"
                    className="flex flex-col items-center gap-1 sm:gap-2 h-auto py-2 sm:py-3"
                    onClick={async () => {
                      // 先更新本地主题显示
                      changeTheme(value as Theme);
                      
                      // 然后保存到服务器
                      const success = await updateTheme(value as Theme);
                      if (success) {
                        // 触发设置变更事件
                        window.dispatchEvent(new CustomEvent('settingsChanged', { 
                          detail: { theme: value } 
                        }));
                        // 移除成功提示，静默保存
                      } else {
                        toast.error(getTranslation('保存失败，请重试', 'Failed to save settings, please try again', 'ساقلاش مەغلۇپ بولدى، قايتا سىناڭ'));
                      }
                    }}
                  >
                    <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs">{label}</span>
                  </Button>
                ))}
              </div>
            </div>

            <div className="border-t border-slate-200 dark:border-slate-700"></div>

            {/* 数据优化 */}
            <div className="space-y-2 sm:space-y-3">
              <div className={`flex items-start justify-between gap-2 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-start gap-2 sm:gap-3 min-w-0 flex-1 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                  <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 flex-1">
                    <h3 className="text-xs sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words leading-tight">
                      {getTranslation('数据用于优化体验', 'Data for Experience Optimization', 'سانلىق مەلۇماتنى ئەھۋالنى ياخشىلاشقا ئىشلىتىش')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 leading-relaxed break-words">
                    {getTranslation(
                    '允许我们将你的对话内容用于优化 LoomRun 的使用体验。我们保障你的数据隐私安全。',
                    'Allow us to use your conversation content to optimize LoomRun experience. We protect your data privacy and security.',
                    'LoomRun مۇلازىمىتىنى ياخشىلاش ئۈچۈن، سۆھبەت مەزمۇنىڭىزنى ئىشلىتىشكە رۇخسەت قىلىڭ. بىز سىزنىڭ مەخپىيەتلىكىڭىز ۋە سانلىق مەلۇمات بىخەتەرلىكىنى قاتتىق قوغدايمىز.'
                  )}
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.dataOptimization}
                  onCheckedChange={async (checked) => {
                    const success = await updateDataOptimization(checked);
                    if (success) {
                      // 移除成功提示，静默保存
                    } else {
                      toast.error(getTranslation('保存失败，请重试', 'Failed to save settings, please try again', 'ساقلاش مەغلۇپ بولدى، قايتا سىناڭ'));
                    }
                  }}
                  className="flex-shrink-0"
                />
              </div>
            </div>
          </div>
        );

        case 'account':
          return (
            <div className="space-y-4 sm:space-y-6">
              {/* 删除所有项目 */}
              <div className="space-y-2 sm:space-y-3">
                <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                  <Trash2 className="w-4 h-4 sm:w-5 sm:h-5 text-orange-600 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                      {getTranslation('删除所有对话项目', 'Delete All Projects', 'بارلىق سۆھبەت پروگراممىلىرىنى ئۆچۈرۈش')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                      {getTranslation('永久删除您的所有对话记录和项目数据', 'Permanently delete all your conversation records and project data', 'بارلىق سۆھبەت خاتىرىلىرىڭىز ۋە پروگرامما سانلىق مەلۇماتلىرىنى مەڭگۈلۈك ئۆچۈرۈش')}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className={`w-full border-orange-200 text-orange-700 hover:bg-orange-50 dark:border-orange-800 dark:text-orange-300 dark:hover:bg-orange-900/20 h-auto py-2 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}
                  onClick={handleDeleteAllProjects}
                >
                  <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 mx-1.5 sm:mx-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm break-words leading-tight">
                    {getTranslation('删除所有项目', 'Delete All Projects', 'بارلىق پروگراممىلارنى ئۆچۈرۈش')}
                  </span>
                </Button>
              </div>
        
              <div className="border-t border-slate-200 dark:border-slate-700"></div>
        
              {/* 注销账号 */}
              <div className="space-y-2 sm:space-y-3">
                <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                  <LogOut className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                      {getTranslation('注销账号', 'Sign Out', 'ھېساباتتىن چىقىش')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                      {getTranslation('退出当前账号，返回登录页面', 'Sign out of your current account and return to the login page', 'نۆۋەتتىكى ھېساباتتىن چىقىپ، كىرىش بېتىگە قايتىڭىز')}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className={`w-full border-red-200 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-300 dark:hover:bg-red-900/20 h-auto py-2 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}
                  onClick={handleLogout}
                >
                  <LogOut className="w-3 h-3 sm:w-4 sm:h-4 mx-1.5 sm:mx-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm break-words leading-tight">
                    {getTranslation('注销账号', 'Sign Out', 'ھېساباتتىن چىقىش')}
                  </span>
                </Button>
              </div>
            </div>
          );

          case 'payment':
            return (
              <div className="space-y-4 sm:space-y-6">
                {/* 会员管理 */}
                <div className="space-y-2 sm:space-y-3">
                  <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                    <Crown className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                    <div className="min-w-0 flex-1">
                      <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                        {getTranslation('会员管理', 'Membership Management', 'ئەزا باشقۇرۇش')}
                      </h3>
                      <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                        {getTranslation('管理您的订阅和会员权益', 'Manage your subscription and membership benefits', 'ئوبۇنە ۋە ئەزا ھوقۇقلىرىنى باشقۇرۇش')}
                      </p>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-center justify-between mb-2 sm:mb-3">
                      <div>
                        <h4 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100">
                          {getTranslation('当前套餐', 'Current Plan', 'نۆۋەتتىكى پىلان')}
                        </h4>
                        <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400">
                          {getTranslation('免费版', 'Free Plan', 'ھەقسىز پىلان')}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg sm:text-2xl font-bold text-slate-900 dark:text-slate-100">¥0</p>
                        <p className="text-xs text-slate-600 dark:text-slate-400">
                          {getTranslation('/月', '/month', '/ئاي')}
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white"
                      onClick={handleUpgradeToPro}
                    >
                      <Crown className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                      {getTranslation('升级到 Pro', 'Upgrade to Pro', 'Pro نۇسقىغا يۆتەل')}
                    </Button>
                  </div>
                </div>
          
                <div className="border-t border-slate-200 dark:border-slate-700"></div>
          
                {/* Pro 权限 */}
                <div className="space-y-2 sm:space-y-3">
                  <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                    <CreditCard className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                    <div className="min-w-0 flex-1">
                      <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                        {getTranslation('Pro 用户权限', 'Pro User Benefits', 'Pro ئىشلەتكۈچى ھوقۇقلىرى')}
                      </h3>
                      <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                        {getTranslation('解锁更多高级功能', 'Unlock more advanced features', 'قوشۇمچە ئالىي ئىمكانىيەتلەرنى ئاچ')}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1.5 sm:space-y-2">
                    {(settings.language === 'en' ? [
                      '🚀 Unlimited project creation',
                      '⚡ Priority processing speed',
                      '🎨 Advanced template library',
                      '☁️ Cloud data synchronization',
                      '📊 Detailed usage statistics',
                      '🛠️ Professional technical support'
                    ] : settings.language === 'ug' ? [
                      '🚀 چەكلىمىسىز پروگرامما قۇرۇش',
                      '⚡ ئالدىنقى بىر تەرەپ قىلىش سۈرئىتى',
                      '🎨 ئالىي قېلىپلار كۈتۈپخانىسى',
                      '☁️ بۇلۇت سانلىق مەلۇمات ماسقەدەملەش',
                      '📊 تەپسىلىي ئىشلىتىش سىتاتىستىكىسى',
                      '🛠ى كەسپىي تېخنىكا قوللاش'
                    ] : [
                      '🚀 无限制项目创建',
                      '⚡ 优先处理速度',
                      '🎨 高级模板库',
                      '☁️ 云端数据同步',
                      '📊 详细使用统计',
                      '🛠️ 专业技术支持'
                    ]).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs sm:text-sm text-slate-700 dark:text-slate-300">
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );

        case 'legal':
          return (
            <div className="space-y-4 sm:space-y-6">
              {/* 用户协议 */}
              <div className="space-y-2 sm:space-y-3">
                <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                      {getTranslation('用户协议', 'Terms of Service', 'ئىشلەتكۈچى كېلىشىمى')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                      {getTranslation('查看服务条款和使用规则', 'View service terms and usage rules', 'مۇلازىمەت شەرتلىرى ۋە ئىشلىتىش قائىدىلىرىنى كۆرۈڭ')}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => window.open('/Legal page/loomrun-terms-of-service.html', '_blank')}
                >
                  <span className="text-sm">
                    {getTranslation('服务条款', 'Terms of Service', 'مۇلازىمەت شەرتلىرى')}
                  </span>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                </Button>
              </div>

              <div className="border-t border-slate-200 dark:border-slate-700"></div>

              {/* 隐私政策 */}
              <div className="space-y-2 sm:space-y-3">
                <div className={`flex items-start gap-2 sm:gap-3 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
                  <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-slate-100 break-words">
                      {getTranslation('隐私政策', 'Privacy Policy', 'مەخپىيەتلىك سىياسىتى')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 break-words leading-relaxed">
                      {getTranslation('了解我们如何保护您的数据', 'Learn how we protect your data', 'سانلىق مەلۇماتىڭىزنى قانداق قوغدايدىغانلىقىنى بىلىڭ')}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => window.open('/Legal page/loomrun-privacy-policy.html', '_blank')}
                >
                  <span className="text-sm">
                    {getTranslation('隐私保护政策', 'Privacy Policy', 'مەخپىيەتلىك قوغداش سىياسىتى')}
                  </span>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                </Button>
              </div>

              {/* Cookie政策 */}
              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-start gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center flex-shrink-0">
                    <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm sm:text-base font-medium text-slate-900 dark:text-slate-100">
                      {getTranslation('Cookie政策', 'Cookie Policy', 'Cookie سىياسىتى')}
                    </h3>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mt-0.5 sm:mt-1">
                      {getTranslation('了解我们如何使用Cookie', 'Learn how we use cookies', 'Cookie نى قانداق ئىشلىتىدىغانلىقىنى بىلىڭ')}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => window.open('/Legal page/loomrun-cookie-policy.html', '_blank')}
                >
                  <span className="text-sm">
                    {getTranslation('Cookie政策', 'Cookie Policy', 'Cookie سىياسىتى')}
                  </span>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                </Button>
              </div>

              <div className="border-t border-slate-200 dark:border-slate-700"></div>

              {/* 版本信息 */}
              <div className="text-center space-y-1 sm:space-y-2">
                <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400">
                  {getTranslation('LoomRun v0.1.1 - 织梦术师', 'LoomRun v0.1.1 - Dream Weaver', 'LoomRun v0.1.1 - چۈش توقۇغۇچى')}
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  {getTranslation('© 2025 LoomRun. 编织逻辑，运行万物.', '© 2025 LoomRun. Weave Logic, Run Everything.', '© 2025 LoomRun. مەنزىلەر توقۇش، ھەممىنى ئىجرا قىلىش.')}
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-500 mt-1">
                  {getTranslation('上海万来云边科技服务有限公司', 'Shanghai WalleyX Technology Service Co., Ltd.', 'شاڭخەي ۋاللەيX تېخنىكا مۇلازىمەت چەكلىك شىركىتى')}
                </p>
              </div>
            </div>
          );

        default:
          return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`max-w-sm w-[95vw] max-h-[90vh] sm:max-w-2xl sm:w-full sm:max-h-[90vh] p-0 rounded-xl sm:rounded-2xl border-0 shadow-xl bg-white dark:bg-slate-900 overflow-hidden ${settings.language === 'ug' ? 'text-right' : ''}`} showCloseButton={false}>
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-2 sm:p-6 border-b border-slate-200 dark:border-slate-700">
            <DialogTitle className={`text-sm sm:text-xl font-bold text-slate-900 dark:text-slate-100 flex items-center gap-2 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
              <Settings className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
              <span className="truncate">
                {getTranslation('系统设置', 'Settings', 'سىستېما تەڭشىكى')}
              </span>
            </DialogTitle>
            <button
              onClick={onClose}
              className="p-1 rounded-md hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 移动端顶部导航 */}
          <div className="sm:hidden border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50">
            <div className={`flex overflow-x-auto px-1 ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}>
              {tabs.map(({ id, label, icon: Icon }) => (
                <Button
                  key={id}
                  variant="ghost"
                  size="sm"
                  className={`flex-shrink-0 gap-1 rounded-none border-b-2 px-2 py-2 text-xs min-w-0 ${
                    activeTab === id 
                      ? 'border-blue-600 text-blue-600 bg-blue-50 dark:bg-blue-900/20' 
                      : 'border-transparent text-slate-600 dark:text-slate-400'
                  } ${settings.language === 'ug' ? 'flex-row-reverse' : ''}`}
                  onClick={() => setActiveTab(id as SettingsTab)}
                >
                  <Icon className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap text-xs leading-tight overflow-hidden text-ellipsis max-w-[60px]">
                    {label}
                  </span>
                </Button>
              ))}
            </div>
          </div>

          <div className="flex flex-1 overflow-hidden">
            {/* 桌面端左侧导航 */}
            <div className="hidden sm:block w-56 border-r border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50">
              <div className="p-4 space-y-2">
                {tabs.map(({ id, label, icon: Icon }) => (
                  <Button
                    key={id}
                    variant={activeTab === id ? 'default' : 'ghost'}
                    className={`w-full justify-start gap-3 ${
                      activeTab === id 
                        ? 'bg-blue-600 text-white shadow-sm' 
                        : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'
                    }`}
                    onClick={() => setActiveTab(id as SettingsTab)}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* 内容区域 */}
            <div className="flex-1 overflow-y-auto">
              <div className={`p-2 sm:p-6 ${settings.language === 'ug' ? 'text-right' : ''}`}>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                ) : (
                  renderTabContent()
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 