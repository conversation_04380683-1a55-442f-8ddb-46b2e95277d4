/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 01/08/2025 09:18:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for recharge_orders
-- ----------------------------
DROP TABLE IF EXISTS `recharge_orders`;
CREATE TABLE `recharge_orders`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `points_amount` int NOT NULL,
  `points_validity_days` int NOT NULL DEFAULT 0,
  `package_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联的积分套餐key',
  `bonus_points` int NOT NULL DEFAULT 0 COMMENT '赠送积分数量',
  `original_price` decimal(10, 2) NOT NULL,
  `discount_price` decimal(10, 2) NOT NULL,
  `payment_method` enum('alipay','wechat','mock') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'mock',
  `status` enum('pending','paid','cancelled','expired','refunded') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending',
  `paid_at` datetime NULL DEFAULT NULL,
  `order_expires_at` datetime NULL DEFAULT NULL COMMENT '订单过期时间（创建时间+30分钟）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_package_key`(`package_key` ASC) USING BTREE,
  INDEX `idx_order_expires_at`(`order_expires_at` ASC) USING BTREE,
  INDEX `idx_bonus_points`(`bonus_points` ASC) USING BTREE,
  INDEX `idx_user_status_expires`(`user_id` ASC, `status` ASC, `order_expires_at` ASC) USING BTREE,
  INDEX `idx_status_expires`(`status` ASC, `order_expires_at` ASC) USING BTREE,
  CONSTRAINT `recharge_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of recharge_orders
-- ----------------------------
INSERT INTO `recharge_orders` VALUES (1, 46, 'RCH20250801957150', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'paid', '2025-08-01 07:16:08', '2025-07-31 23:45:46', '2025-08-01 07:15:46', '2025-08-01 07:16:08');
INSERT INTO `recharge_orders` VALUES (2, 46, 'RCH20250801403518', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'expired', NULL, '2025-08-01 00:03:52', '2025-08-01 07:33:52', '2025-08-01 08:15:58');
INSERT INTO `recharge_orders` VALUES (3, 47, 'RCH20250801486067', 200, 0, 'basic_100', 0, 20.00, 19.90, 'mock', 'cancelled', NULL, '2025-08-01 00:33:16', '2025-08-01 08:03:16', '2025-08-01 08:14:00');
INSERT INTO `recharge_orders` VALUES (4, 47, 'RCH20250801717615', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'cancelled', NULL, '2025-08-01 00:44:00', '2025-08-01 08:14:00', '2025-08-01 08:15:06');
INSERT INTO `recharge_orders` VALUES (5, 47, 'RCH20250801189645', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'expired', NULL, '2025-08-01 00:45:06', '2025-08-01 08:15:06', '2025-08-01 08:15:58');
INSERT INTO `recharge_orders` VALUES (6, 47, 'RCH20250801513629', 200, 0, 'basic_100', 0, 20.00, 19.90, 'mock', 'cancelled', NULL, '2025-08-01 08:55:40', '2025-08-01 08:25:40', '2025-08-01 08:28:00');
INSERT INTO `recharge_orders` VALUES (7, 47, 'RCH20250801543815', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'cancelled', NULL, '2025-08-01 08:58:00', '2025-08-01 08:28:00', '2025-08-01 08:29:02');
INSERT INTO `recharge_orders` VALUES (8, 47, 'RCH20250801708286', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'cancelled', NULL, '2025-08-01 08:59:02', '2025-08-01 08:29:02', '2025-08-01 08:29:47');
INSERT INTO `recharge_orders` VALUES (9, 47, 'RCH20250801316300', 1200, 0, 'premium_1000', 300, 120.00, 105.00, 'mock', 'cancelled', NULL, '2025-08-01 08:59:47', '2025-08-01 08:29:47', '2025-08-01 08:32:42');
INSERT INTO `recharge_orders` VALUES (10, 47, 'RCH20250801382060', 600, 0, 'standard_500', 100, 60.00, 55.00, 'mock', 'pending', NULL, '2025-08-01 09:02:42', '2025-08-01 08:32:42', '2025-08-01 08:32:42');
INSERT INTO `recharge_orders` VALUES (11, 48, 'RCH20250801974085', 600, 0, 'standard_500', 100, 60.00, 55.00, 'wechat', 'paid', '2025-08-01 08:34:06', '2025-08-01 09:03:58', '2025-08-01 08:33:58', '2025-08-01 08:34:06');
INSERT INTO `recharge_orders` VALUES (12, 48, 'RCH20250801668802', 4800, 0, 'premium_1000', 1200, 480.00, 420.00, 'mock', 'pending', NULL, '2025-08-01 09:42:39', '2025-08-01 09:12:39', '2025-08-01 09:12:39');

SET FOREIGN_KEY_CHECKS = 1;
