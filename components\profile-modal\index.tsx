"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { X, Camera, Check, Edit2, Save, RotateCcw, ZoomIn, ZoomOut, RotateCw, Move } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { useUser } from "@/loomrunhooks/useUser";
import Image from "next/image";

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// 系统预设头像列表
const SYSTEM_AVATARS = [
  "/avatars/1.png",
  "/avatars/2.png", 
  "/avatars/3.png",
  "/avatars/4.png",
  "/avatars/5.png",
  "/avatars/6.png",
  "/avatars/7.png",
  "/avatars/8.png",
];

// 头像裁剪组件
const SmartAvatarCropper = ({ 
  imageSrc, 
  onCropComplete, 
  onCancel 
}: {
  imageSrc: string;
  onCropComplete: (croppedImage: string) => void;
  onCancel: () => void;
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  
  const [cropState, setCropState] = useState({
    x: 0,
    y: 0,
    scale: 1,
    rotation: 0
  });
  
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isLoading, setIsLoading] = useState(true);
  
  const CONTAINER_SIZE = 320;
  const CROP_SIZE = 200;

  // 初始化图片位置和缩放
  const initializeImage = useCallback((img: HTMLImageElement) => {
    const containerWidth = CONTAINER_SIZE;
    const containerHeight = CONTAINER_SIZE;
    
    // 计算最佳缩放比例，确保图片能完全覆盖裁剪区域
    const scaleX = CROP_SIZE / img.naturalWidth;
    const scaleY = CROP_SIZE / img.naturalHeight;
    const initialScale = Math.max(scaleX, scaleY, 0.5); // 至少0.5倍缩放
    
    // 计算缩放后的尺寸
    const scaledWidth = img.naturalWidth * initialScale;
    const scaledHeight = img.naturalHeight * initialScale;
    
         // 自动居中
     const centerX = (containerWidth - scaledWidth) / 2;
     const centerY = (containerHeight - scaledHeight) / 2;
    
    setImageSize({ width: scaledWidth, height: scaledHeight });
    setCropState({
      x: centerX,
      y: centerY,
      scale: initialScale,
      rotation: 0
    });
    
    setIsLoading(false);
  }, []);

  // 处理图片加载
  const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    initializeImage(img);
  }, [initializeImage]);

  // 鼠标/触摸事件处理
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    
    // 设置指针捕获
    if (containerRef.current) {
      containerRef.current.setPointerCapture(e.pointerId);
    }
  }, []);

  const handlePointerMove = useCallback((e: React.PointerEvent) => {
    if (!isDragging) return;
    
    e.preventDefault();
    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;
    
    setCropState(prev => ({
      ...prev,
      x: prev.x + deltaX,
      y: prev.y + deltaY
    }));
    
    setDragStart({ x: e.clientX, y: e.clientY });
  }, [isDragging, dragStart]);

  const handlePointerUp = useCallback((e: React.PointerEvent) => {
    setIsDragging(false);
    
    // 释放指针捕获
    if (containerRef.current) {
      containerRef.current.releasePointerCapture(e.pointerId);
    }
  }, []);

  // 滚轮缩放
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    
    const delta = e.deltaY * -0.001;
    const newScale = Math.max(0.1, Math.min(3, cropState.scale + delta));
    
    // 计算新的图片尺寸
    const img = imageRef.current;
    if (!img) return;
    
    const newWidth = img.naturalWidth * newScale;
    const newHeight = img.naturalHeight * newScale;
    
    // 以鼠标位置为中心进行缩放
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      const scaleRatio = newScale / cropState.scale;
      const newX = mouseX - (mouseX - cropState.x) * scaleRatio;
      const newY = mouseY - (mouseY - cropState.y) * scaleRatio;
      
      setCropState(prev => ({
        ...prev,
        x: newX,
        y: newY,
        scale: newScale
      }));
      
      setImageSize({ width: newWidth, height: newHeight });
    }
  }, [cropState]);

  // 缩放控制
  const handleZoom = useCallback((direction: 'in' | 'out') => {
    const delta = direction === 'in' ? 0.1 : -0.1;
    const newScale = Math.max(0.1, Math.min(3, cropState.scale + delta));
    
    const img = imageRef.current;
    if (!img) return;
    
    const newWidth = img.naturalWidth * newScale;
    const newHeight = img.naturalHeight * newScale;
    
    // 以容器中心为缩放中心
    const centerX = CONTAINER_SIZE / 2;
    const centerY = CONTAINER_SIZE / 2;
    
    const scaleRatio = newScale / cropState.scale;
    const newX = centerX - (centerX - cropState.x) * scaleRatio;
    const newY = centerY - (centerY - cropState.y) * scaleRatio;
    
    setCropState(prev => ({
      ...prev,
      x: newX,
      y: newY,
      scale: newScale
    }));
    
    setImageSize({ width: newWidth, height: newHeight });
  }, [cropState]);

  // 旋转控制
  const handleRotate = useCallback(() => {
    setCropState(prev => ({
      ...prev,
      rotation: (prev.rotation + 90) % 360
    }));
  }, []);

  // 重置到初始状态
  const handleReset = useCallback(() => {
    const img = imageRef.current;
    if (img) {
      initializeImage(img);
    }
  }, [initializeImage]);

  // 实时预览更新
  useEffect(() => {
    const updatePreview = () => {
      const previewCanvas = previewCanvasRef.current;
      const img = imageRef.current;
      
      if (!previewCanvas || !img) return;
      
      const ctx = previewCanvas.getContext('2d');
      if (!ctx) return;
      
      // 设置预览画布尺寸
      previewCanvas.width = 80;
      previewCanvas.height = 80;
      
      // 清空画布
      ctx.clearRect(0, 0, 80, 80);
      
      // 创建圆形裁剪路径
      ctx.save();
      ctx.beginPath();
      ctx.arc(40, 40, 40, 0, Math.PI * 2);
      ctx.clip();
      
      // 计算裁剪区域
      const cropCenterX = CONTAINER_SIZE / 2;
      const cropCenterY = CONTAINER_SIZE / 2;
      const cropRadius = CROP_SIZE / 2;
      
      // 计算源图片在裁剪区域中的位置
      const sourceX = (cropCenterX - cropRadius - cropState.x) / cropState.scale;
      const sourceY = (cropCenterY - cropRadius - cropState.y) / cropState.scale;
      const sourceSize = CROP_SIZE / cropState.scale;
      
      // 绘制预览
      ctx.drawImage(
        img,
        sourceX, sourceY, sourceSize, sourceSize,
        0, 0, 80, 80
      );
      
      ctx.restore();
    };
    
    if (!isLoading) {
      updatePreview();
    }
  }, [cropState, isLoading]);

  // 执行裁剪
  const handleCrop = useCallback(() => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    
    if (!canvas || !img) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 设置输出画布尺寸
    canvas.width = 200;
    canvas.height = 200;
    
    // 清空画布
    ctx.clearRect(0, 0, 200, 200);
    
    // 创建圆形裁剪路径
    ctx.save();
    ctx.beginPath();
    ctx.arc(100, 100, 100, 0, Math.PI * 2);
    ctx.clip();
    
    // 计算裁剪区域
    const cropCenterX = CONTAINER_SIZE / 2;
    const cropCenterY = CONTAINER_SIZE / 2;
    const cropRadius = CROP_SIZE / 2;
    
    // 计算源图片在裁剪区域中的位置
    const sourceX = (cropCenterX - cropRadius - cropState.x) / cropState.scale;
    const sourceY = (cropCenterY - cropRadius - cropState.y) / cropState.scale;
    const sourceSize = CROP_SIZE / cropState.scale;
    
    // 应用旋转
    if (cropState.rotation !== 0) {
      ctx.translate(100, 100);
      ctx.rotate((cropState.rotation * Math.PI) / 180);
      ctx.translate(-100, -100);
    }
    
    // 绘制最终图片
    ctx.drawImage(
      img,
      sourceX, sourceY, sourceSize, sourceSize,
      0, 0, 200, 200
    );
    
    ctx.restore();
    
    // 转换为base64
    const croppedImage = canvas.toDataURL('image/png', 0.9);
    onCropComplete(croppedImage);
  }, [cropState, onCropComplete]);

  return (
    <div className="space-y-4">
      {/* 标题和说明 */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-foreground mb-2">头像裁剪</h3>
        <p className="text-sm text-muted-foreground">拖拽移动图片，滚轮调整大小</p>
      </div>
      
      {/* 主要裁剪区域 */}
      <div className="flex gap-4">
        {/* 裁剪器 */}
        <div className="flex-1">
          <div 
            ref={containerRef}
            className="relative mx-auto border-2 border-border rounded-xl overflow-hidden bg-background select-none"
            style={{ 
              width: CONTAINER_SIZE, 
              height: CONTAINER_SIZE,
              cursor: isDragging ? 'grabbing' : 'grab'
            }}
            onPointerDown={handlePointerDown}
            onPointerMove={handlePointerMove}
            onPointerUp={handlePointerUp}
            onWheel={handleWheel}
          >
            {/* 加载状态 */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-secondary">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}
            
            {/* 图片 */}
            <img
              ref={imageRef}
              src={imageSrc}
              alt="待裁剪图片"
              className="absolute object-cover select-none pointer-events-none"
              style={{
                left: cropState.x,
                top: cropState.y,
                width: imageSize.width,
                height: imageSize.height,
                transform: `rotate(${cropState.rotation}deg)`,
                transformOrigin: 'center'
              }}
              onLoad={handleImageLoad}
              draggable={false}
            />
            
            {/* 裁剪框 */}
            <div
              className="absolute border-2 border-primary rounded-full pointer-events-none"
              style={{
                left: (CONTAINER_SIZE - CROP_SIZE) / 2,
                top: (CONTAINER_SIZE - CROP_SIZE) / 2,
                width: CROP_SIZE,
                height: CROP_SIZE,
                boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)'
              }}
            >
              {/* 裁剪框内部边框 */}
              <div className="absolute inset-1 border border-white/30 dark:border-white/30 border-gray-500/50 rounded-full" />
              
              {/* 中心十字线 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-4 h-0.5 bg-white/50 dark:bg-white/50 bg-gray-600/70 absolute" />
                <div className="h-4 w-0.5 bg-white/50 dark:bg-white/50 bg-gray-600/70 absolute" />
              </div>
            </div>
            
            {/* 操作提示 */}
            {!isDragging && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 dark:bg-black/70 bg-white/90 text-white dark:text-white text-gray-800 text-xs px-3 py-1 rounded-full backdrop-blur-sm">
                <Move className="w-3 h-3 inline mr-1" />
                拖拽移动，滚轮缩放
              </div>
            )}
          </div>
        </div>
        
        {/* 实时预览 */}
        <div className="flex flex-col items-center">
          <div className="text-sm text-muted-foreground mb-2">实时预览</div>
          <div className="relative">
            <canvas
              ref={previewCanvasRef}
              className="border-2 border-border rounded-full bg-secondary"
              style={{ width: 80, height: 80 }}
            />
            <div className="absolute inset-0 border-2 border-primary/30 rounded-full pointer-events-none" />
          </div>
          <div className="text-xs text-muted-foreground mt-2 text-center">
            200×200px<br />圆形头像
          </div>
        </div>
      </div>
      
      {/* 控制按钮 */}
      <div className="flex gap-2 justify-center">
        <Button
          onClick={() => handleZoom('out')}
          size="sm"
          variant="outline"
          className="bg-background border-border text-foreground hover:bg-secondary"
          disabled={cropState.scale <= 0.1}
        >
          <ZoomOut className="w-4 h-4" />
        </Button>
        
        <Button
          onClick={() => handleZoom('in')}
          size="sm"
          variant="outline"
          className="bg-background border-border text-foreground hover:bg-secondary"
          disabled={cropState.scale >= 3}
        >
          <ZoomIn className="w-4 h-4" />
        </Button>
        
        <Button
          onClick={handleRotate}
          size="sm"
          variant="outline"
          className="bg-background border-border text-foreground hover:bg-secondary"
        >
          <RotateCw className="w-4 h-4" />
        </Button>
        
        <Button
          onClick={handleReset}
          size="sm"
          variant="outline"
          className="bg-background border-border text-foreground hover:bg-secondary"
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex gap-3 pt-4">
        <Button
          onClick={onCancel}
          variant="outline"
          className="flex-1 bg-background border-border text-foreground hover:bg-secondary"
        >
          <X className="w-4 h-4 mr-2" />
          取消
        </Button>
        <Button
          onClick={handleCrop}
          className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
        >
          <Check className="w-4 h-4 mr-2" />
          确认裁剪
        </Button>
      </div>
    </div>
  );
};

export default function ProfileModal({ isOpen, onClose }: ProfileModalProps) {
  const { user, refreshUser } = useUser();
  const [nickname, setNickname] = useState(user?.nickname || "");
  const [selectedAvatar, setSelectedAvatar] = useState<string>(user?.avatar_url || "");
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showCropper, setShowCropper] = useState(false);
  const [cropImage, setCropImage] = useState<string>("");
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempNickname, setTempNickname] = useState("");
  const [nicknameStatus, setNicknameStatus] = useState<{
    checking: boolean;
    available: boolean | null;
    message: string;
  }>({ checking: false, available: null, message: '' });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const nicknameCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 重置表单
  const resetForm = () => {
    setNickname(user?.nickname || "");
    setSelectedAvatar(user?.avatar_url || "");
    setShowCropper(false);
    setCropImage("");
    setIsEditingName(false);
    setTempNickname("");
    setNicknameStatus({ checking: false, available: null, message: '' });
    if (nicknameCheckTimeoutRef.current) {
      clearTimeout(nicknameCheckTimeoutRef.current);
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 检查用户名可用性
  const checkNicknameAvailability = useCallback(async (nicknameToCheck: string) => {
    if (!nicknameToCheck.trim()) {
      setNicknameStatus({ checking: false, available: null, message: '' });
      return;
    }

    if (nicknameToCheck.trim().length > 20) {
      setNicknameStatus({ 
        checking: false, 
        available: false, 
        message: '用户名不能超过20个字符' 
      });
      return;
    }

    setNicknameStatus({ checking: true, available: null, message: '检查中...' });

    try {
      const response = await fetch('/api/me/check-nickname', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ nickname: nicknameToCheck.trim() }),
      });

      if (response.ok) {
        const data = await response.json();
        setNicknameStatus({
          checking: false,
          available: data.available,
          message: data.message
        });
      } else {
        setNicknameStatus({
          checking: false,
          available: false,
          message: '检查失败，请重试'
        });
      }
    } catch (error) {
      console.error('检查用户名失败:', error);
      setNicknameStatus({
        checking: false,
        available: false,
        message: '检查失败，请重试'
      });
    }
  }, []);

  // 处理用户名输入变化
  const handleNicknameChange = useCallback((value: string) => {
    setTempNickname(value);
    
    // 清除之前的定时器
    if (nicknameCheckTimeoutRef.current) {
      clearTimeout(nicknameCheckTimeoutRef.current);
    }
    
    // 设置新的定时器，延迟检查
    nicknameCheckTimeoutRef.current = setTimeout(() => {
      checkNicknameAvailability(value);
    }, 500);
  }, [checkNicknameAvailability]);

  // 开始编辑用户名
  const startEditingName = () => {
    setTempNickname(nickname);
    setIsEditingName(true);
    setNicknameStatus({ checking: false, available: null, message: '' });
  };

  // 保存用户名
  const saveNickname = () => {
    if (tempNickname.trim() && nicknameStatus.available) {
      setNickname(tempNickname.trim());
      setIsEditingName(false);
      setNicknameStatus({ checking: false, available: null, message: '' });
    }
  };

  // 取消编辑用户名
  const cancelEditingName = () => {
    setTempNickname("");
    setIsEditingName(false);
    setNicknameStatus({ checking: false, available: null, message: '' });
    if (nicknameCheckTimeoutRef.current) {
      clearTimeout(nicknameCheckTimeoutRef.current);
    }
  };

  // 处理头像点击
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件');
      return;
    }

    // 检查文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('图片大小不能超过5MB');
      return;
    }

    // 读取文件并显示裁剪器
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setCropImage(result);
      setShowCropper(true);
    };
    reader.readAsDataURL(file);
  };

  // 处理裁剪完成
  const handleCropComplete = async (croppedImage: string) => {
    setIsUploading(true);
    try {
      // 上传裁剪后的图片
      const response = await fetch('/api/upload/avatar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: croppedImage }),
      });

      if (!response.ok) {
        throw new Error('上传失败');
      }

      const data = await response.json();
      setSelectedAvatar(data.url);
      setShowCropper(false);
      toast.success('头像上传成功');
    } catch (error) {
      console.error('头像上传失败:', error);
      toast.error('头像上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 保存用户信息
  const handleSave = async () => {
    if (!nickname.trim()) {
      toast.error('请输入用户名');
      return;
    }

    setIsSaving(true);
    try {
      // 先检查用户名是否可用
      const checkResponse = await fetch('/api/me/check-nickname', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ nickname: nickname.trim() }),
      });

      if (checkResponse.ok) {
        const checkData = await checkResponse.json();
        if (!checkData.available) {
          toast.error(checkData.message || '用户名已被使用');
          setIsSaving(false);
          return;
        }
      }

      // 更新用户信息
      const response = await fetch('/api/me/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          nickname: nickname.trim(),
          avatar_url: selectedAvatar,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '保存失败');
      }

      await refreshUser();
      toast.success('个人信息保存成功');
      handleClose();
    } catch (error) {
      console.error('保存失败:', error);
      toast.error(error instanceof Error ? error.message : '保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        showCloseButton={false}
        className="max-w-2xl mx-auto bg-background border border-border text-foreground rounded-lg shadow-2xl p-0 overflow-hidden"
      >
        <DialogTitle className="sr-only">个人中心</DialogTitle>
        <DialogDescription className="sr-only">修改个人信息</DialogDescription>
        
        {/* 头部 */}
        <div className="relative p-6 border-b border-border">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 w-8 h-8 rounded-full bg-secondary hover:bg-secondary/80 flex items-center justify-center transition-colors text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
          </button>
          
          <div className="text-center">
            <h2 className="text-lg font-semibold text-foreground">个人中心</h2>
            <p className="text-sm text-muted-foreground mt-1">
              {showCropper ? '头像裁剪' : '修改您的个人信息'}
            </p>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {showCropper ? (
            // 头像裁剪器视图
            <SmartAvatarCropper
              imageSrc={cropImage}
              onCropComplete={handleCropComplete}
              onCancel={() => setShowCropper(false)}
            />
          ) : (
            // 正常编辑视图
            <div className="space-y-6">
              {/* 头像编辑区域 */}
              <div className="text-center">
                <div className="relative inline-block group">
                  <button
                    onClick={handleAvatarClick}
                    className="relative block"
                    disabled={isUploading}
                  >
                    {selectedAvatar ? (
                      <img
                        src={selectedAvatar}
                        alt="当前头像"
                        className="w-24 h-24 rounded-full object-cover border-4 border-muted group-hover:border-primary transition-all duration-200"
                      />
                    ) : (
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-2xl border-4 border-muted group-hover:border-primary transition-all duration-200">
                        {nickname?.charAt(0)?.toUpperCase() || user.name?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                    )}
                    
                    {/* 悬停时显示的上传提示 */}
                    <div className="absolute inset-0 bg-black/50 dark:bg-black/50 bg-white/70 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <div className="text-center">
                        <Camera className="w-6 h-6 text-white dark:text-white text-gray-800 mx-auto mb-1" />
                        <p className="text-xs text-white dark:text-white text-gray-800">点击上传</p>
                      </div>
                    </div>
                    
                    {/* 上传中状态 */}
                    {isUploading && (
                      <div className="absolute inset-0 bg-black/70 dark:bg-black/70 bg-white/80 rounded-full flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white dark:border-white border-gray-800"></div>
                      </div>
                    )}
                  </button>
                </div>
                <p className="text-sm text-muted-foreground mt-3">
                  点击头像上传自定义头像，支持裁剪调整
                </p>
              </div>

              {/* 用户名编辑区域 */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-3">
                  用户名
                </label>
                {isEditingName ? (
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <div className="flex-1 relative">
                        <Input
                          value={tempNickname}
                          onChange={(e) => handleNicknameChange(e.target.value)}
                          placeholder="请输入用户名"
                          className={`bg-background border-border text-foreground placeholder-muted-foreground focus:border-primary ${
                            nicknameStatus.available === false ? 'border-red-500 dark:border-red-400' : 
                            nicknameStatus.available === true ? 'border-green-500 dark:border-green-400' : ''
                          }`}
                          maxLength={20}
                          autoFocus
                        />
                        {/* 检查状态指示器 */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          {nicknameStatus.checking ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                          ) : nicknameStatus.available === true ? (
                            <Check className="w-4 h-4 text-green-500 dark:text-green-400" />
                          ) : nicknameStatus.available === false ? (
                            <X className="w-4 h-4 text-red-500 dark:text-red-400" />
                          ) : null}
                        </div>
                      </div>
                      <Button
                        onClick={saveNickname}
                        size="sm"
                        className="bg-primary hover:bg-primary/90 text-primary-foreground px-3"
                        disabled={!tempNickname.trim() || nicknameStatus.checking || !nicknameStatus.available}
                      >
                        <Save className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={cancelEditingName}
                        size="sm"
                        variant="outline"
                        className="bg-background border-border text-foreground hover:bg-secondary px-3"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                    {/* 状态提示文字 */}
                    {nicknameStatus.message && (
                      <p className={`text-xs ${
                        nicknameStatus.available === false ? 'text-red-500 dark:text-red-400' : 
                        nicknameStatus.available === true ? 'text-green-500 dark:text-green-400' : 
                        'text-muted-foreground'
                      }`}>
                        {nicknameStatus.message}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-between p-3 bg-secondary rounded-lg border border-border">
                    <span className="text-foreground">{nickname || "未设置"}</span>
                    <Button
                      onClick={startEditingName}
                      size="sm"
                      variant="ghost"
                      className="text-muted-foreground hover:text-foreground hover:bg-secondary/80 p-2"
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>

              {/* 系统预设头像 */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-3">
                  选择系统头像
                </label>
                <div className="grid grid-cols-4 gap-3">
                  {SYSTEM_AVATARS.map((avatar, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedAvatar(avatar)}
                      className={`relative w-12 h-12 rounded-full overflow-hidden border-2 transition-all ${
                        selectedAvatar === avatar
                          ? 'border-primary ring-2 ring-primary/50'
                          : 'border-border hover:border-muted-foreground'
                      }`}
                    >
                      <Image
                        src={avatar}
                        alt={`系统头像 ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                      {selectedAvatar === avatar && (
                        <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                          <Check className="w-4 h-4 text-primary" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleClose}
                  variant="outline"
                  className="flex-1 bg-background border-border text-foreground hover:bg-secondary"
                >
                  取消
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving || !nickname.trim()}
                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground disabled:opacity-50"
                >
                  {isSaving ? "保存中..." : "保存"}
                </Button>
              </div>
            </div>
          )}

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
} 