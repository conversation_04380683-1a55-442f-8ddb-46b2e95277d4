import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken, getUserProjects, createSimpleProject, addCommunityProjectConversation } from "@/lib/auth-service";

// 🚀 企业级项目列表API - 性能优化版本
export async function GET(request: Request) {
  const startTime = performance.now();
  
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // 🔧 解析查询参数 - 支持分页和限制
    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get("limit") || "50"), 100); // 最大100个
    const offset = Math.max(parseInt(url.searchParams.get("offset") || "0"), 0);
    const includeCounts = url.searchParams.get("includeCounts") === "true";

    console.log(`🚀 获取项目列表请求`, {
      userId: user.id,
      limit,
      offset,
      includeCounts,
      userAgent: request.headers.get('user-agent')?.substring(0, 50)
    });

    // 🚀 高性能项目获取
    const projects = await getUserProjects(user.id, limit);
    
    const duration = performance.now() - startTime;
    
    // 📊 性能监控
    console.log(`✅ 项目列表获取完成`, {
      userId: user.id,
      projectCount: projects.length,
      duration: `${duration.toFixed(1)}ms`,
      avgProjectSize: projects.length > 0 ? (projects.reduce((sum, p) => sum + (p.html_content?.length || 0), 0) / projects.length).toFixed(0) + ' chars' : '0'
    });

    // 🎯 性能警告
    if (duration > 1000) {
      console.warn(`⚠️ 项目列表查询较慢`, {
        userId: user.id,
        duration: `${duration.toFixed(1)}ms`,
        projectCount: projects.length
      });
    }
    
    return NextResponse.json(
      {
        ok: true,
        projects,
        meta: {
          total: projects.length,
          limit,
          offset,
          duration: Math.round(duration),
          // 🔧 客户端缓存建议
          cacheHint: duration < 200 ? 'fast' : duration < 500 ? 'normal' : 'slow'
        }
      },
      { 
        status: 200,
        headers: {
          // 🚀 客户端缓存控制
          'Cache-Control': 'private, max-age=60, stale-while-revalidate=300',
          'X-Response-Time': `${duration.toFixed(1)}ms`,
          'X-Project-Count': projects.length.toString()
        }
      }
    );
  } catch (error) {
    const duration = performance.now() - startTime;
    
    console.error("❌ 获取项目列表API错误:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration: `${duration.toFixed(1)}ms`,
      stack: error instanceof Error ? error.stack?.substring(0, 500) : undefined
    });
    
    return NextResponse.json(
      {
        ok: false,
        projects: [],
        error: "服务器内部错误",
        meta: {
          duration: Math.round(duration),
          errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
        }
      },
      { status: 500 }
    );
  }
}

// 🚀 创建新项目API - 支持从首条消息创建
export async function POST(request: NextRequest) {
  const startTime = performance.now();
  
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      console.log('❌ POST /api/me/projects - 未授权: 缺少token');
      return NextResponse.json({ message: "请先登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      console.log('❌ POST /api/me/projects - 未授权: 无效token');
      return NextResponse.json({ message: "请先登录" }, { status: 401 });
    }

    const { firstMessage, isCommunityProject, communityHtmlContent } = await request.json();

    if (!firstMessage || typeof firstMessage !== 'string') {
      console.log('❌ POST /api/me/projects - 缺少首条消息');
      return NextResponse.json(
        { message: "首条消息是必需的" },
        { status: 400 }
      );
    }

    // 🎯 社区项目特殊验证
    if (isCommunityProject && (!communityHtmlContent || typeof communityHtmlContent !== 'string')) {
      console.log('❌ 社区项目缺少HTML内容');
      return NextResponse.json(
        { message: "社区项目必须包含HTML内容" },
        { status: 400 }
      );
    }

    console.log('🚀 开始创建项目', {
      userId: user.id,
      messageLength: firstMessage.length,
      messagePreview: firstMessage.substring(0, 50) + '...',
      isCommunityProject: !!isCommunityProject
    });

    // 🔧 根据项目类型使用不同的创建逻辑
    let project;
    if (isCommunityProject) {
      // 🎯 社区项目：直接使用提供的HTML内容
      project = await createSimpleProject(
        user.id,
        communityHtmlContent, // 使用社区项目的HTML内容
        [firstMessage] // 提示词数组
      );

      // 🎯 为社区项目创建初始对话历史
      if (project) {
        await addCommunityProjectConversation(project.id, user.id, firstMessage, communityHtmlContent);
      }
    } else {
      // 🔧 普通项目：使用占位符HTML
      project = await createSimpleProject(
        user.id,
        '<div>正在生成...</div>', // 初始HTML内容
        [firstMessage] // 提示词数组
      );
    }
    
    if (!project) {
      console.log('❌ 项目创建失败');
      return NextResponse.json(
        { 
          success: false,
          error: "项目创建失败，请重试" 
        },
        { status: 500 }
      );
    }

    const duration = performance.now() - startTime;
    
    console.log('✅ 项目创建成功', {
      projectId: project.id,
      title: project.title,
      duration: `${duration.toFixed(1)}ms`
    });

    // 🎯 性能监控
    if (duration > 2000) {
      console.warn(`⚠️ 项目创建较慢`, {
        userId: user.id,
        duration: `${duration.toFixed(1)}ms`,
        projectId: project.id
      });
    }

    return NextResponse.json(
      {
        success: true,
        project: {
          id: project.id,
          title: project.title,
          html_content: project.html_content,
          created_at: project.created_at,
          updated_at: project.updated_at
        },
        message: '项目创建成功'
      },
      { 
        status: 201,
        headers: {
          'X-Response-Time': `${duration.toFixed(1)}ms`,
          'X-Project-Id': project.id.toString()
        }
      }
    );

  } catch (error) {
    const duration = performance.now() - startTime;
    
    console.error('❌ 项目创建API错误:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration: `${duration.toFixed(1)}ms`,
      stack: error instanceof Error ? error.stack?.substring(0, 500) : undefined
    });
    
    return NextResponse.json(
      {
        success: false,
        error: "服务器内部错误",
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}
