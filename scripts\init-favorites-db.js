// 加载环境变量
require('dotenv').config();

const mysql = require('mysql2/promise');

// 数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'loomrun'
};

async function initFavoritesTable() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    connection = await mysql.createConnection(DB_CONFIG);
    
    console.log('📋 创建收藏表...');
    
    // 创建收藏表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS project_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        project_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_project (user_id, project_id),
        INDEX idx_user_id (user_id),
        INDEX idx_project_id (project_id),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ 收藏表创建成功');
    
    // 检查表是否存在
    const [rows] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'project_favorites'
    `, [DB_CONFIG.database]);
    
    if (rows[0].count > 0) {
      console.log('✅ 收藏表验证成功');
    } else {
      console.log('❌ 收藏表验证失败');
    }
    
  } catch (error) {
    console.error('❌ 初始化收藏表失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initFavoritesTable()
    .then(() => {
      console.log('🎉 收藏功能数据库初始化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 收藏功能数据库初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initFavoritesTable }; 