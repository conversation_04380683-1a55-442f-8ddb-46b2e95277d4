import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 测试数据库连接和表结构
    const tests = [];

    // 1. 测试 points_transactions 表
    try {
      const transactionsCount = await executeQuery(
        'SELECT COUNT(*) as count FROM points_transactions WHERE user_id = ?',
        [user.id]
      ) as { count: number }[];
      
      tests.push({
        table: 'points_transactions',
        status: 'success',
        count: transactionsCount[0]?.count || 0,
        message: '积分交易记录表正常'
      });
    } catch (error) {
      tests.push({
        table: 'points_transactions',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '积分交易记录表查询失败'
      });
    }

    // 2. 测试 user_points_balance 表
    try {
      const balanceCount = await executeQuery(
        'SELECT COUNT(*) as count FROM user_points_balance WHERE user_id = ?',
        [user.id]
      ) as { count: number }[];
      
      tests.push({
        table: 'user_points_balance',
        status: 'success',
        count: balanceCount[0]?.count || 0,
        message: '用户积分余额表正常'
      });
    } catch (error) {
      tests.push({
        table: 'user_points_balance',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '用户积分余额表查询失败'
      });
    }

    // 3. 测试 points_consumption_log 表
    try {
      const consumptionCount = await executeQuery(
        'SELECT COUNT(*) as count FROM points_consumption_log WHERE user_id = ?',
        [user.id]
      ) as { count: number }[];
      
      tests.push({
        table: 'points_consumption_log',
        status: 'success',
        count: consumptionCount[0]?.count || 0,
        message: '积分消费日志表正常'
      });
    } catch (error) {
      tests.push({
        table: 'points_consumption_log',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '积分消费日志表查询失败'
      });
    }

    // 4. 测试用户基本信息
    try {
      const userInfo = await executeQuery(
        'SELECT id, points, total_earned_points, total_spent_points FROM users WHERE id = ?',
        [user.id]
      ) as any[];
      
      tests.push({
        table: 'users',
        status: 'success',
        data: userInfo[0] || {},
        message: '用户信息查询正常'
      });
    } catch (error) {
      tests.push({
        table: 'users',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '用户信息查询失败'
      });
    }

    // 5. 测试最近的积分交易记录
    try {
      const recentTransactions = await executeQuery(
        `SELECT 
          id, transaction_type, points_amount, source_type, points_type, 
          description, created_at, expires_at
         FROM points_transactions 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 5`,
        [user.id]
      ) as any[];
      
      tests.push({
        table: 'recent_transactions',
        status: 'success',
        data: recentTransactions,
        count: recentTransactions.length,
        message: '最近交易记录查询正常'
      });
    } catch (error) {
      tests.push({
        table: 'recent_transactions',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '最近交易记录查询失败'
      });
    }

    // 6. 测试积分余额详情
    try {
      const balanceDetails = await executeQuery(
        `SELECT 
          points_type, 
          SUM(points_amount) as total_points,
          COUNT(*) as record_count,
          MIN(expires_at) as earliest_expiry
         FROM user_points_balance 
         WHERE user_id = ? AND is_active = 1 AND points_amount > 0
         GROUP BY points_type`,
        [user.id]
      ) as any[];
      
      tests.push({
        table: 'balance_summary',
        status: 'success',
        data: balanceDetails,
        count: balanceDetails.length,
        message: '积分余额汇总查询正常'
      });
    } catch (error) {
      tests.push({
        table: 'balance_summary',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '积分余额汇总查询失败'
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        testResults: tests,
        summary: {
          totalTests: tests.length,
          successCount: tests.filter(t => t.status === 'success').length,
          errorCount: tests.filter(t => t.status === 'error').length
        }
      }
    });

  } catch (error) {
    console.error("数据库测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "数据库测试失败",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
