"use client";

import { useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Undo2, Redo2, History } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface UndoRedoToolbarProps {
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  history?: Array<{
    action: string;
    timestamp: number;
  }>;
  currentIndex?: number;
  onJumpToState?: (index: number) => void;
  className?: string;
}

export function UndoRedoToolbar({
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  history = [],
  currentIndex = 0,
  onJumpToState,
  className
}: UndoRedoToolbarProps) {

  const formatTime = useCallback((timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }, []);

  const getRelativeTime = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return `${Math.floor(diff / 86400000)}天前`;
  }, []);

  return (
    <TooltipProvider>
      <div className={cn("flex items-center gap-1", className)}>
        {/* 撤销按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onUndo}
              disabled={!canUndo}
              className={cn(
                "h-8 w-8 p-0 transition-all duration-200",
                "hover:bg-muted hover:scale-105",
                "disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100",
                canUndo && "text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              )}
            >
              <Undo2 className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <div className="text-xs">
              <div className="font-medium">撤销 (Ctrl+Z)</div>
              {canUndo && history[currentIndex - 1] && (
                <div className="text-muted-foreground mt-1">
                  回到: {history[currentIndex - 1].action}
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {/* 重做按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRedo}
              disabled={!canRedo}
              className={cn(
                "h-8 w-8 p-0 transition-all duration-200",
                "hover:bg-muted hover:scale-105",
                "disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:scale-100",
                canRedo && "text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
              )}
            >
              <Redo2 className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <div className="text-xs">
              <div className="font-medium">重做 (Ctrl+Y)</div>
              {canRedo && history[currentIndex + 1] && (
                <div className="text-muted-foreground mt-1">
                  前进到: {history[currentIndex + 1].action}
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {/* 历史记录下拉菜单 */}
        {history.length > 1 && (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground transition-colors"
              >
                <History className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent 
              className="w-80 p-0" 
              align="start"
              side="bottom"
            >
              <div className="p-3 border-b border-border">
                <h4 className="font-medium text-sm">编辑历史</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  点击任意状态快速跳转
                </p>
              </div>
              
              <div className="max-h-64 overflow-y-auto">
                {history.map((state, index) => (
                  <button
                    key={index}
                    onClick={() => onJumpToState?.(index)}
                    className={cn(
                      "w-full px-3 py-2 text-left hover:bg-muted transition-colors",
                      "border-l-2 border-transparent",
                      index === currentIndex && "bg-muted/50 border-l-blue-500"
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className={cn(
                          "text-sm truncate",
                          index === currentIndex ? "font-medium text-foreground" : "text-muted-foreground"
                        )}>
                          {state.action}
                        </div>
                        <div className="text-xs text-muted-foreground mt-0.5">
                          {formatTime(state.timestamp)} • {getRelativeTime(state.timestamp)}
                        </div>
                      </div>
                      
                      {index === currentIndex && (
                        <div className="ml-2 w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="p-3 border-t border-border bg-muted/30">
                <div className="text-xs text-muted-foreground">
                  共 {history.length} 个状态 • 当前位置: {currentIndex + 1}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    </TooltipProvider>
  );
}
