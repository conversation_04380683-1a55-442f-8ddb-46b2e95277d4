/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Rocket } from "lucide-react";
import classNames from "classnames";

import Loading from "@/components/loading";
import { Button } from "@/components/ui/button";

import { AuthModal } from "@/components/auth-modal";
import DeployModal from "@/components/editor/deploy-modal";
import { useUser } from "@/loomrunhooks/useUser";

export function DeployButton({
  html,
  prompts,
  project,
}: {
  html: string;
  prompts: string[];
  project?: any;
}) {
    const { user, refreshUser } = useUser();
  const [loading] = useState(false); // 保留loading状态用于UI显示
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [deployModalOpen, setDeployModalOpen] = useState(false);

  // 提取HTML中的title作为默认项目名称
  const extractTitle = (htmlContent: string): string => {
    const titleMatch = htmlContent?.match(/<title[^>]*>(.*?)<\/title>/i);
    const extractedTitle = titleMatch ? titleMatch[1].trim() : '';
    return extractedTitle || '我的网站';
  };

  const defaultTitle = extractTitle(html);
  const hasContent = html && html.trim().length > 0;
  const isUpdate = project?.id;

  // 添加调试信息
  useEffect(() => {
    console.log('🔍 DeployButton state:', {
      userExists: !!user?.id,
      userId: user?.id,
      projectExists: !!project?.id,
      projectId: project?.id,
      htmlLength: html?.length || 0,
      hasContent,
      defaultTitle,
      promptsCount: prompts?.length || 0,
      isUpdate
    });
  }, [user, html, prompts, project, hasContent, defaultTitle, isUpdate]);

  // 主要部署处理函数
  const handleDeployClick = async () => {
    console.log('🚀 Deploy button clicked...', { 
      hasUser: !!user?.id,
      hasContent,
      isUpdate
    });

    if (!hasContent) {
      toast.error("编辑器中没有内容，无法部署项目");
      return;
    }

    if (!user?.id) {
      // 用户未登录，显示登录模态框
      console.log('👤 User not logged in, showing auth modal');
      setAuthModalOpen(true);
      return;
    }

    // 用户已登录，显示部署模态框
    console.log('✅ User logged in, showing deploy modal');
    setDeployModalOpen(true);
  };





  // 登录成功后的处理
  const handleAuthSuccess = async () => {
    console.log('🎉 Login successful, refreshing user state...');
    setAuthModalOpen(false);
    
    try {
      const result = await refreshUser();
      const updatedUser = result.data;
      
      if (updatedUser?.id) {
        console.log('✅ User state refreshed, showing deploy modal');
        // 短暂延迟后显示部署模态框
        setTimeout(() => {
          setDeployModalOpen(true);
        }, 200);
      } else {
        console.log('⚠️ User state not updated, retrying...');
        setTimeout(async () => {
          const retryResult = await refreshUser();
          if (retryResult.data?.id) {
            setDeployModalOpen(true);
          } else {
            toast.error('登录状态更新失败，请重新尝试');
          }
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error refreshing user state:', error);
      toast.error('登录成功但状态更新失败，请重新尝试');
    }
  };

  return (
    <div className="flex items-center justify-end gap-5">
      <div className="relative flex items-center justify-end">
        {/* 桌面端按钮 */}
        <Button 
          variant="outline" 
          size="sm"
          className={classNames(
            "max-lg:hidden relative overflow-hidden group",
            "bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm border-blue-500/50",
            "hover:from-blue-500/90 hover:to-purple-500/90 hover:border-blue-400/70",
            "text-white hover:text-blue-100",
            "shadow-lg hover:shadow-blue-500/25 transition-all duration-200",
            "transform hover:scale-105 hover:-translate-y-0.5",
            "disabled:opacity-50 disabled:scale-100 disabled:translate-y-0",
            "px-2.5 py-0.5 h-6 rounded-md",
            {
              "cursor-not-allowed": !hasContent,
            }
          )}
          onClick={handleDeployClick}
          disabled={loading || !hasContent}
          title={!hasContent ? "编辑器中没有内容，无法部署项目" : (isUpdate ? "发布项目" : "部署项目")}
        >
          {/* 发光边框效果 */}
          <div className="absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
          
          <div className="relative z-10 flex items-center gap-1">
            <Rocket className="size-3 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-12" />
            <span className="font-semibold text-xs">
              {loading ? "发布中..." : (!hasContent ? "暂无内容" : isUpdate ? "发布" : "部署")}
            </span>
            {loading && <Loading className="ml-1 size-2.5 animate-spin" />}
          </div>
        </Button>
        
        {/* 移动端按钮 */}
        <Button 
          variant="outline" 
          size="sm" 
          className={classNames(
            "lg:hidden relative overflow-hidden group",
            "bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm border-blue-500/50",
            "hover:from-blue-500/90 hover:to-purple-500/90 hover:border-blue-400/70",
            "text-white hover:text-blue-100",
            "shadow-lg hover:shadow-blue-500/25 transition-all duration-200",
            "transform hover:scale-105 hover:-translate-y-0.5",
            "disabled:opacity-50 disabled:scale-100 disabled:translate-y-0",
            "w-6 h-6 p-0 rounded-md",
            {
              "cursor-not-allowed": !hasContent,
            }
          )}
          onClick={handleDeployClick}
          disabled={loading || !hasContent}
          title={!hasContent ? "编辑器中没有内容，无法部署项目" : (isUpdate ? "发布项目" : "部署项目")}
        >
          {/* 发光边框效果 */}
          <div className="absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
          
          <div className="relative z-10 flex items-center justify-center">
            {loading ? (
              <Loading className="size-2.5 animate-spin" />
            ) : (
              <Rocket className="size-2.5 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-12" />
            )}
          </div>
        </Button>
        
        {/* 登录模态框 */}
        <AuthModal
          open={authModalOpen}
          onClose={() => setAuthModalOpen(false)}
          onSuccess={handleAuthSuccess}
          title="登录以部署项目"
                      description="请登录您的 LoomRun 账号以部署您的精彩项目"
        />
        
        {/* 部署模态框 */}
        <DeployModal
          isOpen={deployModalOpen}
          onClose={() => setDeployModalOpen(false)}
          htmlContent={html}
        />
      </div>
    </div>
  );
}
