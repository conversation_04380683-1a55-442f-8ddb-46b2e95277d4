/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 01/08/2025 04:09:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for subscription_plans
-- ----------------------------
DROP TABLE IF EXISTS `subscription_plans`;
CREATE TABLE `subscription_plans`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `plan_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `plan_type` enum('free','pro','max') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'free',
  `plan_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration_months` int NOT NULL,
  `original_price` decimal(10, 2) NOT NULL,
  `discount_price` decimal(10, 2) NOT NULL,
  `points_included` int NOT NULL DEFAULT 0,
  `points_validity_days` int NOT NULL DEFAULT 30,
  `features` json NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `display_order` int NOT NULL DEFAULT 999,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_plan_key`(`plan_key` ASC) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_display_order`(`display_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of subscription_plans
-- ----------------------------
INSERT INTO `subscription_plans` VALUES (10, 'free_monthly', 'free', '免费版', 1, 0.00, 0.00, 500, 30, '{\"exports\": \"limited\", \"ai_requests\": \"limited\", \"monthly_points\": 50, \"priority_support\": false}', 1, 1, '2025-07-31 02:46:05', '2025-08-01 00:51:37');
INSERT INTO `subscription_plans` VALUES (11, 'pro_monthly', 'pro', 'Pro版月度', 1, 29.90, 19.90, 2000, 30, '{\"exports\": \"unlimited\", \"ai_requests\": \"unlimited\", \"monthly_points\": 1000, \"advanced_models\": true, \"priority_support\": true}', 1, 2, '2025-07-31 02:46:05', '2025-08-01 00:51:37');
INSERT INTO `subscription_plans` VALUES (12, 'pro_yearly', 'pro', 'Pro版年度', 12, 358.80, 199.00, 2000, 30, '{\"exports\": \"unlimited\", \"ai_requests\": \"unlimited\", \"monthly_points\": 1000, \"advanced_models\": true, \"priority_support\": true}', 1, 3, '2025-07-31 02:46:05', '2025-08-01 00:51:37');
INSERT INTO `subscription_plans` VALUES (13, 'max_monthly', 'max', 'Max版月度', 1, 59.90, 59.90, 5500, 30, '{\"exports\": \"unlimited\", \"ai_requests\": \"unlimited\", \"custom_points\": true, \"monthly_points\": 2500, \"advanced_models\": true, \"priority_support\": true}', 1, 4, '2025-07-31 02:46:05', '2025-08-01 00:51:37');
INSERT INTO `subscription_plans` VALUES (14, 'max_yearly', 'max', 'Max版年度', 12, 718.80, 599.00, 5500, 30, '{\"exports\": \"unlimited\", \"ai_requests\": \"unlimited\", \"custom_points\": true, \"monthly_points\": 2500, \"advanced_models\": true, \"priority_support\": true}', 1, 5, '2025-07-31 02:46:05', '2025-08-01 00:51:37');

SET FOREIGN_KEY_CHECKS = 1;
