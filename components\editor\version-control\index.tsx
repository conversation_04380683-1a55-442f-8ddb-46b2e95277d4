"use client";

import { useState, useEffect } from "react";
import { ChevronDown, ChevronRight, Clock, GitBranch, CheckCircle2, Eye, Code2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { toast } from "sonner";
import classNames from "classnames";
import { But<PERSON> } from "@/components/ui/button";

interface ProjectVersion {
  id: string;
  version_number: number;
  title: string;
  prompt: string;
  html_content: string;
  parent_version: number | null;
  is_active: boolean;
  created_at: string;
  metadata: {
    lines_changed: number;
    generation_time: number;
    model_used: string;
    is_modification: boolean;
  };
}

interface VersionControlProps {
  projectId: string;
  currentVersion: number;
  onVersionSelect: (version: ProjectVersion) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

export function VersionControl({
  projectId,
  currentVersion,
  onVersionSelect,
  isCollapsed,
  onToggleCollapse,
}: VersionControlProps) {
  const [versions, setVersions] = useState<ProjectVersion[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取版本列表
  const fetchVersions = async () => {
    if (!projectId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/versions`);
      if (response.ok) {
        const data = await response.json();
        setVersions(data.versions || []);
      }
    } catch (error) {
      console.error("Failed to fetch versions:", error);
      toast.error("加载版本历史失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVersions();
  }, [projectId]);

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return "刚刚";
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`;
    return date.toLocaleDateString("zh-CN");
  };

  // 预览版本
  const previewVersion = (version: ProjectVersion) => {
    const newWindow = window.open("", "_blank");
    if (newWindow) {
      newWindow.document.write(version.html_content);
      newWindow.document.close();
    }
  };

  if (isCollapsed) {
    return (
      <div className="flex flex-col h-full bg-[#171717] border-r border-neutral-700/50">
        {/* 收起状态的头部 */}
        <div className="p-3 border-b border-neutral-700/50">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-full justify-start gap-2 text-neutral-300 hover:text-white hover:bg-neutral-800/50"
          >
            <ChevronRight className="size-4" />
            <span className="text-sm font-medium">版本</span>
          </Button>
        </div>

        {/* 当前版本指示器 */}
        <div className="p-3 flex flex-col items-center gap-2">
          <div className="w-8 h-8 bg-blue-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center">
            <span className="text-xs font-bold text-blue-400">v{currentVersion}</span>
          </div>
          <div className="text-xs text-neutral-400 text-center">当前版本</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-[#171717] border-r border-neutral-700/50 w-60">
      {/* 头部 */}
      <div className="p-4 border-b border-neutral-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="p-1 h-auto text-neutral-400 hover:text-white"
            >
              <ChevronDown className="size-4" />
            </Button>
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-blue-500/20 rounded-lg">
                <GitBranch className="size-4 text-blue-400" />
              </div>
              <div>
                <h3 className="text-sm font-semibold text-white">版本历史</h3>
                <p className="text-xs text-neutral-400">管理您的代码版本</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 版本列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : versions.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-neutral-800 rounded-full flex items-center justify-center mx-auto mb-3">
              <Clock className="size-6 text-neutral-400" />
            </div>
            <p className="text-sm text-neutral-400">暂无版本历史</p>
          </div>
        ) : (
          versions.map((version) => (
            <div
              key={version.id}
              className={classNames(
                "group p-3 rounded-xl border transition-all duration-200 cursor-pointer hover:shadow-lg",
                {
                  "bg-blue-500/10 border-blue-500/30 shadow-blue-500/10": version.is_active,
                  "bg-neutral-800/50 border-neutral-700/50 hover:border-neutral-600 hover:bg-neutral-800": !version.is_active,
                }
              )}
              onClick={() => onVersionSelect(version)}
            >
              {/* 版本头部 */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div
                    className={classNames(
                      "px-2 py-1 rounded-md text-xs font-bold",
                      {
                        "bg-blue-500/20 text-blue-300": version.is_active,
                        "bg-neutral-700/50 text-neutral-300": !version.is_active,
                      }
                    )}
                  >
                    v{version.version_number}
                  </div>
                  {version.is_active && (
                    <CheckCircle2 className="size-4 text-blue-400" />
                  )}
                  {version.metadata.is_modification && (
                    <div className="px-1.5 py-0.5 bg-orange-500/20 text-orange-300 text-xs rounded-md">
                      修改
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      previewVersion(version);
                    }}
                    className="h-6 w-6 p-0 text-neutral-400 hover:text-white"
                  >
                    <Eye className="size-3" />
                  </Button>
                </div>
              </div>

              {/* 版本标题 */}
              <h4 className="text-sm font-medium text-white mb-1 line-clamp-1">
                {version.title}
              </h4>

              {/* 提示词预览 */}
              <p className="text-xs text-neutral-400 line-clamp-2 mb-2">
                {version.prompt}
              </p>

              {/* 版本信息 */}
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-3">
                  <span className="text-neutral-500 flex items-center gap-1">
                    <Clock className="size-3" />
                    {formatTime(version.created_at)}
                  </span>
                  {version.metadata.lines_changed > 0 && (
                    <span className="text-neutral-500 flex items-center gap-1">
                      <Code2 className="size-3" />
                      {version.metadata.lines_changed}行
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  <Sparkles className="size-3 text-blue-400" />
                  <span className="text-neutral-500">{version.metadata.model_used}</span>
                </div>
              </div>

              {/* 分支指示器 */}
              {version.parent_version && (
                <div className="mt-2 pt-2 border-t border-neutral-700/30">
                  <span className="text-xs text-neutral-500 flex items-center gap-1">
                    <GitBranch className="size-3" />
                    基于 v{version.parent_version}
                  </span>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
} 