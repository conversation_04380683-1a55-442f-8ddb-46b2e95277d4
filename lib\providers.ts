export const PROVIDERS = {
  "deepseek-official": {
    name: "DeepSeek Official",
    max_tokens: 8192,
    id: "deepseek-official",
    baseURL: "https://api.deepseek.com",
  },
  "doubao-official": {
    name: "Doubao Official",
    max_tokens: 32768,
    id: "doubao-official",
    baseURL: "https://ark.cn-beijing.volces.com/api/v3",
  },
  "gemini-official": {
    name: "Gemini Official",
    max_tokens: 200000,  // 🎯 基于测试结果，200K是最佳平衡点
    id: "gemini-official",
    baseURL: "https://imxzlpwclisz.ap-southeast-1.clawcloudrun.com",
  },
} as const;

type ProviderKey = keyof typeof PROVIDERS;

export const MODELS = [
  {
    value: "deepseek-chat",
    label: "DeepSeek V3 Chat",
    providers: ["deepseek-official"] as Provider<PERSON><PERSON>[],
    autoProvider: "deepseek-official" as Provider<PERSON><PERSON>,
  },
  {
    value: "deepseek-reasoner",
    label: "DeepSeek R1 Reasoner",
    providers: ["deepseek-official"] as ProviderKey[],
    autoProvider: "deepseek-official" as ProviderKey,
    isNew: true,
    isThinker: true,
  },
  {
    value: "doubao-seed-1-6-250615",
    label: "Doubao Vision Pro",
    providers: ["doubao-official"] as ProviderKey[],
    autoProvider: "doubao-official" as ProviderKey,
    supportsImages: true,
  },
  {
    value: "claude-3.5-sonnet",
    label: "Claude-3.5 Sonnet",
    providers: [] as ProviderKey[],
    autoProvider: "" as string,
    isComingSoon: true,
  },
  {
    value: "claude-4-sonnet",
    label: "Claude-4 Sonnet",
    providers: [] as ProviderKey[],
    autoProvider: "" as string,
    isComingSoon: true,
  },
  {
    value: "gemini-2.5-flash",
    label: "Gemini 2.5 Flash",
    providers: ["gemini-official"] as ProviderKey[],
    autoProvider: "gemini-official" as ProviderKey,
    supportsImages: true,
  },
];
