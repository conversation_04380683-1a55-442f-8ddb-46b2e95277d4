/**
 * 🚀 高性能缓存管理器
 * 专门用于优化项目创建和管理流程的性能
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

class PerformanceCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private stats = {
    hits: 0,
    misses: 0
  };

  // 🎯 缓存项目创建状态（防重复）
  private creationStates = new Map<string, boolean>();
  
  // 🎯 缓存HTML内容哈希（快速重复检测）
  private htmlHashes = new Map<string, string>();

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, ttl: number = 60000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    this.cleanup();
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    this.stats.hits++;
    return entry.data as T;
  }

  /**
   * 🚀 高性能HTML内容哈希生成
   */
  generateContentHash(html: string): string {
    // 使用简单但高效的哈希算法
    let hash = 0;
    for (let i = 0; i < html.length; i++) {
      const char = html.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * 🎯 检查内容是否重复
   */
  isContentDuplicate(html: string): boolean {
    const hash = this.generateContentHash(html);
    const existingHash = this.htmlHashes.get('current');
    
    if (existingHash === hash) {
      return true;
    }
    
    this.htmlHashes.set('current', hash);
    return false;
  }

  /**
   * 🎯 设置项目创建状态
   */
  setCreationState(key: string, isCreating: boolean): void {
    if (isCreating) {
      this.creationStates.set(key, true);
    } else {
      this.creationStates.delete(key);
    }
  }

  /**
   * 🎯 检查是否正在创建
   */
  isCreating(key: string): boolean {
    return this.creationStates.has(key);
  }

  /**
   * 🧹 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 📊 获取缓存统计信息
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      size: this.cache.size,
      hitRate: total > 0 ? (this.stats.hits / total) * 100 : 0
    };
  }

  /**
   * 🔄 重置统计信息
   */
  resetStats(): void {
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * 🧹 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.creationStates.clear();
    this.htmlHashes.clear();
    this.resetStats();
  }

  /**
   * 🎯 批量预热缓存
   */
  warmup(entries: Array<{ key: string; data: unknown; ttl?: number }>): void {
    entries.forEach(({ key, data, ttl = 60000 }) => {
      this.set(key, data, ttl);
    });
  }
}

// 🚀 单例实例
export const performanceCache = new PerformanceCache();

// 🎯 社区卡片专用缓存优化
export class CommunityCardCache {
  private static instance: CommunityCardCache;
  private cardCache = new Map<string, CacheEntry<unknown>>();
  private imageCache = new Map<string, string>();
  private renderCache = new Map<string, HTMLElement>();
  private scrollPositionCache = new Map<string, number>();
  
  static getInstance(): CommunityCardCache {
    if (!CommunityCardCache.instance) {
      CommunityCardCache.instance = new CommunityCardCache();
    }
    return CommunityCardCache.instance;
  }

  /**
   * 🎯 缓存社区项目数据
   */
  cacheProjectData(projectId: number, data: unknown, ttl: number = 300000): void {
    this.cardCache.set(`project_${projectId}`, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 🎯 获取缓存的项目数据
   */
  getCachedProjectData(projectId: number): unknown | null {
    const entry = this.cardCache.get(`project_${projectId}`);
    if (!entry || Date.now() - entry.timestamp > entry.ttl) {
      this.cardCache.delete(`project_${projectId}`);
      return null;
    }
    return entry.data;
  }

  /**
   * 🎯 缓存用户头像URL
   */
  cacheUserAvatar(userId: number, avatarUrl: string): void {
    this.imageCache.set(`avatar_${userId}`, avatarUrl);
  }

  /**
   * 🎯 获取缓存的用户头像
   */
  getCachedUserAvatar(userId: number): string | null {
    return this.imageCache.get(`avatar_${userId}`) || null;
  }

  /**
   * 🎯 缓存渲染结果
   */
  cacheRenderResult(key: string, element: HTMLElement): void {
    this.renderCache.set(key, element.cloneNode(true) as HTMLElement);
  }

  /**
   * 🎯 获取缓存的渲染结果
   */
  getCachedRenderResult(key: string): HTMLElement | null {
    const cached = this.renderCache.get(key);
    return cached ? cached.cloneNode(true) as HTMLElement : null;
  }

  /**
   * 🎯 缓存滚动位置
   */
  cacheScrollPosition(containerId: string, position: number): void {
    this.scrollPositionCache.set(containerId, position);
  }

  /**
   * 🎯 获取缓存的滚动位置
   */
  getCachedScrollPosition(containerId: string): number | null {
    return this.scrollPositionCache.get(containerId) || null;
  }

  /**
   * 🧹 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cardCache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cardCache.delete(key);
      }
    }
    
    // 限制缓存大小
    if (this.renderCache.size > 100) {
      const keys = Array.from(this.renderCache.keys());
      keys.slice(0, 50).forEach(key => this.renderCache.delete(key));
    }
  }

  /**
   * 🔄 清空所有缓存
   */
  clear(): void {
    this.cardCache.clear();
    this.imageCache.clear();
    this.renderCache.clear();
    this.scrollPositionCache.clear();
  }
}

// 🎯 社区卡片缓存实例
export const communityCardCache = CommunityCardCache.getInstance();

// 🎯 性能优化工具函数
export const optimizeProjectCreation = {
  /**
   * 防抖函数 - 减少频繁调用
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  /**
   * 节流函数 - 限制调用频率
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 批量操作优化
   */
  batchOperations<T>(
    operations: Array<() => Promise<T>>,
    batchSize: number = 3
  ): Promise<T[]> {
    const batches: Array<Array<() => Promise<T>>> = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      batches.push(operations.slice(i, i + batchSize));
    }

    return batches.reduce(async (acc, batch) => {
      const results = await acc;
      const batchResults = await Promise.all(batch.map(op => op()));
      return [...results, ...batchResults];
    }, Promise.resolve([] as T[]));
  }
}; 