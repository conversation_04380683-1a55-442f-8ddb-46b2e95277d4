import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取用户积分余额详情
    const balances = await executeQuery(
      `SELECT 
        id,
        points_type,
        points_amount,
        expires_at,
        created_at,
        source_order_id,
        source_plan_key
       FROM user_points_balance 
       WHERE user_id = ? AND is_active = 1 AND points_amount > 0
       ORDER BY 
         CASE 
           WHEN expires_at IS NULL THEN 1 
           ELSE 0 
         END,
         expires_at ASC,
         created_at ASC`,
      [user.id]
    );

    // 按积分类型分组统计
    const summary = await executeQuery(
      `SELECT 
        points_type,
        SUM(points_amount) as total_points,
        COUNT(*) as record_count,
        MIN(expires_at) as earliest_expiry
       FROM user_points_balance 
       WHERE user_id = ? AND is_active = 1 AND points_amount > 0
       GROUP BY points_type
       ORDER BY 
         CASE points_type 
           WHEN 'subscription' THEN 1
           WHEN 'activity' THEN 2
           WHEN 'recharge' THEN 3
           ELSE 4
         END`,
      [user.id]
    );

    // 获取即将过期的积分
    const expiringSoon = await executeQuery(
      `SELECT 
        points_type,
        SUM(points_amount) as expiring_points
       FROM user_points_balance 
       WHERE user_id = ? 
         AND is_active = 1 
         AND points_amount > 0
         AND expires_at IS NOT NULL
         AND expires_at <= DATE_ADD(NOW(), INTERVAL 7 DAY)
         AND expires_at > NOW()
       GROUP BY points_type`,
      [user.id]
    );

    return NextResponse.json({
      success: true,
      data: {
        balances,
        summary,
        expiringSoon,
        totalPoints: user.points || 0
      }
    });

  } catch (error) {
    console.error("获取积分余额失败:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}
