"use client";

import { useState } from "react";
import { Maximize2, <PERSON>, Code2, <PERSON><PERSON><PERSON>, <PERSON>, ExternalLink } from "lucide-react";
import classNames from "classnames";
import { Button } from "@/components/ui/button";

interface VersionPreviewCardProps {
  version: number;
  title: string;
  prompt: string;
  htmlContent: string;
  timestamp: string;
  isGenerating?: boolean;
  onExpand: () => void;
  onPreview: () => void;
  onViewCode: () => void;
}

export function VersionPreviewCard({
  version,
  title,
  prompt,
  htmlContent,
  timestamp,
  isGenerating = false,
  onExpand,
  onPreview,
  onViewCode,
}: VersionPreviewCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 生成HTML预览的缩略图iframe
  const renderPreview = () => {
    if (isGenerating) {
      return (
        <div className="w-full h-48 bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-lg border border-neutral-700 flex flex-col items-center justify-center relative overflow-hidden">
          {/* 背景动画效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 animate-pulse"></div>
          
          <div className="relative z-10">
            <div className="w-10 h-10 border-3 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <Sparkles className="absolute inset-0 w-10 h-10 text-blue-400 animate-pulse" />
          </div>
          
          <div className="text-center mt-4 relative z-10">
            <p className="text-sm font-medium text-blue-300 animate-pulse">
              LoomRun 正在编织您的代码...
            </p>
            <div className="flex items-center justify-center gap-1 mt-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full h-48 bg-background rounded-lg border border-border overflow-hidden group">
        {/* HTML预览iframe */}
        <iframe
          srcDoc={htmlContent}
          sandbox="allow-scripts"
          className="w-full h-full border-0 pointer-events-none scale-50 origin-top-left"
          style={{
            width: "200%",
            height: "200%",
            transform: "scale(0.5)",
            transformOrigin: "top left",
          }}
          title={`Version ${version} Preview`}
        />
        
        {/* 悬停遮罩和操作按钮 */}
        <div 
          className={classNames(
            "absolute inset-0 bg-black/40 backdrop-blur-sm transition-all duration-200 flex items-center justify-center gap-2",
            {
              "opacity-0": !isHovered,
              "opacity-100": isHovered,
            }
          )}
        >
          <Button
            variant="secondary"
            size="sm"
            onClick={onPreview}
            className="bg-background/90 text-foreground hover:bg-background gap-2"
          >
            <Eye className="size-4" />
            预览
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={onViewCode}
            className="bg-background/90 text-foreground hover:bg-background gap-2"
          >
            <Code2 className="size-4" />
            代码
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={onExpand}
            className="bg-blue-600 text-white hover:bg-blue-700 gap-2"
          >
            <Maximize2 className="size-4" />
            展开
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div 
      data-version={version}
      className={classNames(
        "bg-white dark:bg-neutral-900 border rounded-2xl p-4 max-w-md mx-auto transition-all duration-500",
        {
          "border-gray-200 dark:border-neutral-700": isGenerating,
          "border-green-500/30 shadow-green-500/10 shadow-lg": !isGenerating && htmlContent,
        }
      )}
    >
      {/* 卡片头部 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={classNames(
            "px-2 py-1 text-xs font-bold rounded-md transition-all duration-300",
            {
              "bg-blue-500/20 text-blue-300": isGenerating,
              "bg-green-500/20 text-green-300": !isGenerating && htmlContent,
            }
          )}>
            v{version}
          </div>
          <h3 className="text-sm font-semibold text-gray-800 dark:text-white">{title}</h3>
          {!isGenerating && htmlContent && (
            <div className="flex items-center gap-1 text-green-500 dark:text-green-400 animate-fade-in">
              <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium">完成</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-neutral-400">
          <Clock className="size-3" />
          {timestamp}
        </div>
      </div>

      {/* 提示词 */}
      <div className="mb-4">
        <p className="text-xs text-gray-600 dark:text-neutral-400 line-clamp-2 leading-relaxed">
          &ldquo;{prompt}&rdquo;
        </p>
      </div>

      {/* 预览区域 */}
      <div
        className="relative cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={onExpand}
      >
        {renderPreview()}
      </div>

      {/* 底部操作栏 */}
      <div className="flex items-center justify-between mt-4 pt-3 border-t border-neutral-700/50">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onPreview}
            className="text-neutral-400 hover:text-white gap-1.5 px-2"
          >
            <ExternalLink className="size-3" />
            新窗口预览
          </Button>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onExpand}
          className="border-neutral-600 text-neutral-300 hover:text-white hover:border-neutral-500 gap-1.5"
        >
          <Maximize2 className="size-3" />
          使用此版本
        </Button>
      </div>
    </div>
  );
} 