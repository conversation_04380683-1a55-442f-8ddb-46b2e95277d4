"use client";
import { useState, useEffect } from "react";

interface GenerationLayoutProps {
  prompt: string;
  html: string;
  isGenerating: boolean;
  onGenerationComplete?: () => void;
  children?: React.ReactNode;
}

export function GenerationLayout({ 
  prompt, 
  html, 
  isGenerating,
  onGenerationComplete,
  children 
}: GenerationLayoutProps) {
  const [currentStage, setCurrentStage] = useState(0);
  const [stageProgress, setStageProgress] = useState(0);

  // AI生成阶段
  const stages = [
    { name: "初始化引擎", description: "启动AI织梦引擎", icon: "⚡" },
    { name: "语义解析", description: "解析用户需求语义", icon: "🧠" },
    { name: "构建结构", description: "设计页面架构布局", icon: "🏗️" },
    { name: "样式编译", description: "编译视觉样式代码", icon: "🎨" },
    { name: "代码优化", description: "优化性能与体验", icon: "⚡" }
  ];

  // 模拟AI生成进度
  useEffect(() => {
    if (!isGenerating) return;

    let stageIndex = 0;
    let progress = 0;

    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5; // 随机递增5-20%
      
      if (progress >= 100) {
        progress = 100;
        
        // 延迟进入下一阶段
        setTimeout(() => {
          stageIndex++;
          progress = 0;
          
          if (stageIndex >= stages.length) {
            clearInterval(interval);
            onGenerationComplete?.();
            return;
          }
          
          setCurrentStage(stageIndex);
          setStageProgress(0);
        }, 800);
      }
      
      setStageProgress(progress);
    }, 200);

    return () => clearInterval(interval);
  }, [isGenerating, onGenerationComplete]);

  return (
    <div className="h-[100dvh] bg-slate-900 flex">
      {/* 左侧：AI监控面板 */}
      <div className="w-72 bg-gradient-to-b from-slate-800 to-slate-900 border-r border-slate-700/50 flex flex-col"> {/* 320px缩小10% = 288px ≈ w-72 */}
        {/* 头部状态 */}
        <div className="p-6 border-b border-slate-700/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-3 h-3 bg-cyan-400 rounded-full animate-pulse" />
            <span className="text-cyan-400 font-medium text-sm">AI 织梦引擎运行中</span>
          </div>
          
          <h2 className="text-white text-lg font-semibold mb-2">生成项目</h2>
          <p className="text-slate-300 text-sm bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
            {prompt}
          </p>
        </div>

        {/* 生成阶段监控 */}
        <div className="flex-1 p-6">
          <h3 className="text-white font-medium mb-4 flex items-center gap-2">
            <span>🔄</span>
            处理阶段
          </h3>
          
          <div className="space-y-4">
                         {stages.map((stage, index) => {
               const isActive = index === currentStage && isGenerating;
               const isCompleted = index < currentStage;
              
              return (
                <div 
                  key={index}
                  className={`relative p-4 rounded-xl border transition-all duration-500 ${
                    isActive 
                      ? 'bg-cyan-500/10 border-cyan-400/50 ring-1 ring-cyan-400/30' 
                      : isCompleted
                      ? 'bg-emerald-500/10 border-emerald-400/30'
                      : 'bg-slate-800/30 border-slate-600/30'
                  }`}
                >
                  {/* 扫描线动画 - 仅在激活状态显示 */}
                  {isActive && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      <div className="h-[2px] bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-scan-line" />
                    </div>
                  )}
                  
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{stage.icon}</span>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className={`font-medium text-sm ${
                          isActive ? 'text-cyan-400' : 
                          isCompleted ? 'text-emerald-400' : 
                          'text-slate-400'
                        }`}>
                          {stage.name}
                        </h4>
                        
                        {/* 状态指示器 */}
                        {isCompleted && <span className="text-emerald-400 text-xs">✓</span>}
                        {isActive && <span className="text-cyan-400 text-xs animate-pulse">●</span>}
                      </div>
                      
                      <p className="text-slate-400 text-xs mb-2">{stage.description}</p>
                      
                      {/* 进度条 */}
                      {isActive && (
                        <div className="w-full bg-slate-700 rounded-full h-1">
                          <div 
                            className="bg-gradient-to-r from-cyan-400 to-blue-400 h-1 rounded-full transition-all duration-200"
                            style={{ width: `${stageProgress}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 底部统计 */}
        <div className="p-6 border-t border-slate-700/50">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="bg-slate-800/50 rounded-lg p-3">
              <div className="text-cyan-400 font-mono text-lg">{currentStage + 1}</div>
              <div className="text-slate-400 text-xs">当前阶段</div>
            </div>
            <div className="bg-slate-800/50 rounded-lg p-3">
              <div className="text-purple-400 font-mono text-lg">{Math.round(stageProgress)}%</div>
              <div className="text-slate-400 text-xs">阶段进度</div>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧：预览区域 */}
      <div className="flex-1 bg-slate-900 relative">
        {/* 预览头部 */}
        <div className="h-16 bg-slate-800 border-b border-slate-700/50 flex items-center justify-between px-6">
          <div className="flex items-center gap-3">
            <span className="text-slate-300 font-medium">实时预览</span>
            {isGenerating && (
              <div className="flex items-center gap-2 text-cyan-400 text-sm">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                生成中...
              </div>
            )}
          </div>
          
          {/* 浏览器模拟按钮 */}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-400 rounded-full" />
            <div className="w-3 h-3 bg-yellow-400 rounded-full" />
            <div className="w-3 h-3 bg-green-400 rounded-full" />
          </div>
        </div>

        {/* 预览内容 */}
                        <div className="h-[calc(100%-64px)] bg-white relative overflow-hidden">
          {html ? (
            <iframe
              srcDoc={html}
              className="w-full h-full border-0"
              sandbox="allow-scripts allow-same-origin"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-slate-50">
              <div className="text-center">
                <div className="text-6xl mb-4 opacity-20">🎨</div>
                <p className="text-slate-400">等待AI生成内容...</p>
              </div>
            </div>
          )}
          
          {/* 生成中的加载遮罩 */}
          {isGenerating && (
            <div className="absolute inset-0 bg-slate-900/20 backdrop-blur-[1px] flex items-center justify-center">
              <div className="bg-slate-800/90 text-white px-6 py-3 rounded-xl border border-slate-600/50">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">AI正在编织您的网站...</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 自定义子组件区域 */}
        {children}
      </div>

      {/* CSS 动画 */}
      <style jsx>{`
        @keyframes scan-line {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100vw); }
        }
        
        .animate-scan-line {
          animation: scan-line 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
} 