import { NextRequest, NextResponse } from "next/server";
import { sendVerificationCode, loginWithPhone } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    // 生成测试手机号
    const testPhone = `139${Math.floor(10000000 + Math.random() * 90000000)}`;
    
    console.log(`🧪 开始完整登录测试，手机号: ${testPhone}`);

    // 步骤1: 发送验证码
    console.log(`📱 步骤1: 发送验证码...`);
    const sendResult = await sendVerificationCode(testPhone);
    
    if (!sendResult.success) {
      return NextResponse.json({
        success: false,
        message: "发送验证码失败",
        error: sendResult.message
      }, { status: 500 });
    }

    // 从返回消息中提取验证码
    const codeMatch = sendResult.message.match(/\[测试模式: (\d{6})\]/);
    if (!codeMatch) {
      return NextResponse.json({
        success: false,
        message: "无法提取验证码",
        error: "消息格式不正确"
      }, { status: 500 });
    }

    const code = codeMatch[1];
    console.log(`✅ 验证码发送成功: ${code}`);

    // 等待1秒确保数据库写入完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 步骤2: 验证验证码记录
    console.log(`🔍 步骤2: 检查验证码记录...`);
    const codeRecords = await executeQuery(
      `SELECT id, code, expires_at, is_used, created_at,
              TIMESTAMPDIFF(MINUTE, created_at, NOW()) as minutes_ago,
              (expires_at > NOW()) as is_not_expired
       FROM phone_verifications 
       WHERE phone = ? AND code = ?
       ORDER BY created_at DESC LIMIT 1`,
      [testPhone, code]
    ) as any[];

    if (codeRecords.length === 0) {
      return NextResponse.json({
        success: false,
        message: "验证码记录未找到",
        data: { phone: testPhone, code: code }
      }, { status: 500 });
    }

    console.log(`📋 验证码记录:`, codeRecords[0]);

    // 步骤3: 尝试登录
    console.log(`🔐 步骤3: 尝试登录...`);
    const ipAddress = '127.0.0.1';
    const userAgent = 'Test User Agent - Full Login Test';
    
    const loginResult = await loginWithPhone(testPhone, code, ipAddress, userAgent);

    if (loginResult) {
      console.log(`✅ 登录成功: 用户ID=${loginResult.user.id}`);
      
      // 步骤4: 检查积分记录
      const pointsHistory = await executeQuery(
        `SELECT pt.id, pt.transaction_type, pt.points_amount, pt.source_type, 
                pt.description, pt.created_at
         FROM points_transactions pt 
         WHERE pt.user_id = ? 
         ORDER BY pt.created_at DESC`,
        [loginResult.user.id]
      ) as any[];

      return NextResponse.json({
        success: true,
        message: "完整登录测试成功",
        data: {
          step1_sendCode: {
            success: sendResult.success,
            message: sendResult.message,
            extractedCode: code
          },
          step2_codeRecord: codeRecords[0],
          step3_login: {
            success: true,
            user: {
              id: loginResult.user.id,
              nickname: loginResult.user.nickname,
              phone: loginResult.user.phone,
              points: loginResult.user.points,
              invite_code: loginResult.user.invite_code
            }
          },
          step4_pointsHistory: pointsHistory,
          testInfo: {
            phone: testPhone,
            code: code,
            ipAddress: ipAddress,
            userAgent: userAgent
          }
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: "登录失败",
        data: {
          step1_sendCode: {
            success: sendResult.success,
            message: sendResult.message,
            extractedCode: code
          },
          step2_codeRecord: codeRecords[0],
          step3_login: {
            success: false,
            error: "loginWithPhone返回null"
          },
          testInfo: {
            phone: testPhone,
            code: code
          }
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error("完整登录测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
