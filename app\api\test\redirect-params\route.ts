import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const invite = searchParams.get('invite');
  
  console.log('🔍 [重定向测试] 收到的参数:', {
    invite,
    allParams: Object.fromEntries(searchParams.entries()),
    url: request.url
  });

  return NextResponse.json({
    success: true,
    invite,
    allParams: Object.fromEntries(searchParams.entries()),
    url: request.url,
    message: invite ? `邀请码 ${invite} 成功传递` : '没有邀请码参数'
  });
}
