import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取当前用户的积分交易记录
    const transactions = await executeQuery(
      `SELECT 
        id, transaction_type, points_amount, balance_before, balance_after,
        source_type, points_type, description, created_at, expires_at
      FROM points_transactions 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 20`,
      [user.id]
    ) as any[];

    // 获取当前用户的积分余额记录
    const balances = await executeQuery(
      `SELECT 
        id, points_type, points_amount, expires_at, is_active, created_at
      FROM user_points_balance 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10`,
      [user.id]
    ) as any[];

    return NextResponse.json({
      success: true,
      data: {
        currentUser: {
          id: user.id,
          nickname: user.nickname,
          points: user.points,
          total_earned_points: user.total_earned_points,
          total_spent_points: user.total_spent_points,
          created_at: user.created_at
        },
        transactions: transactions,
        balances: balances,
        summary: {
          transactionCount: transactions.length,
          balanceCount: balances.length,
          hasRegistrationBonus: transactions.some(t => t.source_type === 'registration')
        }
      }
    });

  } catch (error) {
    console.error("获取当前用户信息失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
