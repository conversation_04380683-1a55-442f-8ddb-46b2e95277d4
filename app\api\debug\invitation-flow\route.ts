import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";
import { validateInviteCode } from "@/lib/invitation-service";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    console.log('🔍 开始调试邀请流程...');

    // 1. 检查邀请码 WMQESB 对应的邀请者
    console.log('\n=== 1. 检查邀请码 WMQESB ===');
    const inviterRows = await executeQuery(
      'SELECT id, nickname, invite_code, invitation_count FROM users WHERE invite_code = ?',
      ['WMQESB']
    ) as any[];
    
    if (inviterRows.length === 0) {
      return NextResponse.json({
        success: false,
        error: '邀请码 WMQESB 不存在！',
        step: 1
      });
    }
    
    const inviter = inviterRows[0];
    console.log('✅ 邀请者信息:', inviter);

    // 2. 检查邀请设置
    console.log('\n=== 2. 检查邀请系统设置 ===');
    const settingsRows = await executeQuery(
      `SELECT setting_key, setting_value FROM system_settings 
       WHERE setting_key IN ('invitation_enabled', 'invitation_points_per_user', 'max_invitations_per_user') 
       AND is_active = 1`
    ) as any[];
    
    const settings: Record<string, string> = {};
    settingsRows.forEach((row: any) => {
      settings[row.setting_key] = row.setting_value;
    });
    
    console.log('📋 邀请设置:', settings);

    // 3. 验证邀请码
    console.log('\n=== 3. 验证邀请码 ===');
    const validation = await validateInviteCode('WMQESB');
    console.log('🔍 邀请码验证结果:', validation);

    // 4. 检查最新注册的用户（用户ID 34）
    console.log('\n=== 4. 检查最新注册用户 ===');
    const newUserRows = await executeQuery(
      'SELECT id, phone, nickname, invited_by_user_id, created_at FROM users WHERE id = 34'
    ) as any[];
    
    let newUser = null;
    if (newUserRows.length > 0) {
      newUser = newUserRows[0];
      console.log('👤 新用户信息:', newUser);
    } else {
      console.log('❌ 用户ID 34 不存在！');
    }

    // 5. 检查邀请记录
    console.log('\n=== 5. 检查邀请记录 ===');
    const invitationRecords = await executeQuery(
      'SELECT * FROM user_invitations WHERE inviter_user_id = ? OR invited_user_id = ?',
      [inviter.id, newUser?.id || 34]
    ) as any[];
    
    console.log('📝 邀请记录:', invitationRecords);

    // 6. 检查积分交易记录
    console.log('\n=== 6. 检查积分交易记录 ===');
    const transactionRows = await executeQuery(
      `SELECT * FROM points_transactions 
       WHERE user_id = ? AND source_type = 'invitation' 
       ORDER BY created_at DESC LIMIT 5`,
      [inviter.id]
    ) as any[];
    
    console.log('💰 邀请积分交易记录:', transactionRows);

    // 7. 检查新用户的注册积分记录
    let newUserTransactions = [];
    if (newUser) {
      newUserTransactions = await executeQuery(
        `SELECT * FROM points_transactions 
         WHERE user_id = ? AND source_type = 'registration' 
         ORDER BY created_at DESC LIMIT 3`,
        [newUser.id]
      ) as any[];
      
      console.log('🎁 新用户注册积分记录:', newUserTransactions);
    }

    // 8. 诊断问题
    const issues = [];
    
    if (settings.invitation_enabled !== '1') {
      issues.push('邀请功能已关闭');
    }
    
    if (!validation.valid) {
      issues.push(`邀请码验证失败: ${validation.message}`);
    }
    
    if (newUser && !newUser.invited_by_user_id) {
      issues.push('新用户的 invited_by_user_id 字段为空，说明邀请关系没有建立');
    }
    
    if (invitationRecords.length === 0) {
      issues.push('没有创建邀请记录，说明邀请处理逻辑没有执行');
    }
    
    if (transactionRows.length === 0) {
      issues.push('没有邀请积分交易记录，说明积分奖励没有发放');
    }

    return NextResponse.json({
      success: true,
      data: {
        inviter,
        settings,
        validation,
        newUser,
        invitationRecords,
        transactionRows,
        newUserTransactions,
        issues,
        diagnosis: issues.length === 0 ? '没有发现明显问题，可能是前端邀请码传递问题' : '发现问题'
      }
    });

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
