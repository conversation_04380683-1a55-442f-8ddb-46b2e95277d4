/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useUser } from "@/loomrunhooks/useUser";
import { usePathname, useRouter } from "next/navigation";
import { useMount } from "react-use";
import { UserContext } from "@/components/contexts/user-context";

import { toast } from "sonner";
import { useBroadcastChannel } from "@/lib/useBroadcastChannel";

export default function AppContext({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, logout, loading, refreshUser } = useUser();
  const pathname = usePathname();
  const router = useRouter();

  useMount(() => {
    // 检查特定路径的访问权限
    if (pathname.includes("/spaces") && !user && !loading) {
      toast.error("需要登录才能访问此页面");
      router.push("/");
    }
  });

  // 保持原有的BroadcastChannel逻辑，但移除已过时的OAuth处理
  useBroadcastChannel("auth", (message) => {
    if (pathname.includes("/auth/callback")) return;
    
    // 这里可以处理其他认证相关的消息
    console.log("Auth broadcast message:", message);
  });

  return (
    <UserContext value={{ user, loading, logout, refreshUser } as any}>
      {children}
    </UserContext>
  );
}
