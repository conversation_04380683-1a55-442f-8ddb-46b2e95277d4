import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { 
  getUserByToken, 
  addConversationToProject,
  getProjectChatHistory,
  saveChatMessage
} from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

/**
 * 获取项目聊天历史
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const chatHistory = await getProjectChatHistory(projectId, user.id);
    
    return NextResponse.json({
      ok: true,
      chatHistory
    });
  } catch (error) {
    console.error("获取聊天历史失败:", error);
    return NextResponse.json(
      { error: "服务器内部错误", ok: false },
      { status: 500 }
        );
  }
}

/**
 * 删除指定消息及其后续的所有对话
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const { fromMessageId, fromTimestamp } = await request.json();
    
    if (!fromMessageId && !fromTimestamp) {
      return NextResponse.json(
        { message: "fromMessageId or fromTimestamp is required" },
        { status: 400 }
      );
    }

    console.log('🗑️ 删除对话请求:', {
      projectId,
      fromMessageId,
      fromTimestamp,
      userId: user.id
    });

    // 🎯 根据时间戳删除该时间点之后的所有消息和版本
    if (fromTimestamp) {
      const deleteTime = new Date(fromTimestamp);
      
      // 删除该时间之后的所有聊天消息
      await executeQuery(
        `DELETE FROM chat_messages 
         WHERE project_id = ? AND user_id = ? AND created_at >= ?`,
        [projectId, user.id, deleteTime]
      );

      // 删除该时间之后的所有项目版本
      await executeQuery(
        `DELETE FROM project_versions 
         WHERE project_id = ? AND user_id = ? AND created_at >= ?`,
        [projectId, user.id, deleteTime]
      );

      console.log('✅ 已删除指定时间后的所有对话和版本');
    }

    return NextResponse.json({
      ok: true,
      message: "对话已删除"
    });
  } catch (error) {
    console.error("❌ 删除对话失败:", error);
    return NextResponse.json(
      { error: "服务器内部错误", ok: false },
      { status: 500 }
    );
  }
}

/**
 * 为项目添加新的对话（用户消息 + AI响应）
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('📥 Add conversation API called');
    
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      console.log('❌ Unauthorized: No token');
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    console.log('👤 User lookup result:', user ? `User ID: ${user.id}` : 'No user found');
    
    if (!user) {
      console.log('❌ Unauthorized: Invalid token');
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return NextResponse.json({ message: "Invalid project ID" }, { status: 400 });
    }

    const { userPrompt, aiHtmlContent, saveUserMessageOnly, images, syncToRecentAIMessage } = await request.json();
    console.log('📝 Conversation data:', {
      projectId,
      userPrompt: userPrompt?.substring(0, 50) + '...',
      htmlLength: aiHtmlContent?.length || 0,
      hasTitle: !!(aiHtmlContent && aiHtmlContent.match(/<title[^>]*>(.*?)<\/title>/i)),
      saveUserMessageOnly,
      imageCount: images?.length || 0
    });

    if (!userPrompt) {
      console.log('❌ Missing user prompt');
      return NextResponse.json(
        { message: "User prompt is required.", ok: false },
        { status: 400 }
      );
    }

    // 🚀 新功能：支持只保存用户消息
    if (saveUserMessageOnly) {
      console.log('💾 Saving user message only...');
      const userMessage = await saveChatMessage(
        projectId,
        user.id,
        'user',
        userPrompt,
        undefined, // htmlContent为undefined
        undefined, // versionId为undefined
        images     // 传递图片信息
      );
      
      if (!userMessage) {
        console.log('❌ Failed to save user message');
        return NextResponse.json(
          { error: "Failed to save user message", ok: false },
          { status: 500 }
        );
      }

      console.log('✅ User message saved successfully:', { messageId: userMessage.id });
      return NextResponse.json(
        { 
          result: { userMessageId: userMessage.id },
          ok: true 
        }, 
        { status: 201 }
      );
    }

    // 🔧 原有逻辑：保存完整对话
    if (!aiHtmlContent) {
      console.log('❌ Missing AI HTML content for full conversation');
      return NextResponse.json(
        { message: "AI HTML content is required for full conversation.", ok: false },
        { status: 400 }
      );
    }

    console.log('🚀 Adding full conversation to project...');
    const result = await addConversationToProject(
      projectId,
      user.id,
      userPrompt,
      aiHtmlContent,
      images // 传递图片信息
    );
    
    if (!result) {
      console.log('❌ Failed to add conversation');
      return NextResponse.json(
        { error: "Failed to add conversation", ok: false },
        { status: 500 }
      );
    }

    console.log('✅ Conversation added successfully:', result);

    // 🎯 如果需要同步到最近的AI消息，则额外更新
    if (syncToRecentAIMessage) {
      console.log('🔧 Syncing HTML content to recent AI message...');
      const { updateRecentAIMessageHtmlContent } = await import('@/lib/auth-service');
      const syncSuccess = await updateRecentAIMessageHtmlContent(
        projectId,
        user.id,
        aiHtmlContent,
        5 // 5分钟内的AI消息
      );
      
      if (syncSuccess) {
        console.log('✅ HTML content synced to recent AI message');
      } else {
        console.log('⚠️ No recent AI message found to sync HTML content');
      }
    }

    // 🎯 广播版本更新事件 - 高性能实时更新
    try {
      const versionEventData = {
        type: 'version-created',
        projectId: projectId.toString(),
        versionNumber: result.versionNumber,
        timestamp: Date.now()
      };

      console.log('📢 Broadcasting version update event:', versionEventData);

      // 🔧 方法1：设置localStorage触发storage事件（跨标签页）
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('version-created-event', JSON.stringify(versionEventData));
        // 立即清理，避免localStorage污染
        setTimeout(() => {
          localStorage.removeItem('version-created-event');
        }, 100);
      }

      console.log('✅ Version update event broadcasted successfully');
    } catch (broadcastError) {
      console.log('⚠️ Failed to broadcast version update event:', broadcastError);
      // 不影响主要功能，只记录错误
    }
    return NextResponse.json(
      { 
        result,
        ok: true 
      }, 
      { status: 201 }
    );
  } catch (error) {
    console.error("❌ 添加对话API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误", ok: false },
      { status: 500 }
    );
  }
} 