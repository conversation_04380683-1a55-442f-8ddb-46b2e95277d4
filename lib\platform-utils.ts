/**
 * 🎯 平台检测工具函数
 * 
 * 提供现代化的平台检测方法，避免使用已弃用的 navigator.platform
 */

/**
 * 检测是否为 Mac 系统（包括 macOS, iOS 设备）
 * 使用 userAgent 而不是已弃用的 platform 属性
 */
export const isMacPlatform = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // 优先使用 userAgentData（现代浏览器）
  if ('userAgentData' in navigator && (navigator as any).userAgentData) {
    const uaData = (navigator as any).userAgentData;
    return uaData.platform === 'macOS';
  }
  
  // 回退到 userAgent 检测
  const userAgent = navigator.userAgent.toLowerCase();
  return /mac|iphone|ipad|ipod/.test(userAgent);
};

/**
 * 获取撤销快捷键文本
 */
export const getUndoShortcut = (): string => {
  return isMacPlatform() ? 'Cmd+Z' : 'Ctrl+Z';
};

/**
 * 获取重做快捷键文本
 */
export const getRedoShortcut = (): string => {
  return isMacPlatform() ? 'Cmd+Shift+Z' : 'Ctrl+Y';
};

/**
 * 创建撤销键盘事件
 */
export const createUndoKeyEvent = (): KeyboardEvent => {
  const isMac = isMacPlatform();
  return new KeyboardEvent('keydown', {
    key: 'z',
    ctrlKey: !isMac,
    metaKey: isMac,
    bubbles: true,
    cancelable: true
  });
};

/**
 * 创建重做键盘事件
 */
export const createRedoKeyEvent = (): KeyboardEvent => {
  const isMac = isMacPlatform();
  return new KeyboardEvent('keydown', {
    key: isMac ? 'z' : 'y',
    ctrlKey: !isMac,
    metaKey: isMac,
    shiftKey: isMac,
    bubbles: true,
    cancelable: true
  });
};

/**
 * 检测是否在输入元素中
 */
export const isInInputElement = (target: EventTarget | null): boolean => {
  if (!target || !(target instanceof HTMLElement)) return false;
  
  const element = target as HTMLElement;
  return (
    element.tagName === 'INPUT' ||
    element.tagName === 'TEXTAREA' ||
    element.contentEditable === 'true' ||
    element.closest('input, textarea, [contenteditable="true"]') !== null
  );
};

/**
 * 检测是否在编辑器环境中
 */
export const isInEditorEnvironment = (): boolean => {
  return document.querySelector('.preview-container-optimized') !== null;
};

/**
 * 检测是否有选中的元素
 */
export const hasSelectedElement = (): boolean => {
  return document.querySelector('.selected-element-highlight') !== null;
};

/**
 * 检测是否在编辑模式
 */
export const isInEditMode = (): boolean => {
  return document.querySelector('.edit-mode-active') !== null;
};
