import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Wifi } from 'lucide-react';

// 简化的 className 合并函数
const cn = (...classes: (string | undefined | boolean)[]) => {
  return classes.filter(Boolean).join(' ');
};

interface ModelOption {
  id: string;
  name: string;
  description: string;
  signal: number; // 1-4 信号强度
}

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  disabled?: boolean;
  className?: string;
  isWelcomeMode?: boolean;
}

const MODELS: ModelOption[] = [
  {
    id: 'loomrun-1.2ds',
    name: 'loomrun-1.2ds',
    description: '基础模型，稳定可靠',
    signal: 2
  },
  {
    id: 'loomrun-1.6db',
    name: 'loomrun-1.6db',
    description: '平衡模型，速度与质量并重',
    signal: 3
  },
  {
    id: 'loomrun-zoom',
    name: 'loomrun-zoom',
    description: '极速设计，AI前沿技术',
    signal: 4
  }
];

// 信号强度图标组件 - 绿色信号条
const SignalIcon: React.FC<{ strength: number; className?: string }> = ({ strength, className }) => {
  return (
    <div className={cn("flex items-end gap-0.5", className)}>
      {[1, 2, 3, 4].map((level) => (
        <div
          key={level}
          className={cn(
            "w-0.5 rounded-sm transition-all duration-200",
            level === 1 && "h-1",
            level === 2 && "h-1.5",
            level === 3 && "h-2",
            level === 4 && "h-2.5",
            strength >= level
              ? "bg-green-500"
              : "bg-gray-300 dark:bg-gray-600"
          )}
        />
      ))}
    </div>
  );
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
  className,
  isWelcomeMode = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = MODELS.find(model => model.id === selectedModel) || MODELS[0];

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsOpen(false);
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef} style={{zIndex: isOpen ? 9999 : 'auto'}}>
      {/* 触发按钮 - 紧凑专业设计 */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            setIsOpen(!isOpen);
          }
        }}
        disabled={disabled}
        className={cn(
          "flex items-center gap-1.5 px-2 py-1 rounded-md transition-all duration-200",
          "bg-white dark:bg-black border border-gray-200 dark:border-neutral-600",
          "hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-sm",
          "focus:outline-none focus:ring-1 focus:ring-blue-500/30 focus:border-blue-500",
          isWelcomeMode
            ? "text-gray-700 dark:text-neutral-200 hover:text-blue-600 dark:hover:text-blue-300"
            : "text-neutral-600 dark:text-neutral-300 hover:text-blue-500",
          disabled && "opacity-50 cursor-not-allowed",
          isOpen && "border-blue-500 shadow-sm ring-1 ring-blue-500/30"
        )}
      >
        {/* 信号图标 */}
        <SignalIcon
          strength={selectedOption.signal}
          className="flex-shrink-0"
        />

        {/* 模型名称 */}
        <code className="text-xs font-mono truncate min-w-0">
          {selectedOption.name}
        </code>

        {/* 下拉箭头 */}
        <ChevronDown
          className={cn(
            "w-2.5 h-2.5 transition-transform duration-200 flex-shrink-0",
            isOpen ? "rotate-180 text-blue-500" : "text-gray-400 dark:text-neutral-500"
          )}
        />
      </button>

      {/* 下拉菜单 - 超紧凑设计 */}
      {isOpen && (
        <div className="absolute bottom-full left-0 mb-2 w-52 bg-white dark:bg-black rounded-md shadow-xl border border-gray-200 dark:border-neutral-600 z-[9999] overflow-hidden">
          <div className="py-0.5">
            {MODELS.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelSelect(model.id)}
                className={cn(
                  "w-full flex items-center gap-2 px-2.5 py-1.5 transition-all duration-200 text-left",
                  "hover:bg-gray-50 dark:hover:bg-neutral-900",
                  selectedModel === model.id
                    ? "bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500"
                    : ""
                )}
              >
                {/* 信号图标 */}
                <SignalIcon
                  strength={model.signal}
                  className="flex-shrink-0"
                />

                {/* 模型信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1 mb-0.5">
                    <code className={cn(
                      "font-mono text-xs truncate",
                      selectedModel === model.id
                        ? "text-blue-700 dark:text-blue-300"
                        : "text-gray-900 dark:text-white"
                    )}>
                      {model.name}
                    </code>
                    {selectedModel === model.id && (
                      <div className="w-1 h-1 bg-blue-500 rounded-full flex-shrink-0" />
                    )}
                  </div>
                  <p className={cn(
                    "text-xs leading-tight truncate",
                    selectedModel === model.id
                      ? "text-blue-600 dark:text-blue-400"
                      : "text-gray-500 dark:text-neutral-400"
                  )}>
                    {model.description}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}; 