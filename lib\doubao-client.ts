export interface DoubaoMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

export interface DoubaoRequest {
  model: string;
  messages: DoubaoMessage[];
  stream?: boolean;
  max_tokens?: number;
  temperature?: number;
}

export interface DoubaoStreamChunk {
  choices: Array<{
    delta: {
      content?: string;
      role?: string;
    };
    finish_reason?: string | null;
    index: number;
  }>;
  id: string;
  object: string;
  created: number;
  model: string;
}

export interface DoubaoResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
    index: number;
  }>;
  id: string;
  object: string;
  created: number;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DoubaoClient {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL = "https://ark.cn-beijing.volces.com/api/v3") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
    
    console.log('🔧 豆包客户端初始化', {
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey ? apiKey.substring(0, 8) + '...' : 'none',
      baseURL: baseURL
    });
  }

  async *chatCompletionStream(request: DoubaoRequest): AsyncGenerator<DoubaoStreamChunk> {
    // 验证模型名称
    if (!request.model.includes('doubao')) {
      throw new Error(`Invalid model: ${request.model}. Expected doubao model.`);
    }

    // 验证消息
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: true,
      ...(request.max_tokens && { max_tokens: request.max_tokens }), // 移除token限制，让豆包模型充分发挥
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('Doubao API Request:', JSON.stringify(requestPayload, null, 2));

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Doubao API Error Response:', errorText);
      throw new Error(`Doubao API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Failed to get response reader');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed === '' || trimmed === 'data: [DONE]') continue;
          
          if (trimmed.startsWith('data: ')) {
            try {
              const jsonStr = trimmed.slice(6);
              const data = JSON.parse(jsonStr);
              yield data;
            } catch (error) {
              console.warn('Failed to parse SSE data:', trimmed, error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  async chatCompletion(request: DoubaoRequest): Promise<DoubaoResponse> {
    // 验证模型名称
    if (!request.model.includes('doubao')) {
      throw new Error(`Invalid model: ${request.model}. Expected doubao model.`);
    }

    // 验证消息
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: false,
      ...(request.max_tokens && { max_tokens: request.max_tokens }), // 移除token限制，让豆包模型充分发挥
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('🔧 豆包API请求详情:', {
      model: request.model,
      messagesCount: request.messages.length,
      maxTokens: request.max_tokens,
      baseURL: this.baseURL,
      hasApiKey: !!this.apiKey,
      apiKeyLength: this.apiKey?.length || 0
    });
    
    console.log('Doubao API Request:', JSON.stringify(requestPayload, null, 2));

    console.log('🚀 豆包API: 开始请求', {
      url: `${this.baseURL}/chat/completions`,
      model: request.model,
      messagesCount: request.messages.length,
      maxTokens: request.max_tokens
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.error('❌ 豆包API: 请求超时 (60秒)');
      controller.abort();
    }, 60000); // 60秒超时

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'User-Agent': 'LoomRun/1.0',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestPayload),
        signal: controller.signal,
        // 添加连接配置
        keepalive: true,
      });

      clearTimeout(timeoutId);

      console.log('✅ 豆包API: 收到响应', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 豆包API错误响应:', {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText,
          model: request.model,
          baseURL: this.baseURL
        });
        throw new Error(`Doubao API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('📦 豆包API: 解析结果', {
        hasChoices: !!result.choices,
        choicesLength: result.choices?.length || 0,
        hasContent: !!result.choices?.[0]?.message?.content
      });

      return result;
    } catch (error: unknown) {
      clearTimeout(timeoutId);
      const err = error as Error;
      if (err.name === 'AbortError') {
        throw new Error('豆包API请求超时，请稍后重试');
      }
      console.error('❌ 豆包API: 请求异常', {
        error: err.message,
        model: request.model,
        baseURL: this.baseURL,
        hasApiKey: !!this.apiKey
      });
      throw error;
    }
  }

  // 辅助方法：构建包含图片的消息
  static createImageMessage(text: string, imageUrls: string[]): DoubaoMessage {
    const content: Array<{type: 'text' | 'image_url'; text?: string; image_url?: {url: string}}> = [
      { type: 'text', text }
    ];

    // 添加图片
    for (const url of imageUrls) {
      content.push({
        type: 'image_url',
        image_url: { url }
      });
    }

    return {
      role: 'user',
      content
    };
  }
} 