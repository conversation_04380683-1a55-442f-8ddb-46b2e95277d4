# 邀请好友弹窗深色模式优化指南

## 🎯 优化目标
为邀请好友弹窗提供完美的深色模式视觉体验，确保在深色和浅色模式下都有良好的可读性和美观性。

## 🎨 设计原则

### 1. 对比度优化
- **浅色模式**: 深色文字 + 浅色背景
- **深色模式**: 浅色文字 + 深色背景
- 确保文字与背景有足够的对比度（WCAG AA标准）

### 2. 颜色适配
- **主色调**: 紫色系保持一致，深色模式下稍微调亮
- **状态颜色**: 绿色、蓝色、橙色、红色在深色模式下使用更亮的变体
- **中性色**: 灰色系在深色模式下反转

### 3. 层次感
- **背景层**: 深色模式使用 `gray-900`
- **卡片层**: 深色模式使用 `gray-800`
- **输入框**: 深色模式使用 `gray-800` 背景
- **边框**: 深色模式使用 `gray-700`

## 🔧 技术实现

### 1. 弹窗容器优化
```tsx
<DialogContent className="sm:max-w-md bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
```

### 2. 标题区域
```tsx
<DialogTitle className="flex items-center space-x-2 text-gray-900 dark:text-gray-100">
  <Users className="w-5 h-5 text-purple-500 dark:text-purple-400" />
  <span>邀请好友赚积分</span>
</DialogTitle>
```

### 3. 活动规则卡片
```tsx
<div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-lg border border-purple-100 dark:border-purple-800/30">
  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">活动规则</h3>
  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
    <!-- 规则内容 -->
  </ul>
</div>
```

### 4. 统计卡片
```tsx
<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
  <div className="text-2xl font-bold text-green-600 dark:text-green-400">5</div>
  <div className="text-sm text-gray-600 dark:text-gray-400">成功邀请</div>
</div>
```

### 5. 输入框优化
```tsx
<input
  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent"
/>
```

### 6. 按钮样式
```tsx
// 主要按钮
<Button className="bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 text-white border-0">

// 边框按钮
<Button className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 bg-white dark:bg-gray-900">
```

## 🎨 颜色映射表

| 元素类型 | 浅色模式 | 深色模式 | 用途 |
|---------|---------|---------|------|
| 主背景 | `bg-white` | `bg-gray-900` | 弹窗背景 |
| 卡片背景 | `bg-gray-50` | `bg-gray-800` | 统计卡片 |
| 主文字 | `text-gray-900` | `text-gray-100` | 标题文字 |
| 次要文字 | `text-gray-600` | `text-gray-300` | 描述文字 |
| 辅助文字 | `text-gray-500` | `text-gray-400` | 提示文字 |
| 主色调 | `text-purple-600` | `text-purple-400` | 强调色 |
| 成功色 | `text-green-600` | `text-green-400` | 成功状态 |
| 警告色 | `text-orange-600` | `text-orange-400` | 警告状态 |
| 错误色 | `text-red-600` | `text-red-400` | 错误状态 |
| 边框 | `border-gray-200` | `border-gray-700` | 默认边框 |

## 🛠️ 工具函数

### 1. 深色模式工具类
```typescript
// lib/dark-mode-utils.ts
export const getDarkModeBackground = (variant: string) => {
  // 返回深色模式友好的背景类名
};

export const getDarkModeText = (variant: string) => {
  // 返回深色模式友好的文字类名
};
```

### 2. 有效期显示优化
```typescript
// lib/validity-display-utils.ts
export const formatValidityWithStyle = (days: number) => {
  // 返回包含深色模式支持的颜色类名
  return { 
    text: "15天", 
    colorClass: "text-red-600 dark:text-red-400" 
  };
};
```

## 🧪 测试方法

### 1. 手动测试
1. 访问 `/test-dark-mode` 页面
2. 点击"切换到深色模式"按钮
3. 检查所有元素的可读性和美观性
4. 验证颜色对比度是否足够

### 2. 自动化测试
```bash
# 运行深色模式测试
npm run test:dark-mode

# 检查颜色对比度
npm run test:contrast
```

### 3. 浏览器测试
- Chrome DevTools: 模拟深色模式
- Firefox: 切换 `prefers-color-scheme`
- Safari: 系统深色模式测试

## 📱 响应式适配

### 移动端优化
```tsx
<DialogContent className="sm:max-w-md max-w-[95vw] bg-white dark:bg-gray-900">
```

### 平板端优化
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
```

## ✅ 验收标准

### 视觉效果
- [ ] 深色模式下所有文字清晰可读
- [ ] 颜色对比度符合WCAG AA标准
- [ ] 渐变背景在深色模式下美观
- [ ] 边框和分割线清晰可见

### 交互体验
- [ ] 按钮悬停效果正常
- [ ] 输入框焦点状态清晰
- [ ] 徽章状态区分明显
- [ ] 复制按钮状态反馈及时

### 兼容性
- [ ] 支持系统深色模式自动切换
- [ ] 支持手动切换深色模式
- [ ] 在不同浏览器中表现一致
- [ ] 移动端和桌面端都正常

## 🚀 部署检查

部署前确认：
- [ ] 所有深色模式样式已应用
- [ ] 工具函数正常工作
- [ ] 测试页面可以正常访问
- [ ] 生产环境深色模式正常
- [ ] 性能影响在可接受范围内

## 📖 使用指南

### 开发者使用
```tsx
import { getDarkModeBackground, getDarkModeText } from '@/lib/dark-mode-utils';

// 在组件中使用
<div className={getDarkModeBackground('card')}>
  <h3 className={getDarkModeText('primary')}>标题</h3>
</div>
```

### 设计师参考
- 深色模式使用更柔和的颜色
- 避免纯黑色背景，使用深灰色
- 保持品牌色的识别度
- 注意阴影效果的调整
