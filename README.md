<div align="center">

# 🚀 LoomRun

### AI-Powered Website Builder | AI 驱动的网站构建器

*Build beautiful websites with natural language - powered by DeepSeek AI*

[English](#english) • [中文](#中文) • [ئۇيغۇرچە](#ئۇيغۇرچە)

---

[![Next.js](https://img.shields.io/badge/Next.js-15-black)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19-blue)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4-38B2AC)](https://tailwindcss.com/)
[![DeepSeek AI](https://img.shields.io/badge/DeepSeek-AI-orange)](https://deepseek.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

</div>

---

## English

### 🌟 What is LoomRun?

LoomRun is a revolutionary AI-powered website development platform that transforms how you create websites. Using cutting-edge DeepSeek AI technology, you can build stunning, professional websites through natural language conversations—no coding experience required.

### ✨ Key Features

- 🤖 **AI-Driven Development**: Create websites using natural language with DeepSeek AI
- ⚡ **Real-time Preview**: See your changes instantly as you build
- 🎨 **Professional Design**: Generate production-ready, responsive websites
- 🌐 **Multi-language Support**: Interface available in Chinese, English, and Uyghur
- 🔧 **Modern Tech Stack**: Built with Next.js 15, React 19, and Tailwind CSS
- 💡 **No Code Required**: Perfect for beginners and non-technical users
- 🚀 **Fast Deployment**: Deploy your websites with one click
- 📱 **Mobile Optimized**: All websites are fully responsive

### 🛠️ Technology Stack

| Category | Technology |
|----------|------------|
| **Frontend** | Next.js 15, React 19, TypeScript |
| **Styling** | Tailwind CSS, Shadcn/ui Components |
| **AI Engine** | DeepSeek API for intelligent code generation |
| **Database** | MySQL with optimized queries |
| **Authentication** | Custom secure authentication system |
| **Deployment** | Optimized for modern hosting platforms |

### 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/abnb0208/LoomRun.git
   cd LoomRun
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Add your DeepSeek API key
   DEEPSEEK_API_KEY=your_key_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3141](http://localhost:3141)

### 📦 Available Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

---

## 中文

### 🌟 什么是 LoomRun？

LoomRun 是一个革命性的 AI 驱动网站开发平台，彻底改变了您创建网站的方式。使用前沿的 DeepSeek AI 技术，您可以通过自然语言对话构建令人惊艳的专业网站——无需任何编程经验。

### ✨ 核心特性

- 🤖 **AI 智能开发**：使用 DeepSeek AI 通过自然语言创建网站
- ⚡ **实时预览**：构建过程中即时查看您的更改
- 🎨 **专业设计**：生成可投产的响应式网站
- 🌐 **多语言支持**：界面支持中文、英文和维吾尔语
- 🔧 **现代技术栈**：基于 Next.js 15、React 19 和 Tailwind CSS
- 💡 **无需编程**：非常适合初学者和非技术用户
- 🚀 **快速部署**：一键部署您的网站
- 📱 **移动优化**：所有网站都完全响应式

### 🛠️ 技术架构

| 分类 | 技术 |
|------|------|
| **前端框架** | Next.js 15, React 19, TypeScript |
| **样式系统** | Tailwind CSS, Shadcn/ui 组件库 |
| **AI 引擎** | DeepSeek API 智能代码生成 |
| **数据库** | MySQL 优化查询 |
| **身份验证** | 自定义安全认证系统 |
| **部署优化** | 适配现代托管平台 |

### 🚀 快速开始

1. **克隆仓库**
   ```bash
   git clone https://github.com/abnb0208/LoomRun.git
   cd LoomRun
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 添加您的 DeepSeek API 密钥
   DEEPSEEK_API_KEY=your_key_here
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

5. **打开浏览器**
   访问 [http://localhost:3141](http://localhost:3141)

---

## ئۇيغۇرچە

### 🌟 LoomRun دېگەن نېمە؟

LoomRun بولسا AI تېخنىكىسى بىلەن قوزغىتىلغان ئىنقىلابىي تور بېكەت ياساش سۇپىسى. DeepSeek AI نىڭ ئالدىنقى تېخنىكىسىنى ئىشلىتىپ، سىز تەبىئىي تىل ئارقىلىق كەسپىي تور بېكەتلىرىنى ياساپ چىقالايسىز—ھېچقانداق پروگرامما يېزىش تەجرىبىسى تەلەپ قىلىنمايدۇ.

### ✨ ئاساسىي ئىقتىدارلار

- 🤖 **AI ئاقىللىق ئېچىش**：DeepSeek AI بىلەن تەبىئىي تىل ئارقىلىق تور بېكەت ياساش
- ⚡ **ۋاقىتلىق كۆرۈش**：قۇرۇش جەريانىدا دەرھال ئۆزگىرىشلىرىڭىزنى كۆرۈڭ
- 🎨 **كەسپىي لايىھە**：ئىشلەپچىقىرىش تەييار بولغان ئىنكاس تور بېكەتلىرى
- 🌐 **كۆپ تىل قوللاش**：كۆرۈنۈش خەنزۇچە، ئىنگلىزچە ۋە ئۇيغۇرچىنى قوللايدۇ
- 🔧 **زامانىۋى تېخنىكا**：Next.js 15، React 19 ۋە Tailwind CSS ئاساسىدا
- 💡 **كود تەلەپ قىلمايدۇ**：يېڭى ئۆگەنگۈچىلەر ۋە تېخنىكىسىز ئىشلەتكۈچىلەرگە ماس
- 🚀 **تېز تەقسىملەش**：بىر چېكىش بىلەن تور بېكىتىڭىزنى تەقسىملەڭ
- 📱 **كۆچمە ئۈسكۈنە ئەلالاشتۇرۇش**：بارلىق تور بېكەتلەر تولۇق ئىنكاسلىق

---

### 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### 🙏 Acknowledgments

- [DeepSeek AI](https://deepseek.com/) for providing the powerful AI engine
- [Next.js](https://nextjs.org/) for the amazing React framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Shadcn/ui](https://ui.shadcn.com/) for the beautiful component library

---

<div align="center">

**❤️ Made with passion by abnb0208**

[Website](https://loomrun.com) • [Documentation](https://docs.loomrun.com) • [Community](https://discord.gg/loomrun)

</div>
