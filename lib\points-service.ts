import { executeQuery } from './mysql';

export interface PointsTransaction {
  id: number;
  user_id: number;
  transaction_type: 'earn' | 'spend';
  points_amount: number;
  balance_before: number;
  balance_after: number;
  source_type: 'registration' | 'ai_request' | 'export' | 'recharge' | 'subscription' | 'admin_adjust' | 'refund' | 'invitation';
  source_id?: string;
  description?: string;
  metadata?: Record<string, unknown>;
  created_at: Date;
}

export interface SystemSetting {
  id: number;
  setting_key: string;
  setting_value: string;
  setting_type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  category: string;
  is_active: boolean;
}

// 获取系统设置
export const getSystemSetting = async (key: string): Promise<string | null> => {
  try {
    const results = await executeQuery(
      'SELECT setting_value FROM system_settings WHERE setting_key = ? AND is_active = 1',
      [key]
    ) as SystemSetting[];
    
    return results.length > 0 ? results[0].setting_value : null;
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return null;
  }
};

// 获取布尔类型系统设置
export const getBooleanSetting = async (key: string, defaultValue = false): Promise<boolean> => {
  const value = await getSystemSetting(key);
  if (value === null) return defaultValue;
  return value === '1' || value.toLowerCase() === 'true';
};

// 获取数字类型系统设置
export const getNumberSetting = async (key: string, defaultValue = 0): Promise<number> => {
  const value = await getSystemSetting(key);
  if (value === null) return defaultValue;
  const num = parseInt(value, 10);
  return isNaN(num) ? defaultValue : num;
};

// 获取字符串类型系统设置
export const getStringSetting = async (key: string, defaultValue = ''): Promise<string> => {
  const value = await getSystemSetting(key);
  return value === null ? defaultValue : value;
};

// 获取用户当前积分余额
export const getUserPoints = async (userId: number): Promise<number> => {
  try {
    const results = await executeQuery(
      'SELECT points FROM users WHERE id = ?',
      [userId]
    ) as { points: number }[];
    
    return results.length > 0 ? results[0].points : 0;
  } catch (error) {
    console.error('获取用户积分失败:', error);
    return 0;
  }
};

// 添加积分交易记录并更新用户积分（支持积分分类和有效期）
export const addPointsTransactionWithExpiry = async (
  userId: number,
  transactionType: 'earn' | 'spend',
  pointsAmount: number,
  pointsType: 'activity' | 'subscription' | 'recharge',
  sourceType: PointsTransaction['source_type'],
  sourceId?: string,
  description?: string,
  expiresAt?: Date,
  metadata?: Record<string, unknown>
): Promise<boolean> => {
  const mysql = require('mysql2/promise');
  let connection;

  try {
    // 创建新的数据库连接用于事务
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    // 开始事务
    await connection.beginTransaction();

    // 获取当前积分余额
    const [userRows] = await connection.execute(
      'SELECT points FROM users WHERE id = ?',
      [userId]
    );
    const currentPoints = userRows.length > 0 ? userRows[0].points : 0;

    // 检查积分是否足够（如果是消费）
    if (transactionType === 'spend' && currentPoints < pointsAmount) {
      await connection.rollback();
      throw new Error('积分余额不足');
    }

    // 计算新的积分余额
    const newPoints = transactionType === 'earn'
      ? currentPoints + pointsAmount
      : currentPoints - pointsAmount;

    let balanceRecordId = null;

    if (transactionType === 'earn') {
      // 创建积分余额记录
      const [balanceResult] = await connection.execute(
        `INSERT INTO user_points_balance
         (user_id, points_type, points_amount, expires_at, source_order_id, source_plan_key)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          userId,
          pointsType,
          pointsAmount,
          expiresAt || null,
          sourceId || null,
          null
        ]
      );

      balanceRecordId = balanceResult.insertId;
    }

    // 插入积分交易记录
    await connection.execute(
      `INSERT INTO points_transactions
       (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, points_type, expires_at, balance_record_id, source_id, description, metadata)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        transactionType,
        pointsAmount,
        currentPoints,
        newPoints,
        sourceType,
        pointsType,
        expiresAt || null,
        balanceRecordId,
        sourceId || null,
        description || null,
        metadata ? JSON.stringify(metadata) : null
      ]
    );

    // 更新用户积分余额和统计
    if (transactionType === 'earn') {
      await connection.execute(
        'UPDATE users SET points = ?, total_earned_points = total_earned_points + ? WHERE id = ?',
        [newPoints, pointsAmount, userId]
      );
    } else {
      await connection.execute(
        'UPDATE users SET points = ?, total_spent_points = total_spent_points + ? WHERE id = ?',
        [newPoints, pointsAmount, userId]
      );
    }

    // 提交事务
    await connection.commit();

    const expiryInfo = expiresAt ? ` (有效期至: ${expiresAt.toLocaleDateString()})` : ' (永久有效)';
    console.log(`✅ 积分交易成功: 用户${userId} ${transactionType === 'earn' ? '获得' : '消费'} ${pointsAmount} ${pointsType}积分${expiryInfo}`);
    return true;
  } catch (error) {
    // 回滚事务
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('事务回滚失败:', rollbackError);
      }
    }
    console.error('积分交易失败:', error);
    return false;
  } finally {
    // 关闭连接
    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        console.error('关闭数据库连接失败:', closeError);
      }
    }
  }
};

// 兼容旧版本的积分交易函数
export const addPointsTransaction = async (
  userId: number,
  transactionType: 'earn' | 'spend',
  pointsAmount: number,
  sourceType: PointsTransaction['source_type'],
  sourceId?: string,
  description?: string,
  metadata?: Record<string, unknown>
): Promise<boolean> => {
  // 默认为活动积分，30天有效期
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 30);

  return addPointsTransactionWithExpiry(
    userId,
    transactionType,
    pointsAmount,
    'activity',
    sourceType,
    sourceId,
    description,
    expiresAt,
    metadata
  );
};

// 记录注册日志
export const recordRegistrationLog = async (
  userId: number,
  registrationType: 'phone' | 'wechat',
  registrationSource?: string,
  inviteCode?: string,
  inviterUserId?: number,
  pointsAwarded: number = 0,
  ipAddress?: string,
  userAgent?: string,
  metadata?: Record<string, unknown>
): Promise<boolean> => {
  try {
    await executeQuery(
      `INSERT INTO registration_logs
       (user_id, registration_type, registration_source, invite_code, inviter_user_id, points_awarded, ip_address, user_agent, metadata)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        registrationType,
        registrationSource || null,
        inviteCode || null,
        inviterUserId || null,
        pointsAwarded,
        ipAddress || null,
        userAgent || null,
        metadata ? JSON.stringify(metadata) : null
      ]
    );

    console.log(`📝 注册日志记录成功: 用户${userId}, 类型${registrationType}, 积分${pointsAwarded}`);
    return true;
  } catch (error) {
    console.error('记录注册日志失败:', error);
    return false;
  }
};

// 新用户注册送积分（优化版，包含完整的日志记录）
export const grantNewUserPoints = async (
  userId: number,
  registrationType: 'phone' | 'wechat' = 'phone',
  registrationSource?: string,
  inviteCode?: string,
  inviterUserId?: number,
  ipAddress?: string,
  userAgent?: string
): Promise<boolean> => {
  const mysql = require('mysql2/promise');
  let connection;

  try {
    console.log(`🎯 开始为用户 ${userId} 发放新用户积分...`);

    // 创建数据库连接用于事务
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    // 开始事务
    await connection.beginTransaction();

    // 1. 检查新用户积分功能是否开启
    const [settingsRows] = await connection.execute(
      'SELECT setting_value FROM system_settings WHERE setting_key = ? AND is_active = 1',
      ['new_user_points_enabled']
    );

    // 🔧 修复：默认为false，只有明确设置为'1'时才启用
    const isEnabled = settingsRows.length > 0 ? settingsRows[0].setting_value === '1' : false;
    if (!isEnabled) {
      await connection.rollback();
      console.log('⚠️ 新用户积分功能已关闭或未配置');
      return false;
    }

    // 2. 获取积分配置（批量查询优化）
    const [configRows] = await connection.execute(
      `SELECT setting_key, setting_value FROM system_settings
       WHERE setting_key IN ('new_user_points_amount', 'activity_points_validity_days')
       AND is_active = 1`
    );

    const config = configRows.reduce((acc: Record<string, string>, row: any) => {
      acc[row.setting_key] = row.setting_value;
      return acc;
    }, {});

    // 🔧 修复：积分数量默认为0，必须明确配置才发放
    const pointsAmount = parseInt(config.new_user_points_amount || '0');
    const validityDays = parseInt(config.activity_points_validity_days || '30');

    if (pointsAmount <= 0) {
      await connection.rollback();
      console.log('⚠️ 新用户积分数量为0，跳过发放');
      return false;
    }

    // 3. 检查用户是否已经获得过新用户积分（优化查询）
    const [existingRows] = await connection.execute(
      'SELECT 1 FROM points_transactions WHERE user_id = ? AND source_type = "registration" LIMIT 1',
      [userId]
    );

    if (existingRows.length > 0) {
      await connection.rollback();
      console.log('⚠️ 用户已获得过新用户积分，跳过发放');
      return false;
    }

    // 4. 获取用户当前积分余额
    const [userRows] = await connection.execute(
      'SELECT points FROM users WHERE id = ?',
      [userId]
    );

    const currentPoints = userRows.length > 0 ? userRows[0].points : 0;
    const newPoints = currentPoints + pointsAmount;

    // 5. 计算有效期
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + validityDays);

    console.log(`📊 准备发放积分: ${pointsAmount}积分，有效期${validityDays}天`);

    // 6. 创建积分余额记录
    const [balanceResult] = await connection.execute(
      `INSERT INTO user_points_balance
       (user_id, points_type, points_amount, expires_at, source_order_id)
       VALUES (?, 'activity', ?, ?, ?)`,
      [userId, pointsAmount, expiresAt, userId]
    );

    const balanceRecordId = balanceResult.insertId;

    // 7. 创建积分交易记录
    const transactionMetadata = {
      registration_bonus: true,
      validity_days: validityDays,
      registration_type: registrationType,
      registration_source: registrationSource,
      invite_code: inviteCode,
      inviter_user_id: inviterUserId,
      ip_address: ipAddress,
      user_agent: userAgent,
      grant_timestamp: new Date().toISOString()
    };

    await connection.execute(
      `INSERT INTO points_transactions
       (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, points_type, expires_at, balance_record_id, source_id, description, metadata)
       VALUES (?, 'earn', ?, ?, ?, 'registration', 'activity', ?, ?, ?, ?, ?)`,
      [
        userId,
        pointsAmount,
        currentPoints,
        newPoints,
        expiresAt,
        balanceRecordId,
        userId.toString(),
        `新用户注册奖励 ${pointsAmount} 积分`,
        JSON.stringify(transactionMetadata)
      ]
    );

    // 8. 更新用户积分余额
    await connection.execute(
      'UPDATE users SET points = ?, total_earned_points = total_earned_points + ? WHERE id = ?',
      [newPoints, pointsAmount, userId]
    );

    // 9. 记录注册日志
    const registrationMetadata = {
      points_grant_success: true,
      points_amount: pointsAmount,
      points_expires_at: expiresAt.toISOString(),
      balance_record_id: balanceRecordId,
      system_version: '1.0',
      grant_timestamp: new Date().toISOString()
    };

    await connection.execute(
      `INSERT INTO registration_logs
       (user_id, registration_type, registration_source, invite_code, inviter_user_id, points_awarded, ip_address, user_agent, metadata)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        registrationType,
        registrationSource || null,
        inviteCode || null,
        inviterUserId || null,
        pointsAmount,
        ipAddress || null,
        userAgent || null,
        JSON.stringify(registrationMetadata)
      ]
    );

    // 10. 提交事务
    await connection.commit();

    console.log(`🎉 新用户 ${userId} 注册完成: 获得${pointsAmount}积分，有效期至${expiresAt.toLocaleDateString()}`);
    console.log(`📝 注册日志和积分记录已完整保存`);

    return true;

  } catch (error) {
    // 回滚事务
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('事务回滚失败:', rollbackError);
      }
    }

    console.error('❌ 发放新用户积分过程中发生错误:', error);

    // 尝试至少记录注册日志（即使积分发放失败）
    try {
      await recordRegistrationLog(
        userId,
        registrationType,
        registrationSource,
        inviteCode,
        inviterUserId,
        0, // 积分发放失败，记录为0
        ipAddress,
        userAgent,
        {
          points_grant_success: false,
          error_message: error instanceof Error ? error.message : 'Unknown error',
          error_timestamp: new Date().toISOString()
        }
      );
    } catch (logError) {
      console.error('记录失败日志也失败:', logError);
    }

    return false;
  } finally {
    // 关闭连接
    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        console.error('关闭数据库连接失败:', closeError);
      }
    }
  }
};

// 简化版新用户积分发放（备用方案）
export const grantNewUserPointsSimple = async (userId: number): Promise<boolean> => {
  try {
    console.log(`🎯 [简化版] 开始为用户 ${userId} 发放新用户积分...`);

    // 🔧 修复：必须检查系统设置，不能绕过控制
    const isEnabled = await getBooleanSetting('new_user_points_enabled', false);
    if (!isEnabled) {
      console.log('⚠️ [简化版] 新用户积分功能已关闭，跳过发放');
      return false;
    }

    // 🔧 修复：从系统设置获取积分数量，不能硬编码
    const pointsAmount = await getNumberSetting('new_user_points_amount', 0);
    if (pointsAmount <= 0) {
      console.log('⚠️ [简化版] 新用户积分数量为0或未配置，跳过发放');
      return false;
    }

    // 检查是否已发放过注册积分
    const existingCheck = await executeQuery(
      'SELECT COUNT(*) as count FROM points_transactions WHERE user_id = ? AND source_type = "registration"',
      [userId]
    ) as { count: number }[];

    if (existingCheck.length > 0 && existingCheck[0].count > 0) {
      console.log('⚠️ [简化版] 用户已获得过注册积分，跳过发放');
      return false;
    }

    // 获取用户当前积分
    const currentPoints = await getUserPoints(userId);
    const newPoints = currentPoints + pointsAmount;

    // 直接更新用户积分
    await executeQuery(
      'UPDATE users SET points = ?, total_earned_points = total_earned_points + ? WHERE id = ?',
      [newPoints, pointsAmount, userId]
    );

    // 创建交易记录
    await executeQuery(
      `INSERT INTO points_transactions
       (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, description)
       VALUES (?, 'earn', ?, ?, ?, 'registration', ?)`,
      [
        userId,
        pointsAmount,
        currentPoints,
        newPoints,
        `[简化版] 新用户注册奖励 ${pointsAmount} 积分`
      ]
    );

    console.log(`🎉 [简化版] 用户 ${userId} 获得注册奖励 ${pointsAmount} 积分`);
    return true;
  } catch (error) {
    console.error('❌ [简化版] 发放新用户积分失败:', error);
    return false;
  }
};

// 获取AI模型积分消耗配置
export const getModelPointsCost = async (modelKey: string): Promise<number> => {
  try {
    const results = await executeQuery(
      'SELECT points_per_request FROM ai_models WHERE model_key = ? AND is_active = 1',
      [modelKey]
    ) as { points_per_request: number }[];

    return results.length > 0 ? results[0].points_per_request : 0;
  } catch (error) {
    console.error('获取模型积分消耗配置失败:', error);
    return 0;
  }
};

// 检查用户积分是否足够
export const checkUserPointsSufficient = async (userId: number, requiredPoints: number): Promise<boolean> => {
  try {
    const currentPoints = await getUserPoints(userId);
    return currentPoints >= requiredPoints;
  } catch (error) {
    console.error('检查用户积分余额失败:', error);
    return false;
  }
};

// 智能积分消耗（按优先级消耗不同类型积分）
export const consumePointsWithPriority = async (
  userId: number,
  pointsAmount: number,
  sourceType: PointsTransaction['source_type'],
  sourceId?: string,
  description?: string,
  metadata?: Record<string, unknown>
): Promise<{ success: boolean; transactionId?: number; consumptionLogs?: any[] }> => {
  const mysql = require('mysql2/promise');
  let connection;

  try {
    // 创建新的数据库连接用于事务
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    // 开始事务
    await connection.beginTransaction();

    // 1. 检查用户当前总积分
    const [userRows] = await connection.execute(
      'SELECT points FROM users WHERE id = ?',
      [userId]
    );
    const currentPoints = userRows.length > 0 ? userRows[0].points : 0;

    if (currentPoints < pointsAmount) {
      await connection.rollback();
      return { success: false };
    }

    // 2. 获取用户可用积分余额（按优先级排序：subscription > activity > recharge）
    const [balanceRows] = await connection.execute(
      `SELECT id, points_type, points_amount, expires_at
       FROM user_points_balance
       WHERE user_id = ? AND is_active = 1 AND points_amount > 0
       ORDER BY
         CASE points_type
           WHEN 'subscription' THEN 1
           WHEN 'activity' THEN 2
           WHEN 'recharge' THEN 3
           ELSE 4
         END,
         CASE
           WHEN expires_at IS NULL THEN 1
           ELSE 0
         END,
         expires_at ASC`,
      [userId]
    );

    // 3. 按优先级消耗积分
    let remainingToConsume = pointsAmount;
    const consumptionLogs = [];

    for (const balance of balanceRows) {
      if (remainingToConsume <= 0) break;

      const consumeFromThis = Math.min(balance.points_amount, remainingToConsume);

      // 更新余额记录
      const newBalanceAmount = balance.points_amount - consumeFromThis;
      await connection.execute(
        'UPDATE user_points_balance SET points_amount = ?, updated_at = NOW() WHERE id = ?',
        [newBalanceAmount, balance.id]
      );

      // 如果余额为0，标记为非活跃
      if (newBalanceAmount === 0) {
        await connection.execute(
          'UPDATE user_points_balance SET is_active = 0 WHERE id = ?',
          [balance.id]
        );
      }

      consumptionLogs.push({
        balance_record_id: balance.id,
        points_consumed: consumeFromThis,
        points_type: balance.points_type
      });

      remainingToConsume -= consumeFromThis;
    }

    // 4. 创建积分交易记录
    const newPoints = currentPoints - pointsAmount;
    const [transactionResult] = await connection.execute(
      `INSERT INTO points_transactions
       (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, source_id, description, metadata)
       VALUES (?, 'spend', ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        pointsAmount,
        currentPoints,
        newPoints,
        sourceType,
        sourceId || null,
        description || null,
        metadata ? JSON.stringify(metadata) : null
      ]
    );

    const transactionId = transactionResult.insertId;

    // 5. 创建详细的消耗日志
    for (const log of consumptionLogs) {
      await connection.execute(
        `INSERT INTO points_consumption_log
         (user_id, transaction_id, balance_record_id, points_consumed, points_type)
         VALUES (?, ?, ?, ?, ?)`,
        [userId, transactionId, log.balance_record_id, log.points_consumed, log.points_type]
      );
    }

    // 6. 更新用户总积分
    await connection.execute(
      'UPDATE users SET points = ?, total_spent_points = total_spent_points + ? WHERE id = ?',
      [newPoints, pointsAmount, userId]
    );

    // 提交事务
    await connection.commit();

    console.log(`✅ 智能积分消耗成功: 用户${userId} 消费${pointsAmount}积分`, {
      transactionId,
      consumptionDetails: consumptionLogs
    });

    return {
      success: true,
      transactionId,
      consumptionLogs
    };

  } catch (error) {
    // 回滚事务
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('积分消耗事务回滚失败:', rollbackError);
      }
    }
    console.error('智能积分消耗失败:', error);
    return { success: false };
  } finally {
    // 关闭连接
    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        console.error('关闭数据库连接失败:', closeError);
      }
    }
  }
};

// 获取用户积分交易历史
export const getUserPointsHistory = async (
  userId: number,
  limit = 20,
  offset = 0
): Promise<PointsTransaction[]> => {
  try {
    const results = await executeQuery(
      `SELECT * FROM points_transactions
       WHERE user_id = ?
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    ) as PointsTransaction[];

    return results;
  } catch (error) {
    console.error('获取积分历史失败:', error);
    return [];
  }
};
