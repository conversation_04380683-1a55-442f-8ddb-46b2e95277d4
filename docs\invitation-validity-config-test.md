# 邀请活动积分有效期配置测试指南

## 🎯 测试目标
验证公告横幅能够正确显示管理员配置的邀请活动积分有效期，而不是显示模糊的"由系统设置决定"。

## 📋 测试步骤

### 1. 初始化配置
在Navicat中执行以下SQL：
```sql
-- 添加活动积分配置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`, `is_active`) VALUES
('activity_points_config', 
 '{"invitation": {"validity_days": 15, "description": "邀请活动积分", "enabled": true}}', 
 'json', 
 '不同活动积分有效期配置', 
 'points', 
 1)
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = NOW();
```

### 2. 验证配置读取
访问测试API：`http://localhost:3141/api/test/announcement-config`

应该看到类似输出：
```json
{
  "success": true,
  "data": {
    "invitation_validity_days": 15,
    "final_config": {
      "invitation_validity_days": 15,
      "invitation_points_per_user": 100,
      "max_invitations_per_user": 10
    }
  }
}
```

### 3. 测试不同有效期设置

#### 设置为20天：
```sql
UPDATE system_settings 
SET setting_value = JSON_SET(setting_value, '$.invitation.validity_days', 20)
WHERE setting_key = 'activity_points_config';
```

#### 设置为7天：
```sql
UPDATE system_settings 
SET setting_value = JSON_SET(setting_value, '$.invitation.validity_days', 7)
WHERE setting_key = 'activity_points_config';
```

#### 设置为30天：
```sql
UPDATE system_settings 
SET setting_value = JSON_SET(setting_value, '$.invitation.validity_days', 30)
WHERE setting_key = 'activity_points_config';
```

### 4. 前端显示验证
每次修改配置后：
1. 刷新页面 `http://localhost:3141`
2. 查看公告横幅
3. 点击"立即参与"按钮
4. 在弹窗的"活动规则"部分应该看到：
   ```
   • 积分为活动积分，有效期 [配置的天数] 天
   ```

## 🔍 预期结果

### 配置15天时：
```
• 积分为活动积分，有效期 15 天
```

### 配置20天时：
```
• 积分为活动积分，有效期 20 天
```

### 配置7天时：
```
• 积分为活动积分，有效期 7 天
```

## 🛠️ 故障排除

### 如果显示默认值15天：
1. 检查数据库配置是否正确保存
2. 检查API返回的配置数据
3. 清除浏览器缓存重新测试

### 验证SQL：
```sql
-- 查看当前配置
SELECT 
    setting_key,
    JSON_EXTRACT(setting_value, '$.invitation.validity_days') as validity_days,
    setting_value
FROM system_settings 
WHERE setting_key = 'activity_points_config';
```

### 测试API：
```bash
# 测试公告设置API
curl http://localhost:3141/api/system/announcement-settings

# 测试配置读取API  
curl http://localhost:3141/api/test/announcement-config
```

## 📊 配置管理

### 完整的活动配置示例：
```json
{
  "invitation": {
    "validity_days": 15,
    "description": "邀请活动积分",
    "enabled": true
  },
  "registration": {
    "validity_days": 30,
    "description": "注册奖励积分", 
    "enabled": true
  },
  "special_event": {
    "validity_days": 7,
    "description": "特殊活动积分",
    "enabled": false
  }
}
```

### 批量更新配置：
```sql
UPDATE system_settings 
SET setting_value = '{"invitation": {"validity_days": 25, "description": "邀请活动积分", "enabled": true}, "registration": {"validity_days": 45, "description": "注册奖励积分", "enabled": true}}'
WHERE setting_key = 'activity_points_config';
```

## ✅ 成功标准
- 公告横幅正确显示管理员配置的有效期天数
- 修改数据库配置后，前端立即反映变化
- 不同活动类型可以有不同的有效期设置
- 系统向后兼容，即使没有新配置也能正常工作
