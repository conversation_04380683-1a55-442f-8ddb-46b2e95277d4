import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";
import { getUserInvitationStats } from "@/lib/invitation-service";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    console.log('🔍 开始调试邀请上限问题...');

    // 1. 检查邀请码 WMQESB 对应的邀请者
    const inviterRows = await executeQuery(
      'SELECT id, nickname, invite_code, invitation_count FROM users WHERE invite_code = ?',
      ['WMQESB']
    ) as any[];
    
    if (inviterRows.length === 0) {
      return NextResponse.json({
        success: false,
        error: '邀请码 WMQESB 不存在！'
      });
    }
    
    const inviter = inviterRows[0];
    console.log('✅ 邀请者信息:', inviter);

    // 2. 检查系统设置
    const settingsRows = await executeQuery(
      `SELECT setting_key, setting_value FROM system_settings 
       WHERE setting_key IN ('invitation_enabled', 'max_invitations_per_user') 
       AND is_active = 1`
    ) as any[];
    
    const settings: Record<string, any> = {};
    settingsRows.forEach((row: any) => {
      settings[row.setting_key] = row.setting_value;
    });
    
    console.log('📋 系统设置:', settings);

    // 3. 检查邀请记录详情
    const invitationRecords = await executeQuery(
      `SELECT 
         id, invited_user_id, invite_code, invitation_status, 
         points_awarded, registered_at, created_at
       FROM user_invitations 
       WHERE inviter_user_id = ? 
       ORDER BY created_at DESC`,
      [inviter.id]
    ) as any[];
    
    console.log('📝 邀请记录详情:', invitationRecords);

    // 4. 使用现有函数获取统计
    const stats = await getUserInvitationStats(inviter.id);
    console.log('📊 统计结果:', stats);

    // 5. 手动计算统计
    const manualStats = {
      total_invitations: invitationRecords.length,
      successful_invitations: invitationRecords.filter(r => r.invitation_status === 'registered').length,
      pending_invitations: invitationRecords.filter(r => r.invitation_status === 'pending').length,
      total_points_earned: invitationRecords
        .filter(r => r.invitation_status === 'registered')
        .reduce((sum, r) => sum + (r.points_awarded || 0), 0)
    };

    const maxInvitations = parseInt(settings.max_invitations_per_user || '10');
    
    // 6. 比较不同的计算方式
    const remainingByTotal = Math.max(0, maxInvitations - manualStats.total_invitations);
    const remainingBySuccessful = Math.max(0, maxInvitations - manualStats.successful_invitations);

    console.log('🔍 计算对比:', {
      maxInvitations,
      totalInvitations: manualStats.total_invitations,
      successfulInvitations: manualStats.successful_invitations,
      remainingByTotal,
      remainingBySuccessful,
      currentLogic: stats.remaining_invitations
    });

    return NextResponse.json({
      success: true,
      data: {
        inviter,
        settings,
        invitationRecords,
        stats,
        manualStats,
        comparison: {
          maxInvitations,
          totalInvitations: manualStats.total_invitations,
          successfulInvitations: manualStats.successful_invitations,
          remainingByTotal,
          remainingBySuccessful,
          currentLogic: stats.remaining_invitations
        },
        analysis: {
          shouldUseTotal: remainingByTotal <= 0,
          shouldUseSuccessful: remainingBySuccessful <= 0,
          currentBehavior: stats.remaining_invitations <= 0,
          recommendation: '应该使用 successful_invitations 来计算剩余次数'
        }
      }
    });

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
