"use client";
import { useState, useRef, useCallback } from "react";
import { flushSync } from "react-dom";
import { AskAI } from "@/components/editor/ask-ai";
import { EnhancedChat } from "@/components/editor/chat-interface/enhanced-chat";
import { StylePanel } from "@/components/editor/style-panel";
import { ChatMessage } from "@/lib/project-manager";

interface HtmlHistoryItem {
  html: string;
  createdAt: Date;
  prompt: string;
}
import { PerformanceLogger } from "@/lib/performance-logger";
import { getSafeClassName } from "@/lib/utils";

// 🎯 精确的元素类型识别工具函数
const getElementType = (element: HTMLElement): string => {
  const tagName = element.tagName.toLowerCase();
  // 🚨 关键修复：使用安全的className获取函数
  const className = getSafeClassName(element);
  const role = element.getAttribute('role') || '';
  const text = element.textContent?.trim() || '';
  const hasText = text.length > 0;
  const hasChildren = element.children.length > 0;

  // 第一优先级：明确的语义标签
  if (tagName === 'button') return '按钮元素';
  if (tagName === 'a') return '链接元素';
  if (tagName === 'img') return '图片元素';
  if (tagName === 'input') return '输入框';
  if (tagName === 'select') return '下拉选择';
  if (tagName === 'textarea') return '文本域';
  if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) return `标题元素 (${tagName.toUpperCase()})`;
  if (tagName === 'p') return '段落文本';
  if (tagName === 'nav') return '导航元素';
  if (tagName === 'header') return '页头元素';
  if (tagName === 'footer') return '页脚元素';
  if (tagName === 'main') return '主内容区';

  // 第二优先级：通过role属性识别
  if (role === 'button') return '按钮元素';
  if (role === 'link') return '链接元素';
  if (role === 'textbox') return '输入框';

  // 第三优先级：通过className识别按钮样式
  const buttonPatterns = [
    /\bbtn\b/i, /\bbutton\b/i, /\bclick\b/i,
    /\bprimary\b/i, /\bsecondary\b/i, /\bsubmit\b/i
  ];
  if (buttonPatterns.some(pattern => pattern.test(className))) {
    return '按钮元素';
  }

  // 第四优先级：div和span的精确分类
  if (tagName === 'span' || tagName === 'div') {
    // 检查是否有点击事件
    const hasClickHandler = element.onclick !== null ||
                           element.getAttribute('onclick') !== null ||
                           className.includes('cursor-pointer');
    if (hasClickHandler && hasText) {
      return '可点击元素';
    }

    if (hasText && !hasChildren) return '文本内容';
    if (hasChildren && !hasText) return '容器元素';
    if (hasChildren && hasText) return '混合容器';
    return '空容器';
  }

  return hasText ? '文本元素' : '布局元素';
};

const getElementSelector = (element: HTMLElement): string => {
  const path: string[] = [];
  let current: Element | null = element;
  
  while (current && current !== document.body) {
    let selector = current.tagName.toLowerCase();
    
    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break;
    }
    
    if (current.className) {
      const classes = current.className.trim().split(/\s+/).filter(cls => !cls.includes('hovered'));
      if (classes.length > 0) {
        selector += `.${classes.join('.')}`;
      }
    }
    
    path.unshift(selector);
    current = current.parentElement;
  }
  
  return path.join(' > ');
};

const getParentContext = (element: HTMLElement): {tag: string, type: string, role: string} | null => {
  const parent = element.parentElement;
  if (!parent || parent === document.body) return null;
  
  const tag = parent.tagName.toLowerCase();
  const type = getElementType(parent);
  
  let role = '容器';
  if (['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'].includes(tag)) {
    role = '页面区块';
  } else if (['table', 'ul', 'ol', 'dl'].includes(tag)) {
    role = '数据结构';
  }
  
  return { tag, type, role };
};

const getSiblingContext = (element: HTMLElement): Array<{tag: string, text: string, type: string}> => {
  const siblings = Array.from(element.parentElement?.children || [])
    .filter(sibling => sibling !== element)
    .slice(0, 3)
    .map(sibling => ({
      tag: sibling.tagName.toLowerCase(),
      text: (sibling.textContent?.trim() || '').substring(0, 30),
      type: getElementType(sibling as HTMLElement)
    }));
  
  return siblings;
};

interface ChatAreaProps {
  // 编辑器状态
  html: string;
  setHtml: ((newHtml: string, action?: string) => void) | React.Dispatch<React.SetStateAction<string>>; // 🎯 修复：支持带撤销历史的setHtml
  htmlHistory: HtmlHistoryItem[];
  setHtmlHistory: React.Dispatch<React.SetStateAction<HtmlHistoryItem[]>>;
  
  // AI工作状态
  isAiWorking: boolean;
  setIsAiWorking: React.Dispatch<React.SetStateAction<boolean>>;
  
  // 编辑模式状态
  isEditableModeEnabled: boolean;
  setIsEditableModeEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  selectedElement: HTMLElement | null;
  setSelectedElement: React.Dispatch<React.SetStateAction<HTMLElement | null>>;
  
  // 聊天历史
  chatHistory: ChatMessage[];
  setChatHistory: React.Dispatch<React.SetStateAction<ChatMessage[]>>;
  
  // 布局状态
  currentTab: string;
  setCurrentTab: (tab: "chat" | "preview") => void;
  isLargeScreen: boolean;
  isClient: boolean;
  isProjectSwitching: boolean;
  
  // 🎯 统一的AI输入框状态
  sharedInputPrompt: string;
  setSharedInputPrompt: React.Dispatch<React.SetStateAction<string>>;
  
  // 回调函数
  onSwitchToEditor: (htmlContent?: string) => void;
  onSuccess: (newHtml: string) => void;
  onNewPrompt: (prompt: string) => void;
  onSwitchToPreview?: () => void;
  onScrollToBottom?: () => void;
  // 🎯 新增：实时流式内容回调
  onStreamingHtml?: (streamContent: string) => void;
  // 🎯 新增：流式HTML内容
  streamingHtml?: string;
  // 🎯 新增：当前版本号（用于手动编辑保存）
  currentVersionNumber?: number;
}

export function ChatArea({
  html,
  setHtml,
  htmlHistory,
  setHtmlHistory,
  isAiWorking,
  setIsAiWorking,
  isEditableModeEnabled,
  setIsEditableModeEnabled,
  selectedElement,
  setSelectedElement,
  chatHistory,
  setChatHistory,
  currentTab,
  setCurrentTab,
  isLargeScreen,
  isClient,
  isProjectSwitching,
  sharedInputPrompt,
  setSharedInputPrompt,
  onSwitchToEditor,
  onSuccess,
  onNewPrompt,
  /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
  streamingHtml,
  currentVersionNumber,
}: ChatAreaProps) {
  const [chatTabMode, setChatTabMode] = useState<"chat" | "edit">("chat");
  const editor = useRef<HTMLDivElement>(null);

  const handleSwitchToPreview = () => setCurrentTab("preview");

  // 🎯 统一的AI调用处理函数 - 性能优化版本
  const handleUnifiedAI = useCallback(async (prompt: string, fromEditMode: boolean = false, model?: string, images?: string[]) => {
    PerformanceLogger.startTimer('ai-edit-request');
    PerformanceLogger.log('🎯 ChatArea: 统一AI调用', { prompt, fromEditMode });
    
    // 🎯 关键：发送后立即切换到聊天页（如果来自编辑模式）
    if (fromEditMode) {
      setChatTabMode('chat');
      // 关闭编辑模式
      if (isEditableModeEnabled) {
        setIsEditableModeEnabled(false);
      }
    }
    
    // 🔧 关键修复：先添加用户消息到聊天历史
    const userMessage = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-user`,
      type: 'user' as const,
      content: prompt,
      timestamp: new Date(),
    };
    
    // 🔧 立即添加用户消息到聊天历史
    setChatHistory(prev => [...prev, userMessage]);
    console.log('✅ ChatArea: 用户消息已添加到聊天历史', { messageId: userMessage.id, prompt });
    
    // 清空共享输入框
    setSharedInputPrompt("");
    
    // 通知父组件处理新的prompt
    onNewPrompt(prompt);
    
    // 🔧 关键修复：无论聊天模式还是编辑模式，都需要执行AI请求
    // 🎯 添加AI消息并发送PUT请求
    {
      // 🔧 关键修复：立即添加正在生成的AI消息
      const putRequestCount = chatHistory.filter(msg => 
        msg.type === 'ai' && msg.requestType === 'PUT'
      ).length;
      const versionNumber = putRequestCount + 2; // V2开始递增
      
      const aiMessage = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-ai`,
        type: 'ai' as const,
        content: '正在修改中...',
        timestamp: new Date(),
        isGenerating: true,
        requestType: 'PUT' as 'POST' | 'PUT', // 🎯 编辑器固定为PUT（修改项目）
        versionNumber: versionNumber, // 🎯 设置版本号
      };
      
      setChatHistory(prev => [...prev, aiMessage]);
      console.log('✅ ChatArea: 正在生成的AI消息已添加', { messageId: aiMessage.id });
      
      try {
        PerformanceLogger.log('🚀 ChatArea: 开始编辑模式AI请求', prompt);
        
        // 🎯 构建PUT请求参数
        interface PutRequestBody {
          prompt: string;
          html: string;
          provider?: string;
          model?: string;
          images?: string[];
          selectedElementHtml?: string;
          elementContext?: {
            elementType: string;
            tagName: string;
            selector: string;
            textContent: string;
            parentContext?: {tag: string, type: string, role: string} | null;
            siblings?: Array<{tag: string, text: string, type: string}>;
          };
        }
        
        // 🔧 性能优化：简化模型映射逻辑
        const modelMapping: Record<string, string> = {
          "loomrun-1.2ds": "deepseek-chat",
          "loomrun-1.6db": "doubao-seed-1-6-250615",
          "loomrun-zoom": "gemini-2.5-flash"
        };
        
        const actualModel = (model && modelMapping[model]) || "deepseek-chat";
        let provider: string;
        if (actualModel.includes('gemini')) {
          provider = 'gemini-official';
        } else if (actualModel.includes('doubao')) {
          provider = 'doubao-official';
        } else {
          provider = 'deepseek-official';
        }
        
        console.log('🔧 ChatArea: PUT请求模型配置', {
          selectedModel: model,
          actualModel,
          provider
        });
        
        // 🎯 关键逻辑：AI始终使用当前iframe DOM状态（用户看到的预览版本）
        const htmlToUse = html; // 使用当前预览版本HTML - 这是正确的选择
        
        console.log('🎯 AI使用当前预览版本HTML', {
          hasSelectedElement: !!selectedElement,
          htmlLength: htmlToUse.length,
          strategy: '基于用户看到的预览效果进行修改'
        });
        
        const requestBody: PutRequestBody = {
          prompt: prompt,
          html: htmlToUse, // 🎯 使用正确的版本HTML
          provider: provider,
          model: actualModel,
          images: images || []
        };
        
        // 🎯 如果选择了元素，添加元素上下文
        if (selectedElement) {
          // 获取选中元素的HTML
          const selectedElementHtml = selectedElement.outerHTML;

          // 构建元素上下文信息
          const elementContext = {
            elementType: getElementType(selectedElement),
            tagName: selectedElement.tagName.toLowerCase(),
            selector: getElementSelector(selectedElement),
            textContent: selectedElement.textContent?.trim() || '',
            parentContext: getParentContext(selectedElement),
            siblings: getSiblingContext(selectedElement)
          };

          requestBody.selectedElementHtml = selectedElementHtml;
          requestBody.elementContext = elementContext;

          console.log('🎯 编辑模式: 添加元素上下文', {
            elementType: elementContext.elementType,
            tagName: elementContext.tagName,
            hasSelectedHtml: !!selectedElementHtml,
            usingPreviewVersion: true
          });

          // 🎯 关键修复：发送请求后立即清理选中元素标签
          setSelectedElement(null);
          console.log('✅ ChatArea: 请求已发送，选中元素标签已清理');
        }
        
        console.log('🔧 ChatArea: 发送PUT请求', {
          hasSelectedElement: !!selectedElement,
          htmlLength: htmlToUse.length,
          prompt: prompt.substring(0, 50) + '...',
          versionStrategy: selectedElement ? '当前预览版本' : '最新版本'
        });
        
        const response = await fetch('/api/ask-ai', {
          method: 'PUT', // 🔧 关键修复：使用PUT而不是POST
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        // 🎯 精准处理积分不足错误（402状态码）
        if (response.status === 402) {
          const errorData = await response.json();
          console.log('⚠️ ChatArea: 积分不足，阻止消息发送', {
            pointsRequired: errorData.pointsRequired,
            currentPoints: errorData.currentPoints,
            message: errorData.message
          });

          // 🎯 友好的积分不足提示 - 使用信息提示而不是错误
          const friendlyMessage = `需要 ${errorData.pointsRequired} 积分，当前余额 ${errorData.currentPoints} 积分`;
          toast.info(`💰 ${friendlyMessage}`, {
            duration: 6000,
            action: {
              label: '立即充值',
              onClick: () => window.open('/points-pro', '_blank')
            }
          });

          // 🎯 关键：抛出特殊错误，让下层处理消息回退
          const pointsError = new Error(friendlyMessage) as Error & {
            isPointsInsufficient: boolean;
            originalPrompt: string;
          };
          pointsError.isPointsInsufficient = true;
          pointsError.originalPrompt = prompt;
          throw pointsError;
        }

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 🔧 PUT请求返回的是修改后的HTML，不是流式响应
        const result = await response.json();
        
        if (result.ok && result.html) {
          console.log('✅ ChatArea: PUT请求成功', {
            htmlLength: result.html.length,
            blocksProcessed: result.blocksProcessed,
            updatedLines: result.updatedLines?.length || 0
          });
          
          // 🔧 立即更新预览区域的HTML内容
          setHtml(result.html);
          
          // 🔧 关键修复：清除选中的元素
          setSelectedElement(null);
          console.log('✅ ChatArea: 选中元素已清除');
          
          // 🔧 手动更新AI消息状态 - 使用flushSync确保立即更新
          console.log('🔍 ChatArea: 准备更新AI消息状态');
          flushSync(() => {
            setChatHistory(prev => {
              const updated = [...prev];
              const lastMessage = updated[updated.length - 1];
              if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
                // 🔧 关键修复：创建全新的消息对象确保React检测到变化
                const updatedMessage = {
                  ...lastMessage,
                  isGenerating: false,
                  content: '已完成修改',
                  htmlContent: result.html,
                  timestamp: new Date(), // 更新时间戳确保变化
                  versionNumber: lastMessage.versionNumber || 2 // 🎯 保持版本号
                };
                updated[updated.length - 1] = updatedMessage;
                console.log('✅ ChatArea: AI消息状态已更新为完成', { 
                  messageId: updatedMessage.id,
                  isGenerating: updatedMessage.isGenerating,
                  hasHtmlContent: !!updatedMessage.htmlContent
                });
              }
              return updated;
            });
          });
          
          console.log('🔧 ChatArea: flushSync完成，状态应该已立即更新');
          
          // 🚀 性能优化：异步保存对话到数据库，不阻塞UI更新
          setTimeout(async () => {
            try {
              console.log('💾 ChatArea: 异步保存对话到数据库');
              const projectId = window.location.pathname.split('/')[2];
              const saveResponse = await fetch(`/api/me/projects/${projectId}/chat`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  userPrompt: prompt,
                  aiHtmlContent: result.html,
                  syncToRecentAIMessage: true, // 🎯 标记需要同步到最近的AI消息
                }),
              });
              
              if (saveResponse.ok) {
                console.log('✅ ChatArea: 对话已异步保存到数据库');
                
                // 🎯 关键修复：PUT请求成功后，获取新版本号并广播版本更新事件
                const saveData = await saveResponse.json();
                const versionNumber = saveData.result?.versionNumber;
                
                if (versionNumber) {
                  console.log('🎯 ChatArea: PUT请求创建了新版本，准备广播', {
                    projectId,
                    versionNumber
                  });
                  
                  // 🚀 性能优化：简化广播逻辑
                  const versionEventData = {
                    type: 'version-created',
                    projectId: projectId,
                    versionNumber: versionNumber ? parseInt(versionNumber) : undefined,
                    timestamp: Date.now()
                  };

                  console.log('📢 ChatArea: PUT请求广播版本创建事件', versionEventData);

                  // 🔧 并行广播，提高效率
                  const broadcastPromises = [];
                  
                  // 方法1：BroadcastChannel
                  if ('BroadcastChannel' in window) {
                    broadcastPromises.push(
                      new Promise<void>((resolve) => {
                        try {
                          const channel = new BroadcastChannel('version-updates');
                          channel.postMessage(versionEventData);
                          channel.close();
                          resolve();
                        } catch (error) {
                          console.warn('BroadcastChannel 广播失败:', error);
                          resolve();
                        }
                      })
                    );
                  }

                  // 方法2：localStorage事件
                  broadcastPromises.push(
                    new Promise<void>((resolve) => {
                      try {
                        localStorage.setItem('version-created-event', JSON.stringify(versionEventData));
                        setTimeout(() => {
                          localStorage.removeItem('version-created-event');
                        }, 100);
                        resolve();
                      } catch (error) {
                        console.warn('localStorage 广播失败:', error);
                        resolve();
                      }
                    })
                  );

                  // 方法3：自定义事件
                  broadcastPromises.push(
                    new Promise<void>((resolve) => {
                      try {
                        window.dispatchEvent(new CustomEvent('refreshVersionList', {
                          detail: versionEventData
                        }));
                        resolve();
                      } catch (error) {
                        console.warn('CustomEvent 广播失败:', error);
                        resolve();
                      }
                    })
                  );

                  // 方法4：定时检查触发器
                  broadcastPromises.push(
                    new Promise<void>((resolve) => {
                      try {
                        localStorage.setItem('version-check-trigger', projectId);
                        resolve();
                      } catch (error) {
                        console.warn('定时检查触发器设置失败:', error);
                        resolve();
                      }
                    })
                  );

                  // 🚀 并行执行所有广播操作
                  await Promise.allSettled(broadcastPromises);
                  console.log('✅ ChatArea: PUT请求版本创建事件已广播');
                }
              } else {
                console.error('❌ ChatArea: 异步保存对话失败', saveResponse.status);
              }
            } catch (saveError) {
              console.error('❌ ChatArea: 异步保存对话异常', saveError);
            }
          }, 0); // 立即异步执行，不阻塞当前流程
        } else {
          throw new Error(result.message || 'PUT请求失败');
        }

        PerformanceLogger.endTimer('ai-edit-request');

      } catch (error: any) {
        PerformanceLogger.endTimer('ai-edit-request');
        PerformanceLogger.error('❌ ChatArea: 编辑模式AI请求失败:', error);

        // 🔧 关键修复：即使失败也清除选中的元素
        setSelectedElement(null);
        console.log('❌ ChatArea: 请求失败，选中元素已清除');

        // 🎯 特殊处理积分不足错误 - 清理聊天历史并回退消息
        if (error?.isPointsInsufficient) {
          console.log('🔄 ChatArea: 积分不足，清理聊天历史并回退消息', {
            originalPrompt: error.originalPrompt
          });

          // 🎯 关键：移除刚添加的用户消息和AI消息
          flushSync(() => {
            setChatHistory(prev => {
              // 移除最后两条消息（用户消息和AI消息）
              const updated = prev.slice(0, -2);
              console.log('🔄 ChatArea: 已移除积分不足时的消息', {
                removedCount: 2,
                remainingCount: updated.length
              });
              return updated;
            });
          });

          // 🎯 将消息回退到输入框
          setSharedInputPrompt(error.originalPrompt);
          console.log('🔄 ChatArea: 消息已回退到输入框');

          // 积分不足的错误提示已经在上层处理了，这里不需要重复提示
          return;
        }

        // 🔍 其他错误的处理 - 更新AI消息状态为错误
        flushSync(() => {
          setChatHistory(prev => {
            const updated = [...prev];
            const lastMessage = updated[updated.length - 1];
            if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
              // 🔧 创建全新的消息对象
              const updatedMessage = {
                ...lastMessage,
                isGenerating: false,
                content: '修改失败，请重试',
                timestamp: new Date()
              };
              updated[updated.length - 1] = updatedMessage;
              console.log('❌ ChatArea: AI消息状态已更新为失败', { messageId: updatedMessage.id });
            }
            return updated;
          });
        });
      }
    }
  }, [onNewPrompt, html, selectedElement, setHtml, setChatHistory, setSharedInputPrompt, setChatTabMode, isEditableModeEnabled, setIsEditableModeEnabled, setSelectedElement, chatHistory]);

  return (
    <div
      className={`flex flex-col z-10 ${
        (isClient && isLargeScreen) ? "w-full h-full" : "w-full flex-1"
      } min-h-0 bg-card ${
        (isClient && isLargeScreen) ? "" : ""
      }`}
    >
      {/* 标签栏 - 聊天/编辑切换 - 浅色模式优化 */}
      <div className="flex-shrink-0 flex items-start bg-card border-b border-border shadow-sm">
        <div className="flex ml-3 py-1">
          <button
            onClick={() => {
              setChatTabMode('chat');
              // 关闭编辑模式
              if (isEditableModeEnabled) {
                setIsEditableModeEnabled(false);
              }
            }}
            className={`px-3 py-1.5 text-sm font-medium transition-colors duration-200 rounded-t-md ${
              chatTabMode === 'chat' 
                ? 'bg-secondary text-foreground border-b-2 border-blue-500 shadow-sm' 
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50 dark:hover:bg-secondary/50'
            }`}
          >
            聊天
          </button>
          <button
            onClick={() => {
              setChatTabMode('edit');
              // 启用编辑模式
              if (!isEditableModeEnabled) {
                // 移动端：先切换到预览模式，再启用编辑
                if (window.innerWidth < 1024 && currentTab !== "preview") {
                  setCurrentTab("preview");
                  setTimeout(() => {
                    setIsEditableModeEnabled(true);
                  }, 300);
                } else {
                  setIsEditableModeEnabled(true);
                }
              }
            }}
            className={`px-3 py-1.5 text-sm font-medium transition-colors duration-200 rounded-t-md ml-1 ${
              chatTabMode === 'edit' 
                ? 'bg-secondary text-foreground border-b-2 border-blue-500 shadow-sm' 
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50 dark:hover:bg-secondary/50'
            }`}
          >
            编辑
          </button>
        </div>
      </div>
        
        {/* 聊天内容区域 */}
        <div
          ref={editor}
          className="flex-1 flex flex-col overflow-hidden"
        >
          {chatTabMode === 'chat' ? (
            // 聊天模式内容
            <>
              {isProjectSwitching && (
                 <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
                   <div className="bg-card rounded-xl p-8 shadow-2xl border border-border max-w-sm">
                     <div className="flex flex-col items-center gap-4">
                       {/* 双环加载动画 */}
                       <div className="relative">
                         <div className="w-10 h-10 border-3 border-muted border-t-blue-500 rounded-full animate-spin"></div>
                         <div className="absolute inset-1 w-6 h-6 border-2 border-blue-500/40 border-b-transparent rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '0.6s' }}></div>
                       </div>
                       
                       {/* 切换文字 */}
                       <div className="text-center">
                         <p className="text-foreground font-semibold mb-1">正在切换项目</p>
                         <p className="text-muted-foreground text-sm">请稍候...</p>
                       </div>
                       
                       {/* 进度指示器 */}
                       <div className="flex items-center gap-1.5">
                         <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                         <div className="w-1.5 h-1.5 bg-blue-500/70 rounded-full animate-pulse" style={{ animationDelay: '0.15s' }}></div>
                         <div className="w-1.5 h-1.5 bg-blue-500/40 rounded-full animate-pulse" style={{ animationDelay: '0.3s' }}></div>
                       </div>
                     </div>
                   </div>
                 </div>
               )}
               
               {/* 聊天界面 */}
               <EnhancedChat
                   html={html}
                   setHtml={setHtml}
                   htmlHistory={htmlHistory}
                   setHtmlHistory={setHtmlHistory}
                   isAiWorking={isAiWorking}
                   setIsAiWorking={setIsAiWorking}
                   isEditableModeEnabled={isEditableModeEnabled}
                   setIsEditableModeEnabled={setIsEditableModeEnabled}
                   selectedElement={selectedElement}
                   setSelectedElement={setSelectedElement}
                   chatHistory={chatHistory}
                   setChatHistory={setChatHistory}
                   onSwitchToEditor={onSwitchToEditor}
                   onSuccess={onSuccess}
                   onSwitchToPreview={handleSwitchToPreview}
                   isStandalone={false}

                   completeAIGeneration={(htmlContent: string) => {
                     // 🔧 关键修复：只用于POST请求（新建项目），PUT请求不需要这个回调
                     console.log('🎯 ChatArea: AI生成完成回调（仅用于POST请求）', {
                       htmlContentLength: htmlContent.length
                     });
                     
                     // 检查最后一条消息是否是正在生成的AI消息 - 使用flushSync确保立即更新
                     flushSync(() => {
                       setChatHistory(prev => {
                         const updated = [...prev];
                         const lastMessage = updated[updated.length - 1];
                         if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
                           // 🔧 创建全新的消息对象
                           const updatedMessage = {
                             ...lastMessage,
                             isGenerating: false,
                             htmlContent: htmlContent,
                             timestamp: new Date()
                           };
                           updated[updated.length - 1] = updatedMessage;
                           console.log('✅ ChatArea: AI消息生成状态已更新为完成（POST请求）', { messageId: updatedMessage.id });
                         }
                         return updated;
                       });
                     });
                   }}
                   stopAIGeneration={(errorMessage?: string) => {
                     // 🔧 关键修复：不要在这里更新聊天历史，全局状态管理器已经处理了
                     console.log('🎯 ChatArea: AI生成停止通知（全局状态已处理聊天历史）', {
                       errorMessage: errorMessage || '无错误信息'
                     });
                     // 全局状态管理器已经更新了聊天历史中的isGenerating状态
                   }}
                 />
                 
                 {/* 🎯 统一的AI输入框 - 聊天模式也使用这个输入框 */}
                 <div className="flex-shrink-0 p-4 pb-6 border-t border-neutral-700">
                   <AskAI
                     inputPrompt={sharedInputPrompt}
                     setInputPrompt={setSharedInputPrompt}
                     onAskAI={(prompt, model, images) => handleUnifiedAI(prompt, false, model, images)}
                     selectedElement={selectedElement}
                     setSelectedElement={setSelectedElement}
                     placeholder={chatHistory.length === 0 
                       ? "告诉我你想要什么..." 
                       : "继续对话..."
                     }
                     isWelcomeMode={false}
                     isStandalone={false}
                   />
                 </div>
             </>
          ) : (
            // 编辑模式内容
            <div className="w-full flex-1 min-h-0 flex flex-col editor-mode">
              {selectedElement ? (
                // 🎯 有选中元素时显示样式面板
                <>
                  <div className="flex-1 min-h-0">
                    <StylePanel
                      selectedElement={selectedElement}
                      currentVersionNumber={currentVersionNumber} // 🎯 关键修复：传递当前版本号
                      onClose={() => setSelectedElement(null)}
                      onApplyStyles={(styles) => {
                        // 应用样式到选中元素并保存到HTML
                        if (selectedElement) {
                          // 获取更新后的HTML内容
                          const iframe = document.querySelector('iframe');
                          if (iframe && iframe.contentDocument) {
                            const updatedHtml = iframe.contentDocument.documentElement.outerHTML;
                            // 🎯 关键修复：使用带撤销历史的setHtml，并提供描述性的action
                            if (typeof setHtml === 'function' && setHtml.length > 1) {
                              // 这是带撤销历史的setHtml函数
                              (setHtml as (newHtml: string, action?: string) => void)(
                                updatedHtml,
                                `样式修改: ${selectedElement.tagName} ${Object.keys(styles).join(', ')}`
                              );
                            } else {
                              // 这是普通的setHtml函数
                              (setHtml as React.Dispatch<React.SetStateAction<string>>)(updatedHtml);
                            }
                            console.log('✅ 样式已应用并保存到HTML（带撤销历史）:', selectedElement.tagName, styles);
                          }
                        }
                      }}
                      onHtmlChange={(newHtml: string) => {
                        // 🎯 关键修复：添加onHtmlChange回调，连接到带撤销历史的setHtml
                        if (typeof setHtml === 'function' && setHtml.length > 1) {
                          // 这是带撤销历史的setHtml函数
                          (setHtml as (newHtml: string, action?: string) => void)(
                            newHtml,
                            `样式保存: ${selectedElement?.tagName || '元素'}`
                          );
                        } else {
                          // 这是普通的setHtml函数
                          (setHtml as React.Dispatch<React.SetStateAction<string>>)(newHtml);
                        }
                        console.log('✅ 样式已保存到HTML（带撤销历史）:', selectedElement?.tagName);
                      }}
                      onPreviewStyles={(styles) => {
                        // 实时预览样式（临时应用，不保存）
                        console.log('👀 预览样式已应用:', selectedElement.tagName, styles);
                      }}
                      onSave={async (htmlContent: string, versionNumber?: number) => {
                        // 保存修改后的HTML到数据库
                        const projectId = window.location.pathname.split('/')[2];
                        if (projectId) {
                          // 🎯 关键修复：使用传递的版本号，如果没有则使用当前版本号
                          const targetVersionNumber = versionNumber || currentVersionNumber;

                          console.log('🔧 手动编辑保存到指定版本:', {
                            projectId,
                            targetVersionNumber,
                            htmlLength: htmlContent.length
                          });

                          const response = await fetch(`/api/me/projects/${projectId}`, {
                             method: 'PATCH',
                             headers: {
                               'Content-Type': 'application/json',
                             },
                             body: JSON.stringify({
                               action: 'manual_edit',
                               html_content: htmlContent,
                               version_number: targetVersionNumber // 🎯 关键修复：传递目标版本号
                             }),
                           });

                          if (!response.ok) {
                            throw new Error('保存失败');
                          }

                          const result = await response.json();
                          console.log('✅ 样式修改已保存到数据库', result);

                          // 🎯 关键修复：只有当编辑的版本是当前预览版本时，才更新HTML状态
                          if (targetVersionNumber === currentVersionNumber) {
                            setHtml(htmlContent);
                            console.log('🔄 已更新当前预览版本的HTML状态');
                          } else {
                            console.log('ℹ️ 编辑的不是当前预览版本，不更新HTML状态', {
                              editedVersion: targetVersionNumber,
                              currentVersion: currentVersionNumber
                            });
                          }

                          // 🔧 关键修复：更新聊天历史中对应版本的AI消息HTML内容
                          setChatHistory(prev => {
                            const updated = [...prev];
                            let updatedCount = 0;

                            // 查找所有对应版本的AI消息并更新
                            for (let i = 0; i < updated.length; i++) {
                              const msg = updated[i];
                              if (msg.type === 'ai' &&
                                  msg.htmlContent &&
                                  !msg.isGenerating &&
                                  msg.versionNumber === targetVersionNumber) {
                                updated[i] = {
                                  ...msg,
                                  htmlContent: htmlContent,
                                  timestamp: new Date()
                                };
                                updatedCount++;
                                console.log('🔄 已更新版本AI消息HTML内容', {
                                  messageIndex: i,
                                  messageId: msg.id,
                                  versionNumber: targetVersionNumber,
                                  htmlLength: htmlContent.length
                                });
                              }
                            }

                            console.log('✅ 聊天历史更新完成', {
                              targetVersion: targetVersionNumber,
                              updatedMessages: updatedCount
                            });

                            return updated;
                          });
                          
                          // 🔧 关键修复：广播版本更新事件，通知版本菜单刷新
                          const versionEventData = {
                            type: 'version-updated',
                            projectId: projectId,
                            versionNumber: targetVersionNumber, // 🎯 关键：包含具体的版本号
                            htmlContent: htmlContent, // 🎯 关键：包含更新后的HTML内容
                            timestamp: Date.now()
                          };

                          console.log('📢 手动编辑保存后广播版本更新事件', versionEventData);
                          
                          // 广播给版本菜单
                          if ('BroadcastChannel' in window) {
                            try {
                              const channel = new BroadcastChannel('version-updates');
                              channel.postMessage(versionEventData);
                              channel.close();
                            } catch (error) {
                              console.warn('BroadcastChannel 广播失败:', error);
                            }
                          }
                          
                          // localStorage事件
                          try {
                            localStorage.setItem('version-created-event', JSON.stringify(versionEventData));
                            setTimeout(() => {
                              localStorage.removeItem('version-created-event');
                            }, 100);
                          } catch (error) {
                            console.warn('localStorage 广播失败:', error);
                          }
                          
                          // 自定义事件
                          try {
                            window.dispatchEvent(new CustomEvent('refreshVersionList', {
                              detail: versionEventData
                            }));
                          } catch (error) {
                            console.warn('CustomEvent 广播失败:', error);
                          }
                        }
                      }}
                                             onSwitchToChat={() => {
                         // 🎯 完整切换到聊天模式
                         console.log('🔄 开始切换到聊天模式...');

                         // 1. 切换到聊天标签
                         setChatTabMode('chat');

                         // 2. 禁用编辑模式
                         setIsEditableModeEnabled(false);

                         // 3. 清除选中元素
                         setSelectedElement(null);

                         // 4. 切换到聊天区域（移动端）
                         setCurrentTab('chat');

                         console.log('✅ 已完整切换到聊天模式');
                       }}
                       onHtmlChange={(newHtml: string) => {
                         // 关键修复：同步HTML状态到编辑器
                         setHtml(newHtml);
                         console.log('🔄 ChatArea: HTML状态已同步到编辑器', {
                           htmlLength: newHtml.length
                         });
                       }}
                    />
                  </div>
                  
                  {/* 编辑模式下的AI输入框 - 始终在底部显示 */}
                  <div className="flex-shrink-0 p-4 pb-6 border-t border-border">
                    <AskAI
                      inputPrompt={sharedInputPrompt}
                      setInputPrompt={setSharedInputPrompt}
                      onAskAI={(prompt, model, images) => handleUnifiedAI(prompt, true, model, images)}
                      selectedElement={selectedElement}
                      setSelectedElement={setSelectedElement}
                      placeholder={`修改${selectedElement.tagName.toLowerCase()}元素...`}
                      isWelcomeMode={false}
                      isStandalone={false}
                    />
                  </div>
                </>
              ) : (
                // 🎯 没有选中元素时显示默认提示
                <>
                  <div className="flex-1 flex items-center justify-center">
                    {/* 🎯 编辑模式简化提示信息 */}
                    <div className="text-center p-6 max-w-sm">
                      <div className="mb-4">
                        <div className="w-12 h-12 mx-auto mb-3 bg-blue-500/10 dark:bg-blue-500/20 rounded-full flex items-center justify-center">
                          <span className="text-2xl">✏️</span>
                        </div>
                      </div>
                      
                      <h3 className="text-base font-medium text-foreground mb-2">编辑模式</h3>
                      <p className="text-muted-foreground text-sm leading-relaxed mb-3">
                        在右侧预览区域点击任意元素开始编辑
                      </p>
                      
                      <div className="bg-muted/30 border border-border rounded-lg p-3 mb-3">
                        <div className="flex items-center gap-2 justify-center">
                          <span className="text-lg">👆</span>
                          <p className="text-muted-foreground text-xs">
                            点击元素即可编辑样式和内容
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-xs text-muted-foreground/70">
                        💡 支持颜色、字体、间距等常用样式调整
                      </div>
                    </div>
                  </div>
                  
                  {/* 编辑模式下的AI输入框 */}
                  <div className="flex-shrink-0 p-4 pb-6">
                    <AskAI
                      inputPrompt={sharedInputPrompt}
                      setInputPrompt={setSharedInputPrompt}
                      onAskAI={(prompt, model, images) => handleUnifiedAI(prompt, true, model, images)}
                      selectedElement={selectedElement}
                      setSelectedElement={setSelectedElement}
                      placeholder="添加或修改内容..."
                      isWelcomeMode={false}
                      isStandalone={false}
                    />
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }