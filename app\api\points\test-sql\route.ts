import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const tests = [];

    // 1. 测试简单的积分交易查询
    try {
      const simpleQuery = `
        SELECT COUNT(*) as total 
        FROM points_transactions pt 
        WHERE pt.user_id = ?
      `;
      const simpleResult = await executeQuery(simpleQuery, [user.id]) as { total: number }[];
      
      tests.push({
        name: '简单查询测试',
        status: 'success',
        query: simpleQuery,
        result: simpleResult[0]?.total || 0,
        message: '基础查询正常'
      });
    } catch (error) {
      tests.push({
        name: '简单查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '基础查询失败'
      });
    }

    // 2. 测试联表查询
    try {
      const joinQuery = `
        SELECT 
          pt.id,
          pt.user_id,
          pt.transaction_type,
          pt.points_amount,
          pt.source_type,
          pt.created_at,
          upb.expires_at as balance_expires_at
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC
        LIMIT 5
      `;
      const joinResult = await executeQuery(joinQuery, [user.id]) as any[];
      
      tests.push({
        name: '联表查询测试',
        status: 'success',
        query: joinQuery,
        result: joinResult.length,
        data: joinResult,
        message: '联表查询正常'
      });
    } catch (error) {
      tests.push({
        name: '联表查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '联表查询失败'
      });
    }

    // 3. 测试带筛选条件的查询
    try {
      const filterQuery = `
        SELECT 
          pt.id,
          pt.transaction_type,
          pt.points_amount,
          pt.source_type,
          pt.points_type
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ? AND pt.transaction_type = ?
        ORDER BY pt.created_at DESC
        LIMIT 3
      `;
      const filterResult = await executeQuery(filterQuery, [user.id, 'earn']) as any[];
      
      tests.push({
        name: '筛选查询测试',
        status: 'success',
        query: filterQuery,
        result: filterResult.length,
        data: filterResult,
        message: '筛选查询正常'
      });
    } catch (error) {
      tests.push({
        name: '筛选查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '筛选查询失败'
      });
    }

    // 4. 测试完整的历史记录查询（模拟真实API）
    try {
      const fullQuery = `
        SELECT
          pt.id,
          pt.user_id,
          pt.transaction_type,
          pt.points_amount,
          pt.balance_before,
          pt.balance_after,
          pt.source_type,
          pt.points_type,
          pt.expires_at,
          pt.balance_record_id,
          pt.source_id,
          pt.description,
          pt.metadata,
          pt.created_at,
          upb.expires_at as balance_expires_at,
          upb.is_active as balance_is_active
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC, pt.id DESC
        LIMIT 10 OFFSET 0
      `;
      const fullResult = await executeQuery(fullQuery, [user.id]) as any[];

      tests.push({
        name: '完整历史查询测试',
        status: 'success',
        query: fullQuery,
        result: fullResult.length,
        data: fullResult.slice(0, 2), // 只返回前2条作为示例
        message: '完整查询正常'
      });
    } catch (error) {
      tests.push({
        name: '完整历史查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '完整查询失败'
      });
    }

    // 5. 检查用户基本信息和积分状态
    try {
      const userInfoQuery = `
        SELECT
          id,
          nickname,
          points,
          total_earned_points,
          total_spent_points,
          created_at
        FROM users
        WHERE id = ?
      `;
      const userInfoResult = await executeQuery(userInfoQuery, [user.id]) as any[];

      tests.push({
        name: '用户信息查询',
        status: 'success',
        query: userInfoQuery,
        result: userInfoResult.length,
        data: userInfoResult,
        message: '用户信息查询正常'
      });
    } catch (error) {
      tests.push({
        name: '用户信息查询',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '用户信息查询失败'
      });
    }

    // 6. 检查积分余额记录
    try {
      const balanceQuery = `
        SELECT
          id,
          points_type,
          points_amount,
          expires_at,
          is_active,
          created_at
        FROM user_points_balance
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
      `;
      const balanceResult = await executeQuery(balanceQuery, [user.id]) as any[];

      tests.push({
        name: '积分余额查询',
        status: 'success',
        query: balanceQuery,
        result: balanceResult.length,
        data: balanceResult,
        message: '积分余额查询正常'
      });
    } catch (error) {
      tests.push({
        name: '积分余额查询',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '积分余额查询失败'
      });
    }

    // 7. 检查系统设置（新用户积分相关）
    try {
      const settingsQuery = `
        SELECT
          setting_key,
          setting_value,
          setting_type,
          is_active
        FROM system_settings
        WHERE setting_key LIKE '%points%' OR setting_key LIKE '%user%'
        ORDER BY setting_key
      `;
      const settingsResult = await executeQuery(settingsQuery, []) as any[];

      tests.push({
        name: '系统设置查询',
        status: 'success',
        query: settingsQuery,
        result: settingsResult.length,
        data: settingsResult,
        message: '系统设置查询正常'
      });
    } catch (error) {
      tests.push({
        name: '系统设置查询',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '系统设置查询失败'
      });
    }

    // 8. 测试表结构信息
    try {
      const structureQuery = `
        DESCRIBE points_transactions
      `;
      const structureResult = await executeQuery(structureQuery, []) as any[];

      tests.push({
        name: '表结构查询',
        status: 'success',
        result: structureResult.length,
        data: structureResult,
        message: '表结构查询正常'
      });
    } catch (error) {
      tests.push({
        name: '表结构查询',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '表结构查询失败'
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        testResults: tests,
        summary: {
          totalTests: tests.length,
          successCount: tests.filter(t => t.status === 'success').length,
          errorCount: tests.filter(t => t.status === 'error').length
        }
      }
    });

  } catch (error) {
    console.error("SQL测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "SQL测试失败",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
