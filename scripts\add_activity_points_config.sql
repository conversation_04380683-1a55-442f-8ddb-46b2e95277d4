-- 添加活动积分配置系统设置
-- 支持不同活动类型的灵活有效期配置

INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
('activity_points_config', '{"invitation": {"validity_days": 15, "description": "邀请活动积分", "enabled": true}, "registration": {"validity_days": 30, "description": "注册奖励积分", "enabled": true}, "special_event": {"validity_days": 7, "description": "特殊活动积分", "enabled": false}, "daily_checkin": {"validity_days": 30, "description": "每日签到积分", "enabled": false}, "referral_bonus": {"validity_days": 60, "description": "推荐奖励积分", "enabled": false}}', 'json', '不同活动积分有效期配置', 'points', 1, NOW(), NOW()),
('show_invitation_banner', '1', 'boolean', '显示邀请活动横幅', 'ui', 1, NOW(), NOW()),
('invitation_banner_text', '🎉 邀请好友注册，每成功邀请1人获得积分奖励！', 'string', '邀请横幅显示文字', 'ui', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`setting_value` = VALUES(`setting_value`),
`description` = VALUES(`description`),
`updated_at` = NOW();

-- 验证配置是否添加成功
SELECT 
    setting_key,
    setting_value,
    setting_type,
    description,
    category,
    is_active
FROM system_settings 
WHERE setting_key IN ('activity_points_config', 'show_invitation_banner', 'invitation_banner_text')
ORDER BY setting_key;
