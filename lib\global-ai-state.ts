/**
 * 🚀 全局AI生成状态管理系统
 * 确保WelcomeLayout到EditorLayout的状态完全继承
 */

import { ChatMessage } from './project-manager';

export interface GlobalAIState {
  isGenerating: boolean;
  currentProjectId: string | null;
  currentPrompt: string | null;
  chatHistory: ChatMessage[];
  accumulatedContent: string;
  streamReader: ReadableStreamDefaultReader<Uint8Array> | null;
  abortController: AbortController | null;
}

class GlobalAIStateManager {
  private static instance: GlobalAIStateManager | null = null;
  private state: GlobalAIState = {
    isGenerating: false,
    currentProjectId: null,
    currentPrompt: null,
    chatHistory: [],
    accumulatedContent: '',
    streamReader: null,
    abortController: null
  };
  private listeners: Set<(state: GlobalAIState) => void> = new Set();

  static getInstance(): GlobalAIStateManager {
    if (!GlobalAIStateManager.instance) {
      GlobalAIStateManager.instance = new GlobalAIStateManager();
    }
    return GlobalAIStateManager.instance;
  }

  // 🎯 获取当前状态
  getState(): GlobalAIState {
    return { ...this.state };
  }

  // 🎯 开始AI生成
  startGeneration(projectId: string, prompt: string, chatHistory: ChatMessage[]) {
    console.log('🚀 GlobalAIState: 开始AI生成', { projectId, prompt: prompt.substring(0, 30) + '...' });
    
    this.state = {
      ...this.state,
      isGenerating: true,
      currentProjectId: projectId,
      currentPrompt: prompt,
      chatHistory: [...chatHistory],
      accumulatedContent: '',
      abortController: new AbortController()
    };
    
    this.notifyListeners();
  }

  // 🎯 更新生成内容
  updateContent(content: string) {
    if (!this.state.isGenerating) return;
    
    this.state = {
      ...this.state,
      accumulatedContent: content
    };
    
    this.notifyListeners();
  }

  // 🎯 完成生成
  completeGeneration(finalContent: string) {
    console.log('✅ GlobalAIState: AI生成完成开始处理', { 
      contentLength: finalContent.length,
      currentIsGenerating: this.state.isGenerating,
      currentChatHistoryLength: this.state.chatHistory.length,
      timestamp: new Date().toISOString()
    });
    
    // 🔧 关键修复：更新聊天历史中最后一条AI消息的isGenerating状态
    const updatedChatHistory = [...this.state.chatHistory];
    console.log('🔧 GlobalAIState: 检查聊天历史中的AI消息', {
      totalMessages: updatedChatHistory.length,
      messages: updatedChatHistory.map(msg => ({
        id: msg.id,
        type: msg.type,
        isGenerating: msg.isGenerating,
        hasContent: !!msg.content,
        hasHtmlContent: !!msg.htmlContent
      }))
    });
    
    const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];
    if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
      console.log('🎯 GlobalAIState: 找到正在生成的AI消息，开始更新', {
        messageId: lastMessage.id,
        beforeUpdate: {
          isGenerating: lastMessage.isGenerating,
          content: lastMessage.content,
          hasHtmlContent: !!lastMessage.htmlContent,
          contentLength: lastMessage.content?.length || 0
        }
      });
      
      // 🔧 关键修复：创建新的消息对象，确保React检测到变化
      const updatedMessage = {
        ...lastMessage,
        isGenerating: false,
        htmlContent: finalContent,
        content: '', // 清空content，因为完成后应该显示htmlContent
        timestamp: new Date() // 更新时间戳
      };
      
      // 替换最后一条消息
      updatedChatHistory[updatedChatHistory.length - 1] = updatedMessage;
      
      console.log('✅ GlobalAIState: AI消息状态已更新', { 
        messageId: updatedMessage.id,
        afterUpdate: {
          isGenerating: updatedMessage.isGenerating,
          content: updatedMessage.content,
          hasHtmlContent: !!updatedMessage.htmlContent,
          htmlContentLength: updatedMessage.htmlContent?.length || 0
        }
      });
    } else {
      console.log('⚠️ GlobalAIState: 未找到正在生成的AI消息', {
        lastMessageExists: !!lastMessage,
        lastMessageType: lastMessage?.type,
        lastMessageIsGenerating: lastMessage?.isGenerating,
        totalMessages: updatedChatHistory.length
      });
    }
    
    const prevState = { ...this.state };
    this.state = {
      ...this.state,
      isGenerating: false,
      chatHistory: updatedChatHistory,
      accumulatedContent: finalContent,
      streamReader: null,
      abortController: null
    };
    
    console.log('🔧 GlobalAIState: 状态更新完成', {
      stateChanges: {
        isGenerating: `${prevState.isGenerating} -> ${this.state.isGenerating}`,
        chatHistoryLength: `${prevState.chatHistory.length} -> ${this.state.chatHistory.length}`,
        contentLength: `${prevState.accumulatedContent.length} -> ${this.state.accumulatedContent.length}`
      },
      finalChatHistory: this.state.chatHistory.map(msg => ({
        id: msg.id,
        type: msg.type,
        isGenerating: msg.isGenerating,
        hasContent: !!msg.content,
        hasHtmlContent: !!msg.htmlContent
      }))
    });
    
    // 🔧 关键修复：立即通知所有监听器状态变化
    console.log('🎯 GlobalAIState: 开始通知监听器AI生成完成', {
      listenerCount: this.listeners.size,
      finalState: {
        isGenerating: this.state.isGenerating,
        chatHistoryLength: this.state.chatHistory.length,
        lastMessage: this.state.chatHistory.length > 0 ? {
          id: this.state.chatHistory[this.state.chatHistory.length - 1].id,
          type: this.state.chatHistory[this.state.chatHistory.length - 1].type,
          isGenerating: this.state.chatHistory[this.state.chatHistory.length - 1].isGenerating,
          hasHtmlContent: !!this.state.chatHistory[this.state.chatHistory.length - 1].htmlContent
        } : null
      }
    });
    
    // 🔧 关键修复：使用setTimeout确保状态更新在下一个事件循环中执行
    setTimeout(() => {
      this.notifyListeners();
      console.log('✅ GlobalAIState: 监听器通知完成');
      
      // 🎯 新增：发送自定义事件，确保所有组件都能接收到完成信号
      if (typeof window !== 'undefined') {
        const event = new CustomEvent('ai-generation-complete', {
          detail: {
            type: 'ai-generation-complete',
            finalContent,
            chatHistory: this.state.chatHistory,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(event);
        console.log('🎯 GlobalAIState: 已发送ai-generation-complete自定义事件');
      }
    }, 0);
  }

  // 🎯 停止生成
  stopGeneration() {
    console.log('🛑 GlobalAIState: 停止AI生成');
    
    if (this.state.abortController) {
      this.state.abortController.abort();
    }
    
    if (this.state.streamReader) {
      try {
        this.state.streamReader.releaseLock();
      } catch {
        console.log('🔧 StreamReader清理完成');
      }
    }
    
    // 🔧 关键修复：停止时也要更新聊天历史中最后一条AI消息的isGenerating状态
    const updatedChatHistory = [...this.state.chatHistory];
    const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];
    if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
      lastMessage.isGenerating = false;
      lastMessage.content = '生成已停止';
      console.log('🎯 GlobalAIState: 已更新AI消息生成状态为停止', { messageId: lastMessage.id });
    }
    
    this.state = {
      ...this.state,
      isGenerating: false,
      chatHistory: updatedChatHistory,
      streamReader: null,
      abortController: null
    };
    
    this.notifyListeners();
  }

  // 🎯 设置流读取器
  setStreamReader(reader: ReadableStreamDefaultReader<Uint8Array>) {
    this.state = {
      ...this.state,
      streamReader: reader
    };
  }

  // 🎯 更新聊天历史
  updateChatHistory(chatHistory: ChatMessage[]) {
    this.state = {
      ...this.state,
      chatHistory: [...chatHistory]
    };
    
    this.notifyListeners();
  }

  // 🎯 添加状态监听器
  addListener(listener: (state: GlobalAIState) => void) {
    this.listeners.add(listener);
    
    // 立即通知当前状态
    listener(this.getState());
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  // 🎯 通知所有监听器
  private notifyListeners() {
    const currentState = this.getState();
    this.listeners.forEach(listener => {
      try {
        listener(currentState);
      } catch (error) {
        console.error('❌ GlobalAIState: 监听器错误', error);
      }
    });
  }

  // 🎯 清理状态
  reset() {
    console.log('🧹 GlobalAIState: 重置状态');
    
    this.stopGeneration();
    
    this.state = {
      isGenerating: false,
      currentProjectId: null,
      currentPrompt: null,
      chatHistory: [],
      accumulatedContent: '',
      streamReader: null,
      abortController: null
    };
    
    this.notifyListeners();
  }
}

export const globalAIState = GlobalAIStateManager.getInstance(); 