"use client";

import { useState } from "react";
import { UndoRedoToolbar } from "../undo-redo-toolbar";
import { useUndoRedo } from "@/loomrunhooks/useUndoRedo";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

/**
 * 🎯 撤销/重做功能演示组件
 * 用于测试和展示撤销/重做系统的功能
 */
export function UndoRedoDemo() {
  const [content, setContent] = useState("初始内容");
  const [counter, setCounter] = useState(0);

  const {
    currentState,
    canUndo,
    canRedo,
    saveState,
    undo,
    redo,
    history,
    currentIndex,
    getHistoryStats
  } = useUndoRedo(content, {
    maxHistorySize: 20,
    debounceMs: 300,
    enableKeyboardShortcuts: true
  });

  const handleAddText = () => {
    const newContent = `${content} + 新文本${counter + 1}`;
    setContent(newContent);
    setCounter(counter + 1);
    saveState(newContent, `添加文本 ${counter + 1}`);
  };

  const handleClearText = () => {
    const newContent = "";
    setContent(newContent);
    saveState(newContent, "清空文本");
  };

  const handleUndo = () => {
    const state = undo();
    if (state) {
      setContent(state.html);
    }
  };

  const handleRedo = () => {
    const state = redo();
    if (state) {
      setContent(state.html);
    }
  };

  const handleJumpToState = (index: number) => {
    const state = history[index];
    if (state) {
      setContent(state.html);
    }
  };

  const stats = getHistoryStats();

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>撤销/重做功能演示</span>
            <UndoRedoToolbar
              canUndo={canUndo}
              canRedo={canRedo}
              onUndo={handleUndo}
              onRedo={handleRedo}
              history={history.map(state => ({
                action: state.action,
                timestamp: state.timestamp
              }))}
              currentIndex={currentIndex}
              onJumpToState={handleJumpToState}
            />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button onClick={handleAddText} variant="default">
              添加文本
            </Button>
            <Button onClick={handleClearText} variant="destructive">
              清空文本
            </Button>
          </div>

          {/* 内容显示 */}
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="text-sm font-medium mb-2">当前内容:</h3>
            <div className="text-sm text-muted-foreground">
              {content || "（空内容）"}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <div className="font-medium text-blue-700 dark:text-blue-300">
                历史状态
              </div>
              <div className="text-blue-600 dark:text-blue-400">
                {stats.totalStates}
              </div>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="font-medium text-green-700 dark:text-green-300">
                当前位置
              </div>
              <div className="text-green-600 dark:text-green-400">
                {stats.currentIndex + 1}
              </div>
            </div>
            <div className="p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
              <div className="font-medium text-purple-700 dark:text-purple-300">
                可撤销
              </div>
              <div className="text-purple-600 dark:text-purple-400">
                {stats.canUndo ? "是" : "否"}
              </div>
            </div>
            <div className="p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
              <div className="font-medium text-orange-700 dark:text-orange-300">
                可重做
              </div>
              <div className="text-orange-600 dark:text-orange-400">
                {stats.canRedo ? "是" : "否"}
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              使用说明:
            </h3>
            <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• 点击"添加文本"或"清空文本"来创建新的历史状态</li>
              <li>• 使用 Ctrl+Z (Windows) 或 Cmd+Z (Mac) 撤销</li>
              <li>• 使用 Ctrl+Y (Windows) 或 Cmd+Shift+Z (Mac) 重做</li>
              <li>• 点击历史图标查看完整历史记录</li>
              <li>• 点击任意历史状态可直接跳转</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
