/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { User } from "@/types";

/**
 * 🔐 用户认证状态管理 Hook
 * 
 * 这个 hook 负责管理 LoomRun 的用户认证状态：
 * - 用户登录状态检查
 * - 用户信息获取和缓存
 * - 登出功能
 * - 用户数据刷新
 * 
 * @returns 用户认证状态和操作方法
 */
export const useUser = () => {
  const client = useQueryClient();
  const router = useRouter();

  // 🔍 从API获取用户信息
  const { data: user, isLoading, refetch } = useQuery({
    queryKey: ["user.me"],
    queryFn: async (): Promise<User | null> => {
      try {
        const response = await fetch("/api/me", {
          credentials: "include", // 确保发送cookies
        });
        
        if (response.status === 401) {
          // 未认证
          return null;
        }
        
        if (!response.ok) {
          throw new Error("Failed to fetch user");
        }
        
        const data = await response.json();
        return data.user || null;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        return null;
      }
    },
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
  });

  // 🚪 登出功能
  const logout = async () => {
    try {
      // 调用登出API
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      });
      
      // 清除缓存的用户数据
      client.setQueryData(["user.me"], null);
      client.removeQueries({ queryKey: ["user.me"] });
      
      toast.success("登出成功");
      router.push("/");
      // 延迟一下再刷新，确保路由跳转完成
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error("登出失败:", error);
      toast.error("登出失败");
    }
  };

  // 🔄 刷新用户信息
  const refreshUser = async () => {
    console.log('🔄 Refreshing user data...');
    
    // 立即清除缓存并重新获取
    client.removeQueries({ queryKey: ["user.me"] });
    const result = await refetch();
    
    console.log('✅ User data refreshed:', result.data);
    return result;
  };

  // 📊 获取认证状态信息
  const getAuthStatus = () => {
    return {
      isAuthenticated: !!user,
      isLoading,
      userId: user?.id,
      userPhone: user?.phone,
      userName: user?.name || user?.nickname || user?.fullname
    };
  };

  return {
    // 🎯 核心状态
    user,
    loading: isLoading,
    
    // 🛠️ 操作方法
    logout,
    refreshUser,
    getAuthStatus,
    
    // 🔄 兼容性别名（保持向后兼容）
    openLoginWindow: () => {
      console.warn('openLoginWindow is deprecated, please implement login flow');
    }
  };
};
