#!/usr/bin/env node

/**
 * 清理过期的pending订单
 * 这个脚本会将所有过期的pending订单状态更新为'expired'
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function cleanupExpiredOrders() {
  let connection;
  
  try {
    // 创建数据库连接
    console.log('🔧 数据库配置:', {
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      database: process.env.DB_NAME,
      password: process.env.DB_PASSWORD ? '***' : 'undefined'
    });

    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'loomrun'
    });

    console.log('🔗 数据库连接成功');

    // 获取当前时间
    const now = new Date();
    console.log(`⏰ 当前时间: ${now.toLocaleString()}`);

    // 查询过期的会员订单
    console.log('\n📋 检查过期的会员订单...');
    const [expiredMembershipOrders] = await connection.execute(
      `SELECT id, order_no, order_expires_at, created_at 
       FROM membership_orders 
       WHERE status = 'pending' AND order_expires_at <= NOW()`
    );

    console.log(`发现 ${expiredMembershipOrders.length} 个过期的会员订单:`);
    expiredMembershipOrders.forEach(order => {
      console.log(`  - 订单 ${order.order_no}: 过期时间 ${new Date(order.order_expires_at).toLocaleString()}`);
    });

    // 查询过期的充值订单
    console.log('\n📋 检查过期的充值订单...');
    const [expiredRechargeOrders] = await connection.execute(
      `SELECT id, order_no, order_expires_at, created_at 
       FROM recharge_orders 
       WHERE status = 'pending' AND order_expires_at <= NOW()`
    );

    console.log(`发现 ${expiredRechargeOrders.length} 个过期的充值订单:`);
    expiredRechargeOrders.forEach(order => {
      console.log(`  - 订单 ${order.order_no}: 过期时间 ${new Date(order.order_expires_at).toLocaleString()}`);
    });

    // 更新过期的会员订单
    if (expiredMembershipOrders.length > 0) {
      console.log('\n🔄 更新过期的会员订单状态...');
      const [membershipResult] = await connection.execute(
        `UPDATE membership_orders 
         SET status = 'expired', updated_at = NOW() 
         WHERE status = 'pending' AND order_expires_at <= NOW()`
      );
      console.log(`✅ 已更新 ${membershipResult.affectedRows} 个会员订单状态为 'expired'`);
    }

    // 更新过期的充值订单
    if (expiredRechargeOrders.length > 0) {
      console.log('\n🔄 更新过期的充值订单状态...');
      const [rechargeResult] = await connection.execute(
        `UPDATE recharge_orders 
         SET status = 'expired', updated_at = NOW() 
         WHERE status = 'pending' AND order_expires_at <= NOW()`
      );
      console.log(`✅ 已更新 ${rechargeResult.affectedRows} 个充值订单状态为 'expired'`);
    }

    // 验证清理结果
    console.log('\n🔍 验证清理结果...');
    const [remainingPendingMembership] = await connection.execute(
      `SELECT id, order_no, order_expires_at, created_at FROM membership_orders WHERE status = 'pending'`
    );
    const [remainingPendingRecharge] = await connection.execute(
      `SELECT id, order_no, order_expires_at, created_at FROM recharge_orders WHERE status = 'pending'`
    );

    console.log(`剩余pending会员订单: ${remainingPendingMembership.length}`);
    remainingPendingMembership.forEach(order => {
      console.log(`  - 订单 ${order.order_no}: 过期时间 ${new Date(order.order_expires_at).toLocaleString()}`);
    });

    console.log(`剩余pending充值订单: ${remainingPendingRecharge.length}`);
    remainingPendingRecharge.forEach(order => {
      console.log(`  - 订单 ${order.order_no}: 过期时间 ${new Date(order.order_expires_at).toLocaleString()}`);
    });

    console.log('\n✅ 过期订单清理完成!');

  } catch (error) {
    console.error('❌ 清理过期订单失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行清理脚本
if (require.main === module) {
  cleanupExpiredOrders();
}

module.exports = { cleanupExpiredOrders };
