import { NextRequest, NextResponse } from "next/server";
import { validateInviteCode } from "@/lib/invitation-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { inviteCode } = body;

    if (!inviteCode) {
      return NextResponse.json({
        valid: false,
        message: '邀请码不能为空'
      }, { status: 400 });
    }

    const result = await validateInviteCode(inviteCode);
    
    return NextResponse.json({
      valid: result.valid,
      message: result.message,
      inviterUserId: result.inviterUserId
    });
  } catch (error) {
    console.error('验证邀请码失败:', error);
    return NextResponse.json({
      valid: false,
      message: '验证邀请码失败'
    }, { status: 500 });
  }
}
