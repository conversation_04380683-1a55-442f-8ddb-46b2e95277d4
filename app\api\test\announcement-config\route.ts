import { NextResponse } from "next/server";
import { executeQuery, initDatabase } from "@/lib/mysql";
import { getActivityValidityDays } from "@/lib/activity-points-config";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    console.log('🧪 开始测试公告配置读取...');

    // 1. 测试直接从数据库读取设置
    const settings = await executeQuery(
      `SELECT setting_key, setting_value, setting_type 
       FROM system_settings 
       WHERE setting_key IN (
         'show_invitation_banner', 
         'invitation_enabled', 
         'invitation_points_per_user', 
         'max_invitations_per_user',
         'activity_points_config'
       ) AND is_active = 1`,
      []
    ) as { setting_key: string; setting_value: string; setting_type: string }[];

    console.log('📋 数据库设置:', settings);

    // 2. 测试活动积分配置解析
    const activityConfigSetting = settings.find(s => s.setting_key === 'activity_points_config');
    let activityConfig = null;
    if (activityConfigSetting) {
      try {
        activityConfig = JSON.parse(activityConfigSetting.setting_value);
        console.log('🎯 活动积分配置:', activityConfig);
      } catch (error) {
        console.error('❌ 解析活动积分配置失败:', error);
      }
    }

    // 3. 测试工具函数
    const invitationValidityDays = await getActivityValidityDays('invitation');
    console.log('📅 邀请活动有效期天数:', invitationValidityDays);

    // 4. 模拟公告设置API的逻辑
    const config: Record<string, any> = {
      show_invitation_banner: true,
      invitation_enabled: false,
      invitation_points_per_user: 100,
      max_invitations_per_user: 10,
      invitation_validity_days: 15
    };

    let activityPointsConfig: any = null;

    settings.forEach(setting => {
      let value: any = setting.setting_value;
      
      switch (setting.setting_type) {
        case 'boolean':
          value = setting.setting_value === '1' || setting.setting_value === 'true';
          break;
        case 'number':
          value = parseInt(setting.setting_value) || 0;
          break;
        case 'json':
          try {
            value = JSON.parse(setting.setting_value);
          } catch {
            value = setting.setting_value;
          }
          break;
      }
      
      if (setting.setting_key === 'activity_points_config') {
        activityPointsConfig = value;
      } else {
        config[setting.setting_key] = value;
      }
    });

    // 从活动积分配置中提取邀请活动的有效期
    if (activityPointsConfig && activityPointsConfig.invitation) {
      config.invitation_validity_days = activityPointsConfig.invitation.validity_days || 15;
    }

    console.log('✅ 最终配置:', config);

    return NextResponse.json({
      success: true,
      message: "公告配置测试完成",
      data: {
        raw_settings: settings,
        activity_config: activityConfig,
        invitation_validity_days: invitationValidityDays,
        final_config: config
      }
    });

  } catch (error) {
    console.error("❌ 公告配置测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
