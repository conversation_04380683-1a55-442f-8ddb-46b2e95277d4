import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { getUserByToken } from "@/lib/auth-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ user: null, errCode: 401 }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ user: null, errCode: 401 }, { status: 401 });
    }

    // 转换用户数据格式以兼容现有前端代码
    const formattedUser = {
      id: user.id.toString(),
      name: user.nickname || `用户${user.id}`,
      fullname: user.nickname || `用户${user.id}`,
      nickname: user.nickname,
      avatarUrl: user.avatar_url,
      avatar_url: user.avatar_url, // 兼容两种字段名
      phone: user.phone,
      wechat_openid: user.wechat_openid,
      isPro: false, // 可以根据实际需求设置
      isLocalUse: false,
      // 积分信息
      points: user.points || 0,
      total_earned_points: user.total_earned_points || 0,
      total_spent_points: user.total_spent_points || 0
    };

    return NextResponse.json({ user: formattedUser, errCode: null }, { status: 200 });
  } catch (error) {
    console.error("获取用户信息API错误:", error);
    return NextResponse.json({ user: null, errCode: 500 }, { status: 500 });
  }
}
