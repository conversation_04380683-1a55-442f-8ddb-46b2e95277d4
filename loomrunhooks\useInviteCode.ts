"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export function useInviteCode() {
  const searchParams = useSearchParams();
  const [inviteCode, setInviteCode] = useState<string | null>(null);
  const [isValidated, setIsValidated] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string>('');

  useEffect(() => {
    const invite = searchParams.get('invite');
    console.log(`🔍 [useInviteCode] URL参数检查: invite="${invite}"`);
    console.log(`🔍 [useInviteCode] 完整URL:`, typeof window !== 'undefined' ? window.location.href : 'N/A');

    if (invite) {
      console.log(`✅ [useInviteCode] 设置邀请码: "${invite}"`);
      setInviteCode(invite);
      validateInviteCode(invite);
    } else {
      console.log(`❌ [useInviteCode] 没有找到邀请码参数`);
      setInviteCode(null);
    }
  }, [searchParams]);

  const validateInviteCode = async (code: string) => {
    try {
      const response = await fetch('/api/invitation/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ inviteCode: code }),
      });

      const data = await response.json();
      
      if (data.valid) {
        setIsValidated(true);
        setValidationMessage('邀请码有效，注册后将获得积分奖励！');
      } else {
        setIsValidated(false);
        setValidationMessage(data.message || '邀请码无效');
      }
    } catch (error) {
      setIsValidated(false);
      setValidationMessage('验证邀请码时出错');
    }
  };

  return {
    inviteCode,
    isValidated,
    validationMessage,
    hasInviteCode: !!inviteCode
  };
}
