import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';

export async function GET(request: NextRequest) {
  try {
    // 获取UI相关的系统设置
    const settings = await executeQuery(
      `SELECT setting_key, setting_value, setting_type 
       FROM system_settings 
       WHERE setting_key IN (
         'subscription_service_enabled',
         'recharge_service_enabled', 
         'show_subscription_button',
         'free_plan_enabled',
         'pro_plan_enabled',
         'max_plan_enabled'
       ) AND is_active = 1`,
      []
    ) as any[];

    // 转换为键值对格式
    const settingsMap: Record<string, any> = {};
    settings.forEach(setting => {
      const { setting_key, setting_value, setting_type } = setting;
      
      // 根据类型转换值
      switch (setting_type) {
        case 'boolean':
          settingsMap[setting_key] = setting_value === '1' || setting_value === 'true';
          break;
        case 'number':
          settingsMap[setting_key] = parseInt(setting_value, 10) || 0;
          break;
        case 'json':
          try {
            settingsMap[setting_key] = JSON.parse(setting_value);
          } catch {
            settingsMap[setting_key] = null;
          }
          break;
        default:
          settingsMap[setting_key] = setting_value;
      }
    });

    return NextResponse.json({
      success: true,
      data: settingsMap
    });

  } catch (error) {
    console.error('获取系统UI设置失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
