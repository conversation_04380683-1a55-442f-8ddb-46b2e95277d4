/**
 * 🖼️ LoomRun 截图工具库
 * 专业级截图功能，支持自动截图、手动裁剪和图片处理
 */

export interface ScreenshotConfig {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'png' | 'jpeg' | 'webp';
  devicePixelRatio?: number;
  delay?: number; // 延迟截图，等待内容加载
}

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenshotResult {
  dataUrl: string;
  blob: Blob;
  width: number;
  height: number;
  format: string;
  size: number; // 文件大小（字节）
}

/**
 * 🎯 从iframe或元素截图
 */
export class ScreenshotCapture {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;

  constructor() {
    // 只在客户端初始化
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      this.canvas = document.createElement('canvas');
      this.ctx = this.canvas.getContext('2d')!;
    }
  }

  private ensureInitialized(): boolean {
    if (!this.canvas || !this.ctx) {
      if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('ScreenshotCapture: 只能在客户端环境中使用');
        return false;
      }
      this.canvas = document.createElement('canvas');
      this.ctx = this.canvas.getContext('2d')!;
    }
    return true;
  }

  /**
   * 🚀 从iframe自动截图
   */
  async captureFromIframe(
    iframe: HTMLIFrameElement,
    config: ScreenshotConfig = {}
  ): Promise<ScreenshotResult> {
    if (!this.ensureInitialized()) {
      return { success: false, error: 'ScreenshotCapture未在客户端环境中初始化' };
    }
    const {
      width = 1200,
      height = 800,
      quality = 0.9,
      format = 'png',
      devicePixelRatio = window.devicePixelRatio || 1,
      delay = 1000
    } = config;

    // 等待iframe加载完成
    await this.waitForIframeLoad(iframe, delay);

    try {
      // 尝试从iframe内容截图
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error('无法访问iframe内容');
      }

      // 使用html2canvas库截图
      const html2canvas = await this.loadHtml2Canvas();
      
      // 如果没有指定高度，自动计算内容高度
      const actualHeight = height || Math.max(
        iframeDoc.body?.scrollHeight || 0,
        iframeDoc.body?.offsetHeight || 0,
        iframeDoc.documentElement?.scrollHeight || 0,
        iframeDoc.documentElement?.offsetHeight || 0,
        800 // 最小高度
      );
      
      const canvas = await html2canvas(iframeDoc.body, {
        width,
        height: actualHeight,
        scale: devicePixelRatio,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        removeContainer: true,
        scrollX: 0,
        scrollY: 0,
        windowWidth: width,
        windowHeight: actualHeight
      });

      return this.canvasToResult(canvas, format, quality);

    } catch (error) {
      console.warn('iframe截图失败，尝试备用方案:', error);
      
      // 备用方案：截图iframe元素本身
      return this.captureElement(iframe, config);
    }
  }

  /**
   * 🎯 从HTML内容生成截图
   */
  async captureFromHtml(
    htmlContent: string,
    config: ScreenshotConfig = {}
  ): Promise<ScreenshotResult> {
    const {
      width = 1200,
      height,
      quality = 0.9,
      format = 'png',
      devicePixelRatio = window.devicePixelRatio || 1,
      delay = 500
    } = config;

    // 创建临时iframe渲染HTML
    const iframe = document.createElement('iframe');
    iframe.style.width = `${width}px`;
    iframe.style.position = 'absolute';
    iframe.style.left = '-9999px';
    iframe.style.top = '-9999px';
    iframe.style.border = 'none';
    iframe.style.overflow = 'visible';
    
    // 如果没有指定高度，让iframe自适应内容高度
    if (height !== undefined) {
      iframe.style.height = `${height}px`;
    } else {
      iframe.style.height = 'auto';
      iframe.style.minHeight = '100px';
    }
    
    document.body.appendChild(iframe);

    try {
      // 写入HTML内容
      const doc = iframe.contentDocument!;
      doc.open();
      doc.write(htmlContent);
      doc.close();

      // 等待内容加载
      await new Promise(resolve => setTimeout(resolve, delay));

      // 如果是自适应高度，等待内容渲染后获取实际高度
      if (height === undefined) {
        await new Promise(resolve => setTimeout(resolve, 300)); // 额外等待渲染
        
        // 获取内容的实际高度
        const actualHeight = Math.max(
          doc.body?.scrollHeight || 0,
          doc.body?.offsetHeight || 0,
          doc.documentElement?.scrollHeight || 0,
          doc.documentElement?.offsetHeight || 0
        );
        
        if (actualHeight > 0) {
          iframe.style.height = `${actualHeight + 40}px`; // 添加一些padding
          await new Promise(resolve => setTimeout(resolve, 200)); // 等待高度调整
        }
      }

      // 截图
      const result = await this.captureFromIframe(iframe, { ...config, height: height });
      return result;

    } finally {
      // 清理临时iframe
      document.body.removeChild(iframe);
    }
  }

  /**
   * 🎨 从元素截图
   */
  async captureElement(
    element: HTMLElement,
    config: ScreenshotConfig = {}
  ): Promise<ScreenshotResult> {
    const {
      quality = 0.9,
      format = 'png',
      devicePixelRatio = window.devicePixelRatio || 1
    } = config;

    const html2canvas = await this.loadHtml2Canvas();
    
    const canvas = await html2canvas(element, {
      scale: devicePixelRatio,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false
    });

    return this.canvasToResult(canvas, format, quality);
  }

  /**
   * ✂️ 裁剪图片
   */
  async cropImage(
    sourceDataUrl: string,
    cropArea: CropArea,
    outputFormat: 'png' | 'jpeg' | 'webp' = 'png',
    quality: number = 0.9
  ): Promise<ScreenshotResult> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        // 设置canvas尺寸为裁剪区域大小
        this.canvas.width = cropArea.width;
        this.canvas.height = cropArea.height;

        // 清空canvas
        this.ctx.clearRect(0, 0, cropArea.width, cropArea.height);

        // 绘制裁剪后的图片
        this.ctx.drawImage(
          img,
          cropArea.x, cropArea.y, cropArea.width, cropArea.height, // 源图片区域
          0, 0, cropArea.width, cropArea.height // 目标区域
        );

        const result = this.canvasToResult(this.canvas, outputFormat, quality);
        resolve(result);
      };
      
      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = sourceDataUrl;
    });
  }

  /**
   * 🔄 调整图片尺寸
   */
  async resizeImage(
    sourceDataUrl: string,
    targetWidth: number,
    targetHeight: number,
    outputFormat: 'png' | 'jpeg' | 'webp' = 'png',
    quality: number = 0.9
  ): Promise<ScreenshotResult> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.canvas.width = targetWidth;
        this.canvas.height = targetHeight;

        // 清空canvas
        this.ctx.clearRect(0, 0, targetWidth, targetHeight);

        // 绘制缩放后的图片
        this.ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

        const result = this.canvasToResult(this.canvas, outputFormat, quality);
        resolve(result);
      };
      
      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = sourceDataUrl;
    });
  }

  /**
   * 🎨 添加水印
   */
  async addWatermark(
    sourceDataUrl: string,
    watermarkText: string = 'Made with LoomRun',
    options: {
      position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
      fontSize?: number;
      fontFamily?: string;
      color?: string;
      opacity?: number;
      padding?: number;
    } = {}
  ): Promise<ScreenshotResult> {
    const {
      position = 'bottom-right',
      fontSize = 14,
      fontFamily = 'Arial, sans-serif',
      color = '#ffffff',
      opacity = 0.8,
      padding = 10
    } = options;

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.canvas.width = img.width;
        this.canvas.height = img.height;

        // 绘制原图
        this.ctx.drawImage(img, 0, 0);

        // 设置水印样式
        this.ctx.font = `${fontSize}px ${fontFamily}`;
        this.ctx.fillStyle = color;
        this.ctx.globalAlpha = opacity;

        // 计算水印位置
        const textMetrics = this.ctx.measureText(watermarkText);
        const textWidth = textMetrics.width;
        const textHeight = fontSize;

        let x: number, y: number;

        switch (position) {
          case 'bottom-right':
            x = this.canvas.width - textWidth - padding;
            y = this.canvas.height - padding;
            break;
          case 'bottom-left':
            x = padding;
            y = this.canvas.height - padding;
            break;
          case 'top-right':
            x = this.canvas.width - textWidth - padding;
            y = textHeight + padding;
            break;
          case 'top-left':
            x = padding;
            y = textHeight + padding;
            break;
          case 'center':
            x = (this.canvas.width - textWidth) / 2;
            y = (this.canvas.height + textHeight) / 2;
            break;
          default:
            x = this.canvas.width - textWidth - padding;
            y = this.canvas.height - padding;
        }

        // 绘制水印背景
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(x - 5, y - textHeight - 2, textWidth + 10, textHeight + 8);

        // 绘制水印文字
        this.ctx.fillStyle = color;
        this.ctx.fillText(watermarkText, x, y);

        // 恢复透明度
        this.ctx.globalAlpha = 1;

        const result = this.canvasToResult(this.canvas, 'png', 0.9);
        resolve(result);
      };
      
      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = sourceDataUrl;
    });
  }

  /**
   * 🔧 私有方法：等待iframe加载
   */
  private async waitForIframeLoad(iframe: HTMLIFrameElement, delay: number): Promise<void> {
    return new Promise((resolve) => {
      if (iframe.contentDocument?.readyState === 'complete') {
        setTimeout(resolve, delay);
        return;
      }

      const handleLoad = () => {
        iframe.removeEventListener('load', handleLoad);
        setTimeout(resolve, delay);
      };

      iframe.addEventListener('load', handleLoad);
    });
  }

  /**
   * 🔧 私有方法：动态加载html2canvas
   */
  private async loadHtml2Canvas(): Promise<any> {
    if (typeof window !== 'undefined' && (window as any).html2canvas) {
      return (window as any).html2canvas;
    }

    // 动态加载html2canvas库
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js';
      script.onload = () => resolve((window as any).html2canvas);
      script.onerror = () => reject(new Error('html2canvas加载失败'));
      document.head.appendChild(script);
    });
  }

  /**
   * 🔧 私有方法：Canvas转换为结果对象
   */
  private canvasToResult(
    canvas: HTMLCanvasElement,
    format: 'png' | 'jpeg' | 'webp',
    quality: number
  ): ScreenshotResult {
    const mimeType = `image/${format}`;
    const dataUrl = canvas.toDataURL(mimeType, quality);
    
    // 转换为Blob
    const base64Data = dataUrl.split(',')[1];
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });

    return {
      dataUrl,
      blob,
      width: canvas.width,
      height: canvas.height,
      format,
      size: blob.size
    };
  }
}

/**
 * 🎯 截图工具单例
 */
// 🎯 单例实例 - 只在客户端创建
export const screenshotCapture = typeof window !== 'undefined' ? new ScreenshotCapture() : null;

/**
 * 🔧 工具函数：检查浏览器支持
 */
export function isScreenshotSupported(): boolean {
  return typeof window !== 'undefined' && 
         typeof document !== 'undefined' && 
         !!document.createElement('canvas').getContext;
}

/**
 * 🔧 工具函数：获取最佳截图配置
 */
export function getOptimalScreenshotConfig(purpose: 'preview' | 'thumbnail' | 'full'): ScreenshotConfig {
  switch (purpose) {
    case 'thumbnail':
      return {
        width: 400,
        height: 300,
        quality: 0.8,
        format: 'jpeg',
        devicePixelRatio: 1,
        delay: 500
      };
    case 'preview':
      return {
        width: 800,
        height: 600,
        quality: 0.9,
        format: 'png',
        devicePixelRatio: 1.5,
        delay: 1000
      };
    case 'full':
      return {
        width: 1200,
        height: 900,
        quality: 0.95,
        format: 'png',
        devicePixelRatio: 2,
        delay: 1500
      };
    default:
      return {
        width: 800,
        height: 600,
        quality: 0.9,
        format: 'png',
        devicePixelRatio: 1,
        delay: 1000
      };
  }
} 