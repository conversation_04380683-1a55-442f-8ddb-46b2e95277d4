/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { toast } from "sonner";
import { Save } from "lucide-react";
import { useParams } from "next/navigation";
import classNames from "classnames";

import Loading from "@/components/loading";
import { Button } from "@/components/ui/button";
import { useUser } from "@/loomrunhooks/useUser";

export function SaveButton({
  html,
  prompts,
}: {
  html: string;
  prompts: string[];
}) {
  // get params from URL
  const { id } = useParams<{
    id: string;
  }>();
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const updateProject = async () => {
    if (!user) {
      toast.error("请先登录");
      return;
    }

    if (!id) {
      toast.error("项目ID无效");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/me/projects/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          html,
          prompts,
        }),
      });

      const data = await response.json();

      if (data.ok) {
        toast.success("项目保存成功！ 🎉");
      } else {
        toast.error(data.error || "保存失败");
      }
    } catch (err: any) {
      console.error("保存项目失败:", err);
      toast.error("保存失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className={classNames(
          "max-lg:hidden relative overflow-hidden group",
          "bg-zinc-800/70 backdrop-blur-sm border-zinc-700/60",
          "hover:bg-zinc-700/70 hover:border-zinc-600/60",
          "text-zinc-300 hover:text-white",
          "shadow-md hover:shadow-lg transition-all duration-300",
          "transform hover:scale-105 hover:-translate-y-0.5",
          "disabled:opacity-50 disabled:scale-100 disabled:translate-y-0",
          "px-6 py-2.5"
        )}
        onClick={updateProject}
        disabled={loading}
      >
        {/* 发光边框效果 */}
        <div className="absolute inset-0 rounded-md bg-gradient-to-r from-zinc-500/25 to-zinc-400/25 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
        
        <div className="relative z-10 flex items-center gap-2">
          <Save className="size-4 transition-transform duration-200 group-hover:scale-110" />
          <span className="font-medium text-xs md:text-sm">保存项目</span>
          {loading && <Loading className="ml-2 size-4 animate-spin" />}
        </div>
      </Button>
      <Button
        variant="outline"
        size="sm"
        className={classNames(
          "lg:hidden relative overflow-hidden group",
          "bg-zinc-800/70 backdrop-blur-sm border-zinc-700/60",
          "hover:bg-zinc-700/70 hover:border-zinc-600/60",
          "text-zinc-300 hover:text-white",
          "shadow-md hover:shadow-lg transition-all duration-300",
          "transform hover:scale-105 hover:-translate-y-0.5",
          "disabled:opacity-50 disabled:scale-100 disabled:translate-y-0",
          "w-7 h-7 md:w-9 md:h-9 p-0" // 移动端与刷新按钮尺寸一致
        )}
        onClick={updateProject}
        disabled={loading}
      >
        {/* 发光边框效果 */}
        <div className="absolute inset-0 rounded-md bg-gradient-to-r from-zinc-500/25 to-zinc-400/25 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
        
        <div className="relative z-10 flex items-center justify-center">
          {loading ? (
            <Loading className="size-4 animate-spin" />
          ) : (
            <Save className="size-4 transition-transform duration-200 group-hover:scale-110" />
          )}
        </div>
      </Button>
    </>
  );
}
