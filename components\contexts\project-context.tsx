"use client";
import { createContext, useContext, useState, ReactNode } from "react";

interface ProjectContextType {
  shouldRefreshProjects: boolean;
  triggerProjectRefresh: () => void;
  resetRefreshFlag: () => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export function ProjectProvider({ children }: { children: ReactNode }) {
  const [shouldRefreshProjects, setShouldRefreshProjects] = useState(false);

  const triggerProjectRefresh = () => {
    console.log('🚀 Triggering project refresh...');
    setShouldRefreshProjects(true);
  };

  const resetRefreshFlag = () => {
    console.log('🔄 Resetting project refresh flag...');
    setShouldRefreshProjects(false);
  };

  return (
    <ProjectContext.Provider value={{
      shouldRefreshProjects,
      triggerProjectRefresh,
      resetRefreshFlag,
    }}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
} 