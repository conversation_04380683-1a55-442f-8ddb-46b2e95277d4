"use client";
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { AppEditor } from "@/components/editor";
import { useUser } from "@/loomrunhooks/useUser";

interface ProjectData {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  user_id: number;
}

export default function ProjectPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProject = async () => {
      try {
        const projectId = params.id as string;

        if (!projectId || projectId === 'undefined' || isNaN(parseInt(projectId))) {
          console.log('🔍 项目ID无效或为undefined，重定向到新建项目页面');
          router.push("/projects/new");
          return;
        }

        const response = await fetch(`/api/me/projects/${projectId}`);
        const data = await response.json();

        if (data.ok && data.project) {
          setProject(data.project);
        } else {
          if (response.status === 401) {
            toast.error("请先登录");
            router.push("/projects/new");
          } else if (response.status === 404) {
            toast.error("项目不存在");
            router.push("/projects/new");
          } else {
            toast.error("加载项目失败");
            router.push("/projects/new");
          }
        }
      } catch (error) {
        console.error("加载项目失败:", error);
        toast.error("加载项目失败");
        router.push("/projects/new");
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadProject();
    } else {
      // 用户未登录，重定向到新建项目页面
      router.push("/projects/new");
    }
  }, [params, user, router]);

  if (loading) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center gap-6 project-loading-container">
          {/* 加载动画容器 */}
          <div className="relative loading-spinner-glow">
            <div className="w-16 h-16 border-4 border-muted border-t-blue-500 rounded-full animate-spin"></div>
            {/* 内圈装饰 */}
            <div className="absolute inset-2 w-8 h-8 border-2 border-blue-500/30 border-b-transparent rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '0.8s' }}></div>
          </div>
          
          {/* 加载文字 */}
          <div className="text-center">
            <p className="text-foreground text-xl font-medium mb-2">加载项目中</p>
            <p className="text-muted-foreground text-sm">请稍候，正在为您准备项目...</p>
          </div>
          
          {/* 装饰性进度点 */}
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-blue-500/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-blue-500/30 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground text-lg mb-4">项目未找到</p>
          <button 
            onClick={() => router.push("/projects/new")}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors"
          >
            创建新项目
          </button>
        </div>
      </div>
    );
  }

  return <AppEditor project={project} />;
} 