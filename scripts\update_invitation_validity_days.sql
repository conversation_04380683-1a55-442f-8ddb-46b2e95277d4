-- 更新邀请活动积分有效期的便捷脚本
-- 可以直接修改下面的数值来测试不同的有效期设置

-- 方法1: 如果已有activity_points_config配置，更新邀请活动的有效期
UPDATE system_settings 
SET setting_value = JSON_SET(
    setting_value, 
    '$.invitation.validity_days', 
    20  -- 修改这个数值来设置邀请活动积分有效期（天数）
)
WHERE setting_key = 'activity_points_config' AND is_active = 1;

-- 方法2: 如果没有activity_points_config配置，创建完整配置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`, `is_active`) VALUES
('activity_points_config', 
 '{"invitation": {"validity_days": 25, "description": "邀请活动积分", "enabled": true}, "registration": {"validity_days": 30, "description": "注册奖励积分", "enabled": true}}', 
 'json', 
 '不同活动积分有效期配置', 
 'points', 
 1)
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = NOW();

-- 验证配置是否更新成功
SELECT 
    setting_key,
    JSON_EXTRACT(setting_value, '$.invitation.validity_days') as invitation_validity_days,
    JSON_EXTRACT(setting_value, '$.invitation.description') as invitation_description,
    JSON_EXTRACT(setting_value, '$.invitation.enabled') as invitation_enabled,
    setting_value as full_config
FROM system_settings 
WHERE setting_key = 'activity_points_config';

-- 查看所有邀请相关设置
SELECT 
    setting_key,
    setting_value,
    setting_type,
    description
FROM system_settings 
WHERE setting_key IN (
    'invitation_enabled',
    'invitation_points_per_user', 
    'max_invitations_per_user',
    'activity_points_config',
    'show_invitation_banner'
) 
ORDER BY setting_key;
