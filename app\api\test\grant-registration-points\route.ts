import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { grantNewUserPoints } from "@/lib/points-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    console.log(`🎯 开始为用户 ${user.id} (${user.nickname}) 手动发放注册积分...`);

    // 先检查用户是否已有积分记录
    const existingTransactions = await executeQuery(
      'SELECT COUNT(*) as count FROM points_transactions WHERE user_id = ?',
      [user.id]
    ) as { count: number }[];

    if (existingTransactions[0]?.count > 0) {
      console.log(`⚠️ 用户 ${user.id} 已有 ${existingTransactions[0].count} 条积分记录`);
      return NextResponse.json({
        success: false,
        message: `用户已有 ${existingTransactions[0].count} 条积分记录，无需重复发放`,
        data: {
          userId: user.id,
          nickname: user.nickname,
          existingRecords: existingTransactions[0].count
        }
      });
    }

    // 手动发放新用户积分
    const success = await grantNewUserPoints(
      user.id,
      'phone',
      'manual_grant',
      undefined, // inviteCode
      undefined, // inviterUserId
      '127.0.0.1', // ipAddress
      'Manual Grant Test' // userAgent
    );

    if (success) {
      console.log(`✅ 用户 ${user.id} 注册积分发放成功`);
      return NextResponse.json({
        success: true,
        message: "注册积分发放成功",
        data: {
          userId: user.id,
          nickname: user.nickname
        }
      });
    } else {
      console.log(`❌ 用户 ${user.id} 注册积分发放失败`);
      return NextResponse.json({
        success: false,
        message: "积分发放失败，可能用户已获得过注册积分或系统设置问题"
      });
    }

  } catch (error) {
    console.error("手动发放积分失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "服务器错误",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
