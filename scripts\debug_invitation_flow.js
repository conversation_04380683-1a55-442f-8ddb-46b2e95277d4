const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'loomrun',
  charset: 'utf8mb4'
};

async function debugInvitationFlow() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('🔗 数据库连接成功');

    // 1. 检查邀请码 WMQESB 对应的邀请者
    console.log('\n=== 1. 检查邀请码 WMQESB ===');
    const [inviterRows] = await connection.execute(
      'SELECT id, nickname, invite_code, invitation_count FROM users WHERE invite_code = ?',
      ['WMQESB']
    );
    
    if (inviterRows.length === 0) {
      console.log('❌ 邀请码 WMQESB 不存在！');
      return;
    }
    
    const inviter = inviterRows[0];
    console.log('✅ 邀请者信息:', {
      id: inviter.id,
      nickname: inviter.nickname,
      invite_code: inviter.invite_code,
      invitation_count: inviter.invitation_count
    });

    // 2. 检查邀请设置
    console.log('\n=== 2. 检查邀请系统设置 ===');
    const [settingsRows] = await connection.execute(
      `SELECT setting_key, setting_value FROM system_settings 
       WHERE setting_key IN ('invitation_enabled', 'invitation_points_per_user', 'max_invitations_per_user') 
       AND is_active = 1`
    );
    
    const settings = {};
    settingsRows.forEach(row => {
      settings[row.setting_key] = row.setting_value;
    });
    
    console.log('📋 邀请设置:', settings);
    
    if (settings.invitation_enabled !== '1') {
      console.log('❌ 邀请功能已关闭！');
      return;
    }

    // 3. 检查邀请者的邀请统计
    console.log('\n=== 3. 检查邀请者邀请统计 ===');
    const [invitationStats] = await connection.execute(
      `SELECT 
         COUNT(*) as total_invitations,
         COUNT(CASE WHEN invitation_status = 'registered' THEN 1 END) as successful_invitations,
         COALESCE(SUM(CASE WHEN invitation_status = 'registered' THEN points_awarded ELSE 0 END), 0) as total_points_earned
       FROM user_invitations 
       WHERE inviter_user_id = ?`,
      [inviter.id]
    );
    
    const stats = invitationStats[0];
    const maxInvitations = parseInt(settings.max_invitations_per_user || '10');
    const remainingInvitations = maxInvitations - stats.successful_invitations;
    
    console.log('📊 邀请统计:', {
      total_invitations: stats.total_invitations,
      successful_invitations: stats.successful_invitations,
      total_points_earned: stats.total_points_earned,
      max_invitations: maxInvitations,
      remaining_invitations: remainingInvitations
    });
    
    if (remainingInvitations <= 0) {
      console.log('❌ 邀请者已达到邀请上限！');
      return;
    }

    // 4. 检查最新注册的用户（用户ID 34）
    console.log('\n=== 4. 检查最新注册用户 ===');
    const [newUserRows] = await connection.execute(
      'SELECT id, phone, nickname, invited_by_user_id, created_at FROM users WHERE id = 34'
    );
    
    if (newUserRows.length === 0) {
      console.log('❌ 用户ID 34 不存在！');
      return;
    }
    
    const newUser = newUserRows[0];
    console.log('👤 新用户信息:', {
      id: newUser.id,
      phone: newUser.phone,
      nickname: newUser.nickname,
      invited_by_user_id: newUser.invited_by_user_id,
      created_at: newUser.created_at
    });

    // 5. 检查是否有邀请记录
    console.log('\n=== 5. 检查邀请记录 ===');
    const [invitationRecords] = await connection.execute(
      'SELECT * FROM user_invitations WHERE invited_user_id = ? OR inviter_user_id = ?',
      [newUser.id, inviter.id]
    );
    
    console.log('📝 邀请记录数量:', invitationRecords.length);
    if (invitationRecords.length > 0) {
      console.log('📝 邀请记录详情:', invitationRecords);
    } else {
      console.log('❌ 没有找到相关的邀请记录！');
    }

    // 6. 检查积分交易记录
    console.log('\n=== 6. 检查积分交易记录 ===');
    const [transactionRows] = await connection.execute(
      `SELECT * FROM points_transactions 
       WHERE user_id = ? AND source_type = 'invitation' 
       ORDER BY created_at DESC LIMIT 5`,
      [inviter.id]
    );
    
    console.log('💰 邀请积分交易记录数量:', transactionRows.length);
    if (transactionRows.length > 0) {
      console.log('💰 最近的邀请积分记录:', transactionRows);
    } else {
      console.log('❌ 邀请者没有邀请积分交易记录！');
    }

    // 7. 检查新用户的注册积分记录
    console.log('\n=== 7. 检查新用户注册积分记录 ===');
    const [newUserTransactions] = await connection.execute(
      `SELECT * FROM points_transactions 
       WHERE user_id = ? AND source_type = 'registration' 
       ORDER BY created_at DESC LIMIT 3`,
      [newUser.id]
    );
    
    console.log('🎁 新用户注册积分记录数量:', newUserTransactions.length);
    if (newUserTransactions.length > 0) {
      console.log('🎁 新用户注册积分记录:', newUserTransactions);
    }

    // 8. 模拟邀请码验证
    console.log('\n=== 8. 模拟邀请码验证 ===');
    console.log('🔍 验证邀请码 WMQESB...');
    
    // 检查邀请者是否存在
    if (inviterRows.length > 0) {
      console.log('✅ 邀请码有效，邀请者ID:', inviter.id);
      
      // 检查邀请次数限制
      if (remainingInvitations > 0) {
        console.log('✅ 邀请者还可以邀请', remainingInvitations, '人');
        console.log('🎯 邀请码验证应该成功！');
      } else {
        console.log('❌ 邀请者已达到邀请上限');
      }
    }

    // 9. 总结问题
    console.log('\n=== 9. 问题诊断总结 ===');
    
    const issues = [];
    
    if (settings.invitation_enabled !== '1') {
      issues.push('邀请功能已关闭');
    }
    
    if (remainingInvitations <= 0) {
      issues.push('邀请者已达到邀请上限');
    }
    
    if (!newUser.invited_by_user_id) {
      issues.push('新用户的 invited_by_user_id 字段为空，说明邀请关系没有建立');
    }
    
    if (invitationRecords.length === 0) {
      issues.push('没有创建邀请记录，说明邀请处理逻辑没有执行');
    }
    
    if (transactionRows.length === 0) {
      issues.push('没有邀请积分交易记录，说明积分奖励没有发放');
    }
    
    if (issues.length === 0) {
      console.log('✅ 没有发现明显问题，可能是前端邀请码传递问题');
    } else {
      console.log('❌ 发现以下问题:');
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行调试
debugInvitationFlow().catch(console.error);
