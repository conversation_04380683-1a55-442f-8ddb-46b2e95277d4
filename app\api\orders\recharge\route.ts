import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';
import { getNumberSetting, getStringSetting, getBooleanSetting } from '@/lib/points-service';

// 生成订单编号
const generateOrderNo = async (prefix: string): Promise<string> => {
  const date = new Date();
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0');
  const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${prefix}${dateStr}${randomNum}`;
};

// 检查用户是否有pending订单
const checkPendingOrder = async (userId: number, orderType: 'subscription' | 'recharge') => {
  let query: string;
  if (orderType === 'subscription') {
    query = `SELECT id, order_no, discount_price as amount, plan_key, created_at, order_expires_at
             FROM membership_orders
             WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
             ORDER BY created_at DESC LIMIT 1`;
  } else {
    query = `SELECT id, order_no, discount_price as amount, package_key, created_at, order_expires_at
             FROM recharge_orders
             WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
             ORDER BY created_at DESC LIMIT 1`;
  }

  const results = await executeQuery(query, [userId]) as any[];
  return results.length > 0 ? results[0] : null;
};

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, message: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, message: "用户不存在" }, { status: 401 });
    }

    // 检查充值服务是否启用
    const rechargeEnabled = await getBooleanSetting('recharge_service_enabled', false);
    if (!rechargeEnabled) {
      return NextResponse.json({ success: false, message: "充值服务暂未开放" }, { status: 403 });
    }

    const body = await request.json();
    const { packageKey, quantity = 1 } = body;

    if (!packageKey) {
      return NextResponse.json({ success: false, message: "缺少套餐参数" }, { status: 400 });
    }

    // 检查重复下单
    const duplicateCheckEnabled = await getBooleanSetting('duplicate_order_check_enabled', true);
    if (duplicateCheckEnabled) {
      const pendingOrder = await checkPendingOrder(user.id, 'recharge');
      if (pendingOrder) {
        return NextResponse.json({
          success: false,
          message: "您有未完成的订单",
          pendingOrder: {
            orderId: pendingOrder.id.toString(),
            orderNo: pendingOrder.order_no,
            orderType: 'recharge',
            amount: pendingOrder.amount,
            createdAt: pendingOrder.created_at
          }
        });
      }
    }

    // 获取积分套餐信息
    const packageResults = await executeQuery(
      `SELECT id, package_key, package_name, points_amount, original_price, discount_price, 
              bonus_points, description
       FROM points_packages 
       WHERE package_key = ? AND is_active = 1`,
      [packageKey]
    ) as any[];

    if (packageResults.length === 0) {
      return NextResponse.json({ success: false, message: "积分套餐不存在" }, { status: 404 });
    }

    const pkg = packageResults[0];
    const totalAmount = pkg.discount_price * quantity;
    const totalPoints = pkg.points_amount * quantity;
    const totalBonusPoints = pkg.bonus_points * quantity;

    // 生成订单编号
    const orderPrefix = await getStringSetting('recharge_order_prefix', 'RCH');
    const orderNo = await generateOrderNo(orderPrefix);

    // 计算订单过期时间 - 彻底修复时区问题
    const expireMinutes = await getNumberSetting('order_expire_minutes', 30);

    // 使用数据库当前时间作为基准，避免服务器时区问题
    const dbTimeResult = await executeQuery('SELECT NOW() as `current_time`') as any[];
    const dbCurrentTime = new Date(dbTimeResult[0].current_time);

    // 基于数据库时间计算过期时间
    const orderExpiresAt = new Date(dbCurrentTime.getTime() + expireMinutes * 60 * 1000);

    // 格式化为MySQL datetime格式
    const formatDateTime = (date: Date) => {
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0') + ':' +
        String(date.getSeconds()).padStart(2, '0');
    };

    console.log(`🕐 充值订单过期时间设置:`);
    console.log(`   数据库当前时间: ${formatDateTime(dbCurrentTime)}`);
    console.log(`   订单过期时间: ${formatDateTime(orderExpiresAt)}`);
    console.log(`   有效期: ${expireMinutes}分钟`);
    console.log(`   时间差验证: ${(orderExpiresAt.getTime() - dbCurrentTime.getTime()) / 1000 / 60}分钟`);

    // 获取充值积分默认有效期
    const defaultValidityDays = await getNumberSetting('recharge_points_default_validity_days', 0);

    // 创建订单
    const orderResult = await executeQuery(
      `INSERT INTO recharge_orders
       (user_id, order_no, points_amount, points_validity_days, package_key, bonus_points,
        original_price, discount_price, order_expires_at, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')`,
      [
        user.id,
        orderNo,
        totalPoints,
        defaultValidityDays,
        packageKey,
        totalBonusPoints,
        pkg.original_price * quantity,
        totalAmount,
        formatDateTime(orderExpiresAt)
      ]
    ) as any;

    return NextResponse.json({
      success: true,
      data: {
        orderId: orderResult.insertId.toString(),
        orderNo,
        amount: totalAmount,
        packageName: pkg.package_name,
        pointsAmount: totalPoints,
        bonusPoints: totalBonusPoints,
        expiresAt: orderExpiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('创建充值订单失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
