import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';
import { getNumberSetting, getStringSetting, getBooleanSetting } from '@/lib/points-service';

// 生成订单编号
const generateOrderNo = async (prefix: string): Promise<string> => {
  const date = new Date();
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0');
  const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${prefix}${dateStr}${randomNum}`;
};

// 检查用户是否有pending订单
const checkPendingOrder = async (userId: number, orderType: 'subscription' | 'recharge') => {
  let query: string;
  if (orderType === 'subscription') {
    query = `SELECT id, order_no, discount_price as amount, plan_key, created_at, order_expires_at
             FROM membership_orders
             WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
             ORDER BY created_at DESC LIMIT 1`;
  } else {
    query = `SELECT id, order_no, discount_price as amount, package_key, created_at, order_expires_at
             FROM recharge_orders
             WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
             ORDER BY created_at DESC LIMIT 1`;
  }

  const results = await executeQuery(query, [userId]) as any[];
  return results.length > 0 ? results[0] : null;
};

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, message: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, message: "用户不存在" }, { status: 401 });
    }

    // 检查订阅服务是否启用
    const subscriptionEnabled = await getBooleanSetting('subscription_service_enabled', false);
    if (!subscriptionEnabled) {
      return NextResponse.json({ success: false, message: "订阅服务暂未开放" }, { status: 403 });
    }

    const body = await request.json();
    const { planKey, quantity = 1 } = body;

    if (!planKey) {
      return NextResponse.json({ success: false, message: "缺少计划参数" }, { status: 400 });
    }

    // 检查重复下单
    const duplicateCheckEnabled = await getBooleanSetting('duplicate_order_check_enabled', true);
    if (duplicateCheckEnabled) {
      const pendingOrder = await checkPendingOrder(user.id, 'subscription');
      if (pendingOrder) {
        return NextResponse.json({
          success: false,
          message: "您有未完成的订单",
          pendingOrder: {
            orderId: pendingOrder.id.toString(),
            orderNo: pendingOrder.order_no,
            orderType: 'subscription',
            amount: pendingOrder.amount,
            createdAt: pendingOrder.created_at
          }
        });
      }
    }

    // 获取订阅计划信息
    const planResults = await executeQuery(
      `SELECT id, plan_key, plan_type, plan_name, duration_months, original_price, discount_price, 
              points_included, points_validity_days
       FROM subscription_plans 
       WHERE plan_key = ? AND is_active = 1`,
      [planKey]
    ) as any[];

    if (planResults.length === 0) {
      return NextResponse.json({ success: false, message: "订阅计划不存在" }, { status: 404 });
    }

    const plan = planResults[0];
    const totalAmount = plan.discount_price * quantity;

    // 生成订单编号
    const orderPrefix = await getStringSetting('subscription_order_prefix', 'SUB');
    const orderNo = await generateOrderNo(orderPrefix);

    // 计算订单过期时间 - 彻底修复时区问题
    const expireMinutes = await getNumberSetting('order_expire_minutes', 30);

    // 使用数据库当前时间作为基准，避免服务器时区问题
    const dbTimeResult = await executeQuery('SELECT NOW() as `current_time`') as any[];
    const dbCurrentTime = new Date(dbTimeResult[0].current_time);

    // 基于数据库时间计算过期时间
    const orderExpiresAt = new Date(dbCurrentTime.getTime() + expireMinutes * 60 * 1000);

    // 格式化为MySQL datetime格式
    const formatDateTime = (date: Date) => {
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0') + ':' +
        String(date.getSeconds()).padStart(2, '0');
    };

    console.log(`🕐 订阅订单过期时间设置:`);
    console.log(`   数据库当前时间: ${formatDateTime(dbCurrentTime)}`);
    console.log(`   订单过期时间: ${formatDateTime(orderExpiresAt)}`);
    console.log(`   有效期: ${expireMinutes}分钟`);
    console.log(`   时间差验证: ${(orderExpiresAt.getTime() - dbCurrentTime.getTime()) / 1000 / 60}分钟`);

    // 创建订单
    const orderResult = await executeQuery(
      `INSERT INTO membership_orders
       (user_id, order_no, membership_type, plan_key, duration_months, original_price, discount_price,
        order_expires_at, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')`,
      [
        user.id,
        orderNo,
        plan.plan_type,
        planKey,
        plan.duration_months,
        plan.original_price * quantity,
        totalAmount,
        formatDateTime(orderExpiresAt)
      ]
    ) as any;

    return NextResponse.json({
      success: true,
      data: {
        orderId: orderResult.insertId.toString(),
        orderNo,
        amount: totalAmount,
        planName: plan.plan_name,
        pointsIncluded: plan.points_included * quantity,
        expiresAt: orderExpiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('创建订阅订单失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
