export interface User {
  fullname: string;
  avatarUrl: string;
  name: string;
  isLocalUse?: boolean;
  isPro: boolean;
  id: string;
  token?: string;
  // 新增字段支持新的认证系统
  phone?: string;
  wechat_openid?: string;
  nickname?: string;
  avatar_url?: string;
  // 积分系统字段
  points?: number;
  total_earned_points?: number;
  total_spent_points?: number;
}

export interface HtmlHistory {
  html: string;
  createdAt: Date;
  prompt: string;
}

export interface Project {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  user_id: number;
  created_at: string;
  updated_at: string;
  
  // 🔥 新增：统计信息字段 - 性能优化
  version_count?: number;
  message_count?: number;
  latest_version?: number;
  
  // 向后兼容旧字段
  html?: string;
  space_id?: string;
  _id?: string;
  _updatedAt?: Date;
  _createdAt?: Date;
}
