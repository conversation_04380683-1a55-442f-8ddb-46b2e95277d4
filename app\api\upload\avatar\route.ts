import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { writeFile, mkdir } from "fs/promises";
import path from "path";
import { getUserByToken } from "@/lib/auth-service";

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const { image } = await request.json();
    
    if (!image || !image.startsWith('data:image/')) {
      return NextResponse.json({ error: "无效的图片数据" }, { status: 400 });
    }

    // 解析base64图片数据
    const matches = image.match(/^data:image\/([a-zA-Z]*);base64,(.+)$/);
    if (!matches) {
      return NextResponse.json({ error: "无效的图片格式" }, { status: 400 });
    }

    const imageType = matches[1];
    const imageData = matches[2];
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const filename = `avatar_${user.id}_${timestamp}.${imageType}`;
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'avatars');
    const filePath = path.join(uploadsDir, filename);

    // 确保目录存在
    await mkdir(uploadsDir, { recursive: true });

    // 保存文件
    const buffer = Buffer.from(imageData, 'base64');
    await writeFile(filePath, buffer);

    // 返回文件URL
    const fileUrl = `/uploads/avatars/${filename}`;
    
    return NextResponse.json({ 
      success: true, 
      url: fileUrl 
    });

  } catch (error) {
    console.error("头像上传失败:", error);
    return NextResponse.json({ error: "上传失败" }, { status: 500 });
  }
} 