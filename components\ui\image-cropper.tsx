"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { RotateCcw, Download, Crop, ZoomIn, ZoomOut } from 'lucide-react';

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ImageCropperProps {
  imageUrl: string;
  onCropComplete: (croppedImageUrl: string, cropArea: CropArea) => void;
  onCancel: () => void;
  aspectRatio?: number; // 宽高比，例如 16/9, 4/3, 1 等
  minCropSize?: number; // 最小裁剪尺寸
  maxCropSize?: number; // 最大裁剪尺寸
  initialCrop?: Partial<CropArea>;
}

export function ImageCropper({
  imageUrl,
  onCropComplete,
  onCancel,
  aspectRatio,
  minCropSize = 50,
  maxCropSize = 800,
  initialCrop
}: ImageCropperProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string>('');
  const [scale, setScale] = useState(1);
  const [imageLoaded, setImageLoaded] = useState(false);
  
  // 裁剪区域状态
  const [cropArea, setCropArea] = useState<CropArea>({
    x: 50,
    y: 50,
    width: 200,
    height: 150,
    ...initialCrop
  });

  // 图片和容器尺寸
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });

  // 初始化图片
  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      imageRef.current = img;
      setImageDimensions({ width: img.width, height: img.height });
      setImageLoaded(true);
      
      // 设置初始裁剪区域
      const containerWidth = containerRef.current?.clientWidth || 600;
      const containerHeight = containerRef.current?.clientHeight || 400;
      setContainerDimensions({ width: containerWidth, height: containerHeight });
      
      // 计算缩放比例以适应容器
      const scaleX = containerWidth / img.width;
      const scaleY = containerHeight / img.height;
      const initialScale = Math.min(scaleX, scaleY, 1);
      setScale(initialScale);
      
      // 设置默认裁剪区域（居中）
      if (!initialCrop) {
        const defaultSize = Math.min(200, containerWidth * 0.5, containerHeight * 0.5);
        setCropArea({
          x: (containerWidth - defaultSize) / 2,
          y: (containerHeight - defaultSize) / 2,
          width: defaultSize,
          height: aspectRatio ? defaultSize / aspectRatio : defaultSize
        });
      }
    };
    img.onerror = () => {
      console.error('图片加载失败:', imageUrl);
    };
    img.src = imageUrl;
  }, [imageUrl, aspectRatio, initialCrop]);

  // 重绘canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    if (!canvas || !img || !imageLoaded) return;

    const ctx = canvas.getContext('2d')!;
    canvas.width = containerDimensions.width;
    canvas.height = containerDimensions.height;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 计算图片显示位置和尺寸
    const displayWidth = img.width * scale;
    const displayHeight = img.height * scale;
    const offsetX = (canvas.width - displayWidth) / 2;
    const offsetY = (canvas.height - displayHeight) / 2;

    // 绘制图片
    ctx.drawImage(img, offsetX, offsetY, displayWidth, displayHeight);

    // 绘制遮罩层（除了裁剪区域其他地方变暗）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 清除裁剪区域（显示原图）
    ctx.globalCompositeOperation = 'destination-out';
    ctx.fillRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height);
    ctx.globalCompositeOperation = 'source-over';

    // 绘制裁剪区域边框
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.strokeRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height);

    // 绘制调整手柄
    const handleSize = 8;
    const handles = [
      { x: cropArea.x - handleSize/2, y: cropArea.y - handleSize/2, cursor: 'nw-resize' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y - handleSize/2, cursor: 'ne-resize' },
      { x: cropArea.x - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, cursor: 'sw-resize' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, cursor: 'se-resize' },
      { x: cropArea.x + cropArea.width/2 - handleSize/2, y: cropArea.y - handleSize/2, cursor: 'n-resize' },
      { x: cropArea.x + cropArea.width/2 - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, cursor: 's-resize' },
      { x: cropArea.x - handleSize/2, y: cropArea.y + cropArea.height/2 - handleSize/2, cursor: 'w-resize' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y + cropArea.height/2 - handleSize/2, cursor: 'e-resize' }
    ];

    ctx.fillStyle = '#3b82f6';
    handles.forEach(handle => {
      ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
    });

    // 绘制裁剪区域尺寸信息
    ctx.fillStyle = '#3b82f6';
    ctx.font = '12px Arial';
    const infoText = `${Math.round(cropArea.width)} × ${Math.round(cropArea.height)}`;
    const textX = cropArea.x + 5;
    const textY = cropArea.y - 5;
    if (textY > 15) {
      ctx.fillText(infoText, textX, textY);
    } else {
      ctx.fillText(infoText, textX, cropArea.y + 15);
    }
  }, [cropArea, scale, imageLoaded, containerDimensions]);

  // 重绘canvas当依赖变化时
  useEffect(() => {
    redrawCanvas();
  }, [redrawCanvas]);

  // 鼠标按下事件
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 检查是否点击了调整手柄
    const handleSize = 8;
    const handles = [
      { x: cropArea.x - handleSize/2, y: cropArea.y - handleSize/2, type: 'nw' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y - handleSize/2, type: 'ne' },
      { x: cropArea.x - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, type: 'sw' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, type: 'se' },
      { x: cropArea.x + cropArea.width/2 - handleSize/2, y: cropArea.y - handleSize/2, type: 'n' },
      { x: cropArea.x + cropArea.width/2 - handleSize/2, y: cropArea.y + cropArea.height - handleSize/2, type: 's' },
      { x: cropArea.x - handleSize/2, y: cropArea.y + cropArea.height/2 - handleSize/2, type: 'w' },
      { x: cropArea.x + cropArea.width - handleSize/2, y: cropArea.y + cropArea.height/2 - handleSize/2, type: 'e' }
    ];

    for (const handle of handles) {
      if (x >= handle.x && x <= handle.x + handleSize && y >= handle.y && y <= handle.y + handleSize) {
        setIsResizing(true);
        setResizeHandle(handle.type);
        setDragStart({ x, y });
        return;
      }
    }

    // 检查是否点击了裁剪区域内部（拖拽移动）
    if (x >= cropArea.x && x <= cropArea.x + cropArea.width && 
        y >= cropArea.y && y <= cropArea.y + cropArea.height) {
      setIsDragging(true);
      setDragStart({ x: x - cropArea.x, y: y - cropArea.y });
    }
  }, [cropArea]);

  // 鼠标移动事件
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    if (isDragging) {
      // 移动裁剪区域
      const newX = Math.max(0, Math.min(x - dragStart.x, containerDimensions.width - cropArea.width));
      const newY = Math.max(0, Math.min(y - dragStart.y, containerDimensions.height - cropArea.height));
      
      setCropArea(prev => ({
        ...prev,
        x: newX,
        y: newY
      }));
    } else if (isResizing) {
      // 调整裁剪区域大小
      const deltaX = x - dragStart.x;
      const deltaY = y - dragStart.y;
      
      setCropArea(prev => {
        let newArea = { ...prev };
        
        switch (resizeHandle) {
          case 'nw':
            newArea.x = Math.max(0, prev.x + deltaX);
            newArea.y = Math.max(0, prev.y + deltaY);
            newArea.width = Math.max(minCropSize, prev.width - deltaX);
            newArea.height = Math.max(minCropSize, prev.height - deltaY);
            break;
          case 'ne':
            newArea.y = Math.max(0, prev.y + deltaY);
            newArea.width = Math.max(minCropSize, Math.min(maxCropSize, prev.width + deltaX));
            newArea.height = Math.max(minCropSize, prev.height - deltaY);
            break;
          case 'sw':
            newArea.x = Math.max(0, prev.x + deltaX);
            newArea.width = Math.max(minCropSize, prev.width - deltaX);
            newArea.height = Math.max(minCropSize, Math.min(maxCropSize, prev.height + deltaY));
            break;
          case 'se':
            newArea.width = Math.max(minCropSize, Math.min(maxCropSize, prev.width + deltaX));
            newArea.height = Math.max(minCropSize, Math.min(maxCropSize, prev.height + deltaY));
            break;
          case 'n':
            newArea.y = Math.max(0, prev.y + deltaY);
            newArea.height = Math.max(minCropSize, prev.height - deltaY);
            break;
          case 's':
            newArea.height = Math.max(minCropSize, Math.min(maxCropSize, prev.height + deltaY));
            break;
          case 'w':
            newArea.x = Math.max(0, prev.x + deltaX);
            newArea.width = Math.max(minCropSize, prev.width - deltaX);
            break;
          case 'e':
            newArea.width = Math.max(minCropSize, Math.min(maxCropSize, prev.width + deltaX));
            break;
        }

        // 应用宽高比约束
        if (aspectRatio) {
          if (['nw', 'ne', 'sw', 'se'].includes(resizeHandle)) {
            newArea.height = newArea.width / aspectRatio;
          } else if (['n', 's'].includes(resizeHandle)) {
            newArea.width = newArea.height * aspectRatio;
          } else if (['w', 'e'].includes(resizeHandle)) {
            newArea.height = newArea.width / aspectRatio;
          }
        }

        // 确保不超出边界
        if (newArea.x + newArea.width > containerDimensions.width) {
          newArea.width = containerDimensions.width - newArea.x;
          if (aspectRatio) {
            newArea.height = newArea.width / aspectRatio;
          }
        }
        if (newArea.y + newArea.height > containerDimensions.height) {
          newArea.height = containerDimensions.height - newArea.y;
          if (aspectRatio) {
            newArea.width = newArea.height * aspectRatio;
          }
        }

        return newArea;
      });
      
      setDragStart({ x, y });
    }
  }, [isDragging, isResizing, dragStart, resizeHandle, aspectRatio, minCropSize, maxCropSize, containerDimensions]);

  // 鼠标抬起事件
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle('');
  }, []);

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  }, []);

  // 重置裁剪区域
  const handleReset = useCallback(() => {
    const defaultSize = Math.min(200, containerDimensions.width * 0.5, containerDimensions.height * 0.5);
    setCropArea({
      x: (containerDimensions.width - defaultSize) / 2,
      y: (containerDimensions.height - defaultSize) / 2,
      width: defaultSize,
      height: aspectRatio ? defaultSize / aspectRatio : defaultSize
    });
    setScale(1);
  }, [containerDimensions, aspectRatio]);

  // 完成裁剪
  const handleCropComplete = useCallback(async () => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    if (!canvas || !img) return;

    // 创建裁剪canvas
    const cropCanvas = document.createElement('canvas');
    const cropCtx = cropCanvas.getContext('2d')!;

    // 计算实际裁剪区域在原图中的位置
    const displayWidth = img.width * scale;
    const displayHeight = img.height * scale;
    const offsetX = (containerDimensions.width - displayWidth) / 2;
    const offsetY = (containerDimensions.height - displayHeight) / 2;

    // 计算裁剪区域相对于原图的位置和尺寸
    const cropX = (cropArea.x - offsetX) / scale;
    const cropY = (cropArea.y - offsetY) / scale;
    const cropWidth = cropArea.width / scale;
    const cropHeight = cropArea.height / scale;

    // 设置裁剪canvas尺寸
    cropCanvas.width = cropWidth;
    cropCanvas.height = cropHeight;

    // 绘制裁剪后的图片
    cropCtx.drawImage(
      img,
      cropX, cropY, cropWidth, cropHeight,
      0, 0, cropWidth, cropHeight
    );

    // 转换为数据URL
    const croppedImageUrl = cropCanvas.toDataURL('image/png', 0.9);
    
    // 返回裁剪结果
    onCropComplete(croppedImageUrl, {
      x: cropX,
      y: cropY,
      width: cropWidth,
      height: cropHeight
    });
  }, [cropArea, scale, containerDimensions, onCropComplete]);

  if (!imageLoaded) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between bg-muted/50 p-3 rounded-lg">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleZoomOut}>
            <ZoomOut className="w-4 h-4" />
          </Button>
          <span className="text-sm font-mono min-w-[60px] text-center">
            {Math.round(scale * 100)}%
          </span>
          <Button variant="outline" size="sm" onClick={handleZoomIn}>
            <ZoomIn className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleReset}>
            <RotateCcw className="w-4 h-4 mr-1" />
            重置
          </Button>
          <span className="text-sm text-muted-foreground">
            {Math.round(cropArea.width)} × {Math.round(cropArea.height)}
          </span>
        </div>
      </div>

      {/* 裁剪区域 */}
      <div 
        ref={containerRef}
        className="relative bg-gray-100 rounded-lg overflow-hidden"
        style={{ width: '100%', height: '400px' }}
      >
        <canvas
          ref={canvasRef}
          className="absolute inset-0 cursor-crosshair"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button onClick={handleCropComplete} className="flex items-center gap-2">
          <Crop className="w-4 h-4" />
          确认裁剪
        </Button>
      </div>
    </div>
  );
} 