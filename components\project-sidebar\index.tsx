"use client";
import { useState, useEffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back, memo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";
import { useUser } from "@/loomrunhooks/useUser";
import { cn } from "@/lib/utils";
import { useProjectContext } from "@/components/contexts/project-context";
import { FiGrid, FiSearch } from "react-icons/fi";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { unifiedCache } from "@/lib/unified-cache";
import { ProjectManager } from "@/lib/project-manager";
import { CommunityModal } from "@/components/community/community-modal";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import {
  Edit2,
  Star,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from "lucide-react";
import { Delete<PERSON>onfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { AuthModal } from "@/components/auth-modal";
import { GhostLogo } from "@/components/ui/ghost-logo";

interface Project {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  latest_version?: number;
  version_count?: number;
  message_count?: number;
}

interface ProjectSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  currentProjectId?: number;
  onProjectSelect?: (project: Project) => void;
  onNewProject?: () => void;
  // 🎯 新增：是否通过点击打开
  openedByClick?: boolean;
  // 🎯 新增：打开社区页面的回调
  onOpenCommunityPage?: () => void;
  // 🎯 新增：打开收藏页面的回调
  onOpenFavoritesPage?: () => void;
}

// 🚀 性能优化：使用memo包装项目卡片组件
const ProjectCard = memo(({ 
  project, 
  isActive, 
  onSelect, 
  onRename, 
  onDelete 
}: {
  project: Project;
  isActive: boolean;
  onSelect: (project: Project) => void;
  onRename: (id: number, newTitle: string) => void;
  onDelete: (project: Project) => void;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingTitle, setEditingTitle] = useState(project.title);

  const handleSelect = useCallback(() => {
    if (!isEditing) {
      onSelect(project);
    }
  }, [project, onSelect, isEditing]);

  const handleStartEdit = useCallback(() => {
    setIsEditing(true);
    setEditingTitle(project.title);
  }, [project.title]);

  const handleSaveEdit = useCallback(() => {
    if (editingTitle.trim() && editingTitle !== project.title) {
      onRename(project.id, editingTitle.trim());
    }
    setIsEditing(false);
  }, [project.id, project.title, editingTitle, onRename]);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    setEditingTitle(project.title);
  }, [project.title]);

  const handleDelete = useCallback(() => {
    onDelete(project);
  }, [project, onDelete]);

  return (
    <div
      onClick={handleSelect}
              className={cn(
          "group flex items-center justify-between px-2 py-1 rounded-lg cursor-pointer transition-all duration-200",
          isActive 
            ? "bg-accent text-accent-foreground" 
            : "text-foreground/80 hover:bg-accent/50 hover:text-foreground"
        )}
    >
      <div className="flex-1 min-w-0 mr-2">
        {isEditing ? (
          <Input
            value={editingTitle}
            onChange={(e) => setEditingTitle(e.target.value)}
            onBlur={handleSaveEdit}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSaveEdit();
              } else if (e.key === 'Escape') {
                handleCancelEdit();
              }
            }}
            className="h-6 px-2 py-0 text-sm bg-input border-border text-foreground"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div className="truncate">
            <div className="text-sm font-medium truncate leading-tight">
              {project.title}
            </div>
          </div>
        )}
      </div>
      
      <div 
        className="flex-shrink-0"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
        onMouseUp={(e) => e.stopPropagation()}
        onPointerDown={(e) => e.stopPropagation()}
        onPointerUp={(e) => e.stopPropagation()}
      >
        <DropdownMenu 
          modal={false}
          onOpenChange={(open) => {
            if (process.env.NODE_ENV === 'development') {
              console.log('🔥 DropdownMenu open state changed:', open);
            }
          }}
        >
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity text-foreground/60 hover:text-foreground hover:bg-accent/70"
              onClick={(e) => {
                if (process.env.NODE_ENV === 'development') {
                  console.log('🔥 Three dots button clicked');
                }
                e.stopPropagation();
                e.preventDefault();
                e.nativeEvent.stopImmediatePropagation();
              }}
              onMouseDown={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
              onMouseUp={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
              onPointerUp={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
            >
              <MoreHorizontal className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuPortal>
            <DropdownMenuContent 
              side="right"
              align="start"
              alignOffset={-5}
              sideOffset={5}
              className="w-32 bg-popover border-border !z-[60]"
              style={{ zIndex: 60 }}
              onClick={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
              onMouseDown={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
              }}
            >
            <DropdownMenuItem 
              onClick={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
                handleStartEdit();
              }} 
              className="text-popover-foreground hover:bg-accent text-sm py-1.5"
              onSelect={(event) => {
                event.preventDefault();
                handleStartEdit();
              }}
            >
              <Edit2 className="h-4 w-4 mr-2" />
              重命名
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
                toast.info("收藏功能即将推出");
              }} 
              className="text-popover-foreground hover:bg-accent text-sm py-1.5"
              onSelect={(event) => {
                event.preventDefault();
                toast.info("收藏功能即将推出");
              }}
            >
              <Star className="h-4 w-4 mr-2" />
              收藏
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-border" />
            <DropdownMenuItem 
              onClick={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
                handleDelete();
              }}
              className="text-red-400 hover:text-red-300 hover:bg-red-950/20 text-sm py-1.5"
              onSelect={(event) => {
                event.preventDefault();
                handleDelete();
              }}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenuPortal>
        </DropdownMenu>
      </div>
    </div>
  );
});

ProjectCard.displayName = "ProjectCard";

// 🚀 性能优化：使用memo包装主组件
export const ProjectSidebar = memo(function ProjectSidebar({ 
  isOpen, 
  onClose, 
  currentProjectId,
  onProjectSelect,
  onNewProject,
  openedByClick = false,
  onOpenCommunityPage,
  onOpenFavoritesPage
}: ProjectSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useUser();
  const { shouldRefreshProjects, resetRefreshFlag } = useProjectContext();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  const [sidebarWidth, setSidebarWidth] = useState(280); // 紧凑尺寸: 280px
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [isDeletingProject, setIsDeletingProject] = useState(false);
  const [isCommunityModalOpen, setIsCommunityModalOpen] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 🚀 性能优化：使用useMemo缓存计算结果并去重 + 虚拟化准备
  const memoizedFilteredProjects = useMemo(() => {
    // 🔧 首先去重：确保每个项目ID只出现一次
    const uniqueProjects = projects.reduce((acc, current) => {
      const existing = acc.find(item => item.id === current.id);
      if (!existing) {
        acc.push(current);
      }
      return acc;
    }, [] as Project[]);

    if (!searchTerm.trim()) return uniqueProjects;
    
    const term = searchTerm.toLowerCase();
    return uniqueProjects.filter(project =>
      project.title.toLowerCase().includes(term) ||
      project.id.toString().includes(term)
    );
  }, [projects, searchTerm]);

  // 🚀 新增：虚拟滚动配置 - 性能优化大列表
  const [scrollTop, setScrollTop] = useState(0);
  const itemHeight = 32; // 🔧 修正：根据实际项目卡片高度调整 (px-2 py-1 + text height ≈ 32px)
  const containerHeight = 400; // 可视区域高度
  const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // 缓冲区
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 1);
  const endIndex = Math.min(memoizedFilteredProjects.length, startIndex + visibleCount);
  
  // 🚀 计算可见项目列表 - 只渲染可见部分
  const visibleProjects = useMemo(() => {
    // 🔧 修复：提高虚拟滚动阈值，避免中小型列表显示不全
    if (memoizedFilteredProjects.length <= 50) {
      // 50个以下的列表直接全部渲染，避免显示不全问题
      return memoizedFilteredProjects;
    }
    
    // 大列表使用虚拟滚动
    return memoizedFilteredProjects.slice(startIndex, endIndex);
  }, [memoizedFilteredProjects, startIndex, endIndex]);

  // 🚀 处理滚动事件 - 节流优化
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    setScrollTop(target.scrollTop);
  }, []);

  // 🚀 节流滚动处理
  const throttledHandleScroll = useMemo(
    () => {
      let timeoutId: NodeJS.Timeout;
      return (e: React.UIEvent<HTMLDivElement>) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => handleScroll(e), 16); // 60fps
      };
    },
    [handleScroll]
  );

  // 简单的客户端挂载检测
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 响应式宽度监听 - 移动端全屏覆盖设计
  useEffect(() => {
    if (!isMounted) return;

    const updateSidebarWidth = () => {
      setSidebarWidth(window.innerWidth >= 1024 ? 280 : window.innerWidth); // 桌面: 280px, 移动端: 100vw
    };

     // 初始设置
     updateSidebarWidth();

     // 监听窗口尺寸变化
     window.addEventListener('resize', updateSidebarWidth);
     return () => window.removeEventListener('resize', updateSidebarWidth);
   }, []);

  // 🔥 高效项目加载函数（优化版）
  const loadProjects = useCallback(async (forceRefresh = false) => {
    if (!user?.id) return;
    
    // 🚫 防止频繁请求（减少到1秒，项目创建后立即可见）
    const now = Date.now();
          if (!forceRefresh && now - lastLoadTime < 1000) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🛑 防止频繁请求，使用缓存数据');
        }
        return;
      }

    try {
      setIsLoading(true);
      
              if (process.env.NODE_ENV === 'development') {
          console.log(`🚀 开始加载项目列表 (用户${user.id})`);
        }
      
             // 🎯 使用统一缓存系统
       const projectList = await unifiedCache.getProjectList(parseInt(user.id), forceRefresh);
      
      setProjects(projectList);
      setLastLoadTime(now);
      setIsInitialLoad(false);
      
              if (process.env.NODE_ENV === 'development') {
          console.log(`✅ 项目列表加载完成 (${projectList.length}个项目)`);
          // 📊 显示缓存统计（仅开发环境）
          const stats = unifiedCache.getStats();
          console.log('📊 缓存统计:', stats);
        }
      
          } catch (err) {
        console.error('❌ 项目列表加载失败:', err);
      } finally {
      setIsLoading(false);
    }
  }, [user?.id, lastLoadTime]);

  // 🔄 初始化和刷新逻辑 - 优化版本
  useEffect(() => {
    if (isOpen && user?.id && projects.length === 0 && !isLoading) {
      // 只在项目列表为空且没有正在加载时才加载
      loadProjects();
    }
  }, [isOpen, user?.id]); // 移除projects.length和loadProjects依赖避免循环

  // 监听用户登录状态变化
  useEffect(() => {
    if (user?.id && projects.length === 0 && !isLoading) {
      // 只在用户存在且项目列表为空且没有正在加载时才加载
      loadProjects();
    } else if (!user) {
      setProjects([]);
    }
  }, [user?.id]); // 移除projects.length和loadProjects依赖避免循环

  // 监听项目刷新标志
  useEffect(() => {
    if (shouldRefreshProjects && user?.id) {
      loadProjects(true); // 强制刷新
      resetRefreshFlag();
    }
  }, [shouldRefreshProjects, user?.id, resetRefreshFlag]); // 移除loadProjects依赖避免循环

  // 🚀 监听强制刷新事件（来自项目创建完成）
  useEffect(() => {
    const handleForceRefresh = (event: CustomEvent) => {
      console.log('🔄 ProjectSidebar: 收到强制刷新事件', {
        detail: event.detail,
        userId: user?.id
      });
      
      if (user?.id) {
        console.log('🚀 ProjectSidebar: 执行强制刷新项目列表');
        loadProjects(true); // 强制刷新，忽略缓存
      }
    };

    window.addEventListener('forceRefreshProjects', handleForceRefresh as EventListener);
    
    return () => {
      window.removeEventListener('forceRefreshProjects', handleForceRefresh as EventListener);
    };
  }, [user?.id, loadProjects]);



  // 🎯 项目选择处理（优化版）
  const handleProjectSelect = useCallback(async (project: Project) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 选择项目:', project.id);
      }
      
      if (onProjectSelect) {
        onProjectSelect(project);
      } else {
        router.push(`/projects/${project.id}`);
      }
      
      onClose();
    } catch (error) {
      console.error('❌ 项目选择失败:', error);
    }
  }, [onProjectSelect, router, onClose]);

  // 🎯 创建新项目（智能优化版）
  const handleNewProject = useCallback(() => {
    const startTime = performance.now();
    
    // 🔍 智能判断当前页面状态
    if (pathname === '/projects/new') {
      // 📱 在欢迎界面：直接关闭项目栏即可
      if (process.env.NODE_ENV === 'development') {
        console.log('🏠 在欢迎界面，关闭项目栏');
      }
      onClose();
      
      // 📊 性能日志
              if (process.env.NODE_ENV === 'development') {
          const duration = performance.now() - startTime;
          console.log(`⚡ New Chat 智能操作: 关闭项目栏 (${duration.toFixed(1)}ms)`);
        }
      return;
    }
    
    // 🔄 在编辑器界面：跳转到欢迎界面
    if (pathname.startsWith('/projects/')) {
      if (process.env.NODE_ENV === 'development') {
        console.log('📝 在编辑器界面，跳转到欢迎界面');
      }
      router.push('/projects/new');
      onClose();
      
      // 📊 性能日志
              if (process.env.NODE_ENV === 'development') {
          const duration = performance.now() - startTime;
          console.log(`⚡ New Chat 智能操作: 跳转到欢迎界面 (${duration.toFixed(1)}ms)`);
        }
      return;
    }
    
    // 🚀 其他情况：执行原有的新建项目逻辑
    if (!user) {
      toast.info("您可以开始创建项目，稍后可以登录保存");
    }
    onNewProject?.();
    onClose();
    
    // 📊 性能日志
          if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.log(`⚡ New Chat 智能操作: 新建项目 (${duration.toFixed(1)}ms)`);
      }
  }, [pathname, router, onClose, onNewProject, user]);

  // 🎯 重命名项目
  const handleRenameProject = useCallback(async (projectId: number, newTitle: string) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🏷️ 开始重命名项目:', { projectId, newTitle });
      }

      const response = await fetch(`/api/me/projects/${projectId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ title: newTitle }),
      });

      const data = await response.json();

      if (response.ok && data.ok) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 项目重命名成功:', { projectId, newTitle });
        }
        
        // 🔄 乐观更新UI - 立即更新项目列表中的标题
        setProjects(prev => prev.map(project => 
          project.id === projectId 
            ? { ...project, title: newTitle, updated_at: new Date().toISOString() }
            : project
        ));
        
        toast.success("项目重命名成功");
        
        // 🚀 后台刷新确保数据一致性
        setTimeout(() => {
          loadProjects(true);
        }, 500);

      } else {
        console.error('❌ 重命名API失败:', data);
        toast.error(data.error || data.message || "重命名失败");
      }
    } catch (error) {
      console.error("❌ 重命名项目网络错误:", error);
      toast.error("网络错误，请重试");
    }
  }, [setProjects, loadProjects]);

  // 🎯 显示删除确认对话框
  const showDeleteConfirmation = useCallback((project: Project) => {
    setProjectToDelete(project);
    setDeleteDialogOpen(true);
  }, []);

  // 🎯 执行删除项目 - 高效优化版本
  const executeDeleteProject = useCallback(async () => {
    if (!projectToDelete) return;

    setIsDeletingProject(true);
    
    // 🚀 立即更新UI状态（乐观更新）
    const remainingProjects = projects.filter(p => p.id !== projectToDelete.id);
    setProjects(remainingProjects);
    
    // 🔄 立即执行跳转逻辑
    if (currentProjectId === projectToDelete.id) {
      if (remainingProjects.length > 0) {
        router.push(`/projects/${remainingProjects[0].id}`);
      } else {
        router.push("/projects/new");
      }
    }
    
    // 关闭对话框（立即响应）
    setDeleteDialogOpen(false);
    setProjectToDelete(null);
    setIsDeletingProject(false);
    
    try {
      // 🚀 后台执行实际删除操作
      const projectManager = ProjectManager.getInstance();
      const result = await projectManager.deleteProject(projectToDelete.id);

      if (!result.success) {
        // ❌ 删除失败，回滚UI状态
        console.error('删除项目失败:', result.error);
        setProjects(projects); // 恢复原状态
        
        if (result.error !== '项目正在删除中') {
          toast.error(result.error || "删除失败，请重试");
        }
      }
    } catch (error) {
      console.error("删除项目异常:", error);
      // 回滚UI状态
      setProjects(projects);
      toast.error("删除失败，请重试");
    }
  }, [projectToDelete, projects, setProjects, currentProjectId, router]);

  // 🎯 取消删除
  const cancelDelete = useCallback(() => {
    setDeleteDialogOpen(false);
    setProjectToDelete(null);
    setIsDeletingProject(false);
  }, []);

  // 🎯 处理登录成功
  const handleLoginSuccess = useCallback(async () => {
    setAuthModalOpen(false);
    // 刷新用户状态会在useUser hook中自动处理
    // 项目列表会在用户状态变化后自动重新加载
  }, []);



  // 🎯 删除项目
  const handleDeleteProject = useCallback((project: Project) => {
    showDeleteConfirmation(project);
  }, [showDeleteConfirmation]);

  if (!isOpen) return null;

  // 简单的移动端检测
  const isMobile = isMounted && typeof window !== 'undefined' && window.innerWidth < 1024;

  return (
    <>
      {/* 移动端背景遮罩 - 确保不干扰侧边栏内容 */}
      {isMobile && (
        <div
          className="fixed inset-0 bg-black/50 z-[99998]"
          onClick={(e) => {
            // 只有点击遮罩本身才关闭，不影响侧边栏内容
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        />
      )}

      <div
        className={cn(
          "fixed bg-background border-r border-border transition-transform duration-200 ease-out",
          isOpen ? "translate-x-0" : "-translate-x-full",
          isMobile
            ? "inset-0 z-[99999]" // 移动端全屏
            : "left-0 top-[44px] w-[280px] h-[calc(100vh-44px)] z-40" // 桌面端
        )}
        onClick={(e) => {
          // 阻止事件冒泡到背景遮罩
          e.stopPropagation();
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
        }}
        onMouseLeave={(e) => {
          // 🎯 鼠标离开项目栏右边缘时收起（仅在悬停打开时）
          const rect = e.currentTarget.getBoundingClientRect();
          const mouseX = e.clientX;
          const sidebarRight = rect.right;
          
          // 如果鼠标离开了项目栏的右边缘，且不是点击打开的，则收起
          if (mouseX > sidebarRight && isOpen && !openedByClick) {
            onClose();
          }
        }}
      >
        <div className="flex flex-col h-full">
          {/* 移动端顶部栏 */}
          {isMobile && (
            <div
              className="flex items-center justify-between p-4 border-b bg-background relative z-10"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center gap-3">
                <div className="w-7 h-7 rounded-lg flex items-center justify-center shadow-sm">
                  <GhostLogo size={24} />
                </div>
                <div className="flex flex-col">
                  <span className="text-foreground font-semibold text-base">LoomRun</span>
                  <span className="text-muted-foreground text-xs">Free</span>
                </div>
              </div>
              <button
                onClick={(e) => {
                  console.log('关闭按钮被点击，调用onClose'); // 调试日志
                  e.preventDefault();
                  e.stopPropagation();
                  onClose();
                }}
                onTouchStart={(e) => {
                  e.stopPropagation();
                }}
                className="w-12 h-12 rounded-lg hover:bg-secondary/80 active:bg-secondary flex items-center justify-center relative z-20 touch-manipulation"
                style={{
                  WebkitTapHighlightColor: 'transparent',
                  touchAction: 'manipulation',
                  minWidth: '48px',
                  minHeight: '48px'
                }}
              >
                <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {/* 头部区域 */}
          <div className="p-4 flex-shrink-0">
            {/* New Chat 按钮 - 专业对齐 */}
            <Button
              onClick={handleNewProject}
              className="w-full mb-3 bg-secondary hover:bg-secondary/80 text-foreground border border-border rounded-lg h-9 text-sm font-medium transition-all duration-200 flex items-center justify-center"
              title={
                pathname === '/projects/new'
                  ? '关闭项目栏'
                  : pathname.startsWith('/projects/')
                    ? '返回欢迎界面'
                    : '开始新对话'
              }
            >
              新建对话
            </Button>

            {/* 搜索框 */}
            <div className="relative mb-3">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索项目"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 bg-background border-border text-foreground placeholder-muted-foreground focus:border-ring h-9 text-sm rounded-lg"
              />
            </div>

            {/* 导航菜单 - 统一图标风格 */}
            <div className="space-y-1 mb-3">
              <div
                onClick={() => {
                  // 通过回调通知父组件打开社区页面
                  if (onOpenCommunityPage) {
                    onOpenCommunityPage();
                  } else {
                    setIsCommunityModalOpen(true);
                  }
                }}
                className="group text-muted-foreground hover:bg-secondary hover:text-foreground px-3 py-2.5 rounded-lg cursor-pointer text-sm flex items-center gap-3 transition-all duration-200"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <Globe className="w-4 h-4" />
                </div>
                <span className="font-medium">LoomRun 社区</span>
              </div>
              <div
                onClick={() => {
                  // 🔐 检查用户登录状态
                  if (!user) {
                    console.log('⚠️ ProjectSidebar: 用户未登录，显示登录弹窗');
                    // 触发登录弹窗
                    window.dispatchEvent(new CustomEvent('show-login-modal'));
                    return;
                  }

                  if (onOpenFavoritesPage) {
                    onOpenFavoritesPage();
                  } else {
                    // 如果没有回调，直接导航到收藏页面
                    router.push('/favorites');
                  }
                }}
                className="text-muted-foreground hover:bg-secondary hover:text-foreground px-3 py-2.5 rounded-lg cursor-pointer text-sm flex items-center gap-3 transition-all duration-200"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <Star className="w-4 h-4" />
                </div>
                <span className="font-medium">收藏项目</span>
              </div>
              <div
                onClick={() => {
                  // 收藏对话功能 - 待实现
                  console.log('收藏对话功能待实现');
                }}
                className="text-muted-foreground hover:bg-secondary hover:text-foreground px-3 py-2.5 rounded-lg cursor-pointer text-sm flex items-center gap-3 transition-all duration-200"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <Heart className="w-4 h-4" />
                </div>
                <span className="font-medium">收藏对话</span>
              </div>
            </div>

            {/* 最近项目标题 - 固定不滚动，只有登录用户才显示 */}
            {user && (
              <div className="flex items-center justify-between mb-1 px-1">
                <h3 className="text-sm font-semibold text-muted-foreground">最近</h3>
              </div>
            )}
          </div>



          {/* 项目列表区域 */}
          <div className="flex-1 overflow-y-auto px-4 pt-1 pb-2" onScroll={throttledHandleScroll}>

            {/* 🔐 未登录状态显示 */}
            {!user ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="w-12 h-12 mb-4 rounded-full flex items-center justify-center">
                  <GhostLogo size={40} />
                </div>
                <h3 className="text-base font-semibold text-foreground mb-2">
                  还没登录
                </h3>
                <p className="text-sm text-muted-foreground mb-6 leading-relaxed max-w-[200px]">
                  登录后管理你的项目，保存创作历史，参与社区分享
                </p>
                <Button
                  onClick={() => {
                    console.log('⚠️ ProjectSidebar: 点击登录按钮，显示登录弹窗');
                    // 触发登录弹窗
                    window.dispatchEvent(new CustomEvent('show-login-modal'));
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white border-0 px-6 py-2 rounded-lg font-medium"
                >
                  立即登录
                </Button>
              </div>
            ) : (
              <>
                {/* 加载状态 */}
                {isLoading && isInitialLoad && (
                  <div className="flex items-center justify-center py-8">
                    <div className="w-5 h-5 border-2 border-muted border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}

                {/* 项目列表 */}
                {!isLoading || !isInitialLoad ? (
                  visibleProjects.length > 0 ? (
                    <div className="space-y-0" style={{
                      // 🚀 虚拟滚动：设置总高度和偏移
                      paddingTop: memoizedFilteredProjects.length > 50 ? startIndex * itemHeight : 0,
                      paddingBottom: memoizedFilteredProjects.length > 50 ?
                        (memoizedFilteredProjects.length - endIndex) * itemHeight : 0,
                      minHeight: memoizedFilteredProjects.length > 50 ?
                        memoizedFilteredProjects.length * itemHeight : 'auto'
                    }}>
                      {visibleProjects.map((project) => (
                        <ProjectCard
                          key={`project-${project.id}-${project.updated_at}`} // 🔧 优化key，避免不必要的重渲染
                          project={project}
                          isActive={currentProjectId === project.id}
                          onSelect={handleProjectSelect}
                          onRename={handleRenameProject}
                          onDelete={handleDeleteProject}
                        />
                      ))}
                    </div>
                  ) : (
                    // 空状态 - 优化版本
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="w-12 h-12 mb-4 rounded-full bg-muted flex items-center justify-center">
                        <FiGrid className="w-6 h-6 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {searchTerm ? '未找到匹配的项目' : '还没有项目'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {searchTerm ? '尝试调整搜索条件' : '点击上方"新建对话"开始创建'}
                      </p>
                    </div>
                  )
                ) : null}
              </>
            )}
          </div>

          {/* 底部安全间距区域 - 确保与系统底部有足够间距 */}
          <div className="flex-shrink-0 h-8 bg-card"></div>

        </div>
      </div>

      {/* 删除确认对话框 */}
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="删除项目"
        description="您确定要删除这个项目吗？项目中的所有数据和聊天记录都将被永久删除。"
        itemName={projectToDelete?.title}
        onConfirm={executeDeleteProject}
        onCancel={cancelDelete}
        isLoading={isDeletingProject}
      />

      {/* 社区模态框 */}
      <CommunityModal
        isOpen={isCommunityModalOpen}
        onClose={() => setIsCommunityModalOpen(false)}
        onOpenProject={async (project) => {
          // 🎯 处理打开社区项目的逻辑
          try {
            const projectManager = ProjectManager.getInstance();
            const projectIdStr = await projectManager.createProjectFromCommunityProject(project.title, project.htmlContent);

            if (projectIdStr && onProjectSelect) {
              // 🎯 模拟项目对象，对话历史已在后端自动创建
              const newProject = {
                id: parseInt(projectIdStr),
                title: project.title,
                html_content: project.htmlContent,
                prompts: [project.title],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                latest_version: 1,
                version_count: 1,
                message_count: 2 // 用户消息 + AI消息
              };

              onProjectSelect(newProject);
            }
          } catch (error) {
            console.error('打开社区项目失败:', error);
          }
        }}
      />

      {/* 认证模态框 */}
      <AuthModal
        open={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        onSuccess={handleLoginSuccess}
        title="登录 LoomRun"
        description="登录后即可保存项目、管理历史记录"
      />
    </>
  );
});