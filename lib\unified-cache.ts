/**
 * 🚀 统一高性能缓存系统 - 合并所有缓存功能
 * 目标: 快速查看、高效率、低能耗、智能缓存
 */

import { toast } from 'sonner';
import { PerformanceLogger } from './performance-logger';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isGenerating?: boolean;
  htmlContent?: string;
  versionNumber?: number;
}

// 📊 数据类型定义
interface ProjectItem {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  latest_version?: number;
  version_count?: number;
  message_count?: number;
}

// 🔥 新增：社区项目类型定义
interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
    avatar_url?: string;
  };
  favoritesCount?: number;
  isFavorited?: boolean;
}

interface CommunityProjectsResponse {
  projects: CommunityProject[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

interface ProjectData {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  chatHistory: unknown[];
  versions: unknown[];
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  etag?: string;
  maxAge: number;
  accessCount: number;
  lastAccessed: number;
  priority: number; // 优先级：越高越不容易被淘汰
}

interface RequestCache {
  promise: Promise<unknown>;
  timestamp: number;
}

// ⚡ 企业级优化配置
const CONFIG = {
  // 缓存TTL - 更智能的缓存策略
  PROJECT_LIST_TTL: 5 * 60 * 1000,     // 5分钟 (频繁更新的列表)
  PROJECT_DETAIL_TTL: 15 * 60 * 1000,  // 15分钟 (相对稳定的详情)
  
  // 内存限制 - 企业级配置
  MAX_MEMORY_ITEMS: 150,               // 增加内存容量
  MAX_STORAGE_SIZE: 50,                // 50MB存储限制
  
  // 网络优化 - 更强的容错性
  REQUEST_TIMEOUT: 10000,              // 10秒超时 (平衡性能与稳定性)
  MAX_RETRIES: 2,                      // 减少重试次数避免过长等待
  
  // 预加载优化 - 智能预加载
  PREFETCH_DELAY: 300,                 // 300ms延迟预加载
  PREFETCH_COUNT: 3,                   // 预加载3个项目
  
  // 性能优化 - 更频繁的维护
  CLEANUP_INTERVAL: 10 * 60 * 1000,    // 10分钟清理一次
  SAVE_DEBOUNCE: 500,                  // 500ms保存防抖
  
  // 🔥 新增：智能缓存策略
  STALE_WHILE_REVALIDATE: true,        // 使用过期缓存同时后台更新
  BACKGROUND_REFRESH_THRESHOLD: 0.7,   // 缓存剩余30%时间时开始后台刷新
  MAX_BACKGROUND_REQUESTS: 2,          // 同时最多2个后台请求
};

export class UnifiedCache {
  private static instance: UnifiedCache;
  
  // 🔥 多级缓存存储
  private memoryCache = new Map<string, CacheEntry<unknown>>();
  private pendingRequests = new Map<string, RequestCache>();
  
  // 📊 性能统计（简化）
  private stats = {
    hits: 0,
    misses: 0,
    requests: 0,
    startTime: Date.now()
  };
  
  // ⏰ 定时器
  private cleanupTimer: NodeJS.Timeout | null = null;

  private projectListCache: ProjectItem[] | null = null;
  private projectDetailCache: Map<number, ProjectData> = new Map();
  private chatHistoryCache: Map<number, ChatMessage[]> = new Map();
  // 🔥 新增：社区项目缓存
  private communityProjectsCache: Map<string, CommunityProjectsResponse> = new Map();
  // 🎯 新增：项目保存状态缓存
  private projectSaveStatusCache: Map<number, { saving: boolean; lastSaved: number }> = new Map();
  private lastFetchTime: Map<string, number> = new Map();
  
  // 🔥 新增：后台刷新控制
  private backgroundRefreshCount = 0;
  
  // 🔧 缓存有效期配置（性能优化）
  private readonly CACHE_DURATION = {
    PROJECT_LIST: 5 * 60 * 1000,      // 5分钟
    PROJECT_DETAIL: 10 * 60 * 1000,   // 10分钟  
    CHAT_HISTORY: 2 * 60 * 1000,      // 2分钟
    SAVE_STATUS: 30 * 1000,           // 30秒
    COMMUNITY_PROJECTS: 5 * 60 * 1000 // 5分钟
  };

  private constructor() {
    this.initializeCleanup();
    
    // 🔧 生产环境关闭性能监控
    if (process.env.NODE_ENV === 'development') {
      this.setupPerformanceMonitoring();
    }
  }

  static getInstance(): UnifiedCache {
    if (!UnifiedCache.instance) {
      UnifiedCache.instance = new UnifiedCache();
    }
    return UnifiedCache.instance;
  }

  // 🎯 企业级高性能项目列表获取
  async getProjectList(userId: number, forceRefresh = false): Promise<ProjectItem[]> {
    const cacheKey = `projects_${userId}`;
    
    try {
      // 🔍 检查内存缓存 - 智能缓存策略
      if (!forceRefresh) {
        const memoryResult = this.getFromMemory<ProjectItem[]>(cacheKey);
        if (memoryResult) {
          this.stats.hits++;
          
          // 🔥 智能后台刷新：缓存接近过期时触发后台更新
          const cacheAge = Date.now() - (this.lastFetchTime.get(cacheKey) || 0);
          const shouldBackgroundRefresh = cacheAge > (CONFIG.PROJECT_LIST_TTL * CONFIG.BACKGROUND_REFRESH_THRESHOLD);
          
          if (CONFIG.STALE_WHILE_REVALIDATE && shouldBackgroundRefresh) {
            // 返回当前缓存，同时触发后台刷新
            this.backgroundRefreshProjectList(userId, cacheKey);
          }
          
          // 🚀 智能预加载（不阻塞主线程）
          this.asyncPrefetchProjects(memoryResult.slice(0, CONFIG.PREFETCH_COUNT));
          return memoryResult;
        }

        // 🔍 检查持久化缓存
        const storageResult = await this.getFromStorage<ProjectItem[]>(cacheKey);
        if (storageResult) {
          this.stats.hits++;
          this.setToMemory(cacheKey, storageResult, CONFIG.PROJECT_LIST_TTL, 5); // 高优先级
          
          // 检查是否需要后台刷新
          const storageAge = Date.now() - (this.lastFetchTime.get(cacheKey) || 0);
          if (CONFIG.STALE_WHILE_REVALIDATE && storageAge > (CONFIG.PROJECT_LIST_TTL * CONFIG.BACKGROUND_REFRESH_THRESHOLD)) {
            this.backgroundRefreshProjectList(userId, cacheKey);
          }
          
          this.asyncPrefetchProjects(storageResult.slice(0, CONFIG.PREFETCH_COUNT));
          return storageResult;
        }
      }

      // 🔄 防重复请求检查 - 增强版
      const pendingRequest = this.pendingRequests.get(cacheKey);
      if (pendingRequest && !this.isRequestExpired(pendingRequest)) {
        // 如果有其他请求正在进行，等待其完成
        return await pendingRequest.promise as ProjectItem[];
      }

      // 🌐 发起网络请求 - 企业级容错
      const networkPromise = this.fetchProjectListFromNetworkEnhanced();
      this.pendingRequests.set(cacheKey, {
        promise: networkPromise,
        timestamp: Date.now()
      });

      const result = await networkPromise;
      this.pendingRequests.delete(cacheKey);

      // 🎯 缓存结果 - 多层缓存策略
      if (result && result.length > 0) {
        this.setToMemory(cacheKey, result, CONFIG.PROJECT_LIST_TTL, 5);
        await this.setToStorage(cacheKey, result, CONFIG.PROJECT_LIST_TTL);
        this.lastFetchTime.set(cacheKey, Date.now());
        
        // 🚀 智能预加载前几个项目的详情
        this.asyncPrefetchProjects(result.slice(0, CONFIG.PREFETCH_COUNT));
      }

      this.stats.misses++;
      this.stats.requests++;
      
      return result || [];

    } catch (error) {
      this.pendingRequests.delete(cacheKey);
      
      console.warn('🔄 项目列表请求失败，尝试降级策略:', error);
      
      // 🔄 1. 尝试获取过期缓存作为降级 - Stale-While-Revalidate
      const fallbackResult = await this.getFallbackData<ProjectItem[]>(cacheKey);
      if (fallbackResult) {
        console.log('✅ 使用过期缓存数据降级', { count: fallbackResult.length });
        
        // 在后台尝试刷新数据（不阻塞用户）
        setTimeout(() => {
          this.backgroundRefreshProjectList(userId, cacheKey);
        }, 1000);
        
        if (typeof window !== 'undefined') {
          // 用户友好的提示，而不是直接报错
          const { toast } = await import('sonner');
          toast.warning('网络较慢，显示缓存数据', {
            description: '数据正在后台更新',
            duration: 3000
          });
        }
        return fallbackResult;
      }
      
      // 🔄 2. 如果是超时错误，返回空数组而不是抛出异常
      if (error instanceof Error && (error.message.includes('超时') || error.message.includes('timeout'))) {
        console.log('⏰ 请求超时，返回空数组');
        if (typeof window !== 'undefined') {
          const { toast } = await import('sonner');
          toast.error('网络连接超时', {
            description: '请检查网络连接后重试',
            action: {
              label: '重试',
              onClick: () => this.getProjectList(userId, true)
            }
          });
        }
        return [];
      }
      
      // 🔄 3. 其他错误也返回空数组，避免应用崩溃
      console.error('❌ 项目列表获取失败:', error);
      if (typeof window !== 'undefined') {
        const { toast } = await import('sonner');
        toast.error('加载项目失败', {
          description: '请稍后重试',
          action: {
            label: '重试',
            onClick: () => this.getProjectList(userId, true)
          }
        });
      }
      return [];
    }
  }

  // 🎯 高效项目详情获取
  async getProjectDetail(projectId: number, forceRefresh = false): Promise<ProjectData | null> {
    const cacheKey = `project_detail_${projectId}`;
    
    try {
      // 🔍 检查内存缓存
      if (!forceRefresh) {
        const memoryResult = this.getFromMemory<ProjectData>(cacheKey);
        if (memoryResult) {
          this.stats.hits++;
          return memoryResult;
        }

        // 🔍 检查持久化缓存
        const storageResult = await this.getFromStorage<ProjectData>(cacheKey);
        if (storageResult) {
          this.stats.hits++;
          this.setToMemory(cacheKey, storageResult, CONFIG.PROJECT_DETAIL_TTL, 3);
          return storageResult;
        }
      }

      // 🔄 防重复请求检查
      const pendingRequest = this.pendingRequests.get(cacheKey);
      if (pendingRequest && !this.isRequestExpired(pendingRequest)) {
        return await pendingRequest.promise as ProjectData;
      }

      // 🌐 发起网络请求
      const networkPromise = this.fetchProjectDetailFromNetwork(projectId);
      this.pendingRequests.set(cacheKey, {
        promise: networkPromise,
        timestamp: Date.now()
      });

      const result = await networkPromise;
      this.pendingRequests.delete(cacheKey);

      if (result) {
        this.setToMemory(cacheKey, result, CONFIG.PROJECT_DETAIL_TTL, 3);
        await this.setToStorage(cacheKey, result, CONFIG.PROJECT_DETAIL_TTL);
      }

      this.stats.misses++;
      this.stats.requests++;
      
      return result;

    } catch {
      this.pendingRequests.delete(cacheKey);
      
      const fallbackResult = await this.getFallbackData<ProjectData>(cacheKey);
      if (fallbackResult) {
        if (process.env.NODE_ENV === 'development') {
          toast.warning('使用缓存数据，信息可能不是最新的');
        }
        return fallbackResult;
      }
      
      return null;
    }
  }

  // 📊 获取性能统计（简化）
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? ((this.stats.hits / totalRequests) * 100).toFixed(1) : '0';
    
    return {
      hitRate: `${hitRate}%`,
      networkRequests: this.stats.requests,
      cacheSize: this.memoryCache.size,
    };
  }

  // 🔥 私有方法实现
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;
    
    if (this.isCacheExpired(entry)) {
      this.memoryCache.delete(key);
      return null;
    }
    
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    return entry.data as T;
  }

  private setToMemory<T>(key: string, data: T, maxAge: number, priority = 1): void {
    // 智能LRU淘汰机制
    if (this.memoryCache.size >= CONFIG.MAX_MEMORY_ITEMS) {
      this.evictLRUMemoryCache();
    }
    
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      maxAge,
      accessCount: 1,
      lastAccessed: Date.now(),
      priority
    });
  }

  private async getFromStorage<T>(key: string): Promise<T | null> {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = localStorage.getItem(`loomrun_unified_${key}`);
      if (!stored) return null;
      
      const entry: CacheEntry<T> = JSON.parse(stored);
      
      if (this.isCacheExpired(entry)) {
        localStorage.removeItem(`loomrun_unified_${key}`);
        return null;
      }
      
      return entry.data;
    } catch {
      return null;
    }
  }

  private async setToStorage<T>(key: string, data: T, maxAge: number): Promise<void> {
    if (typeof window === 'undefined') return;
    
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        maxAge,
        accessCount: 1,
        lastAccessed: Date.now(),
        priority: 1
      };
      
      localStorage.setItem(`loomrun_unified_${key}`, JSON.stringify(entry));
    } catch (error) {
      // 静默处理存储错误
      if (process.env.NODE_ENV === 'development') {
        console.warn('存储缓存写入失败:', error);
      }
    }
  }

  private async fetchProjectListFromNetworkEnhanced(): Promise<ProjectItem[]> {
    let lastError: Error | null = null;
    
    // 🔄 实现重试机制 - 优化重试策略
    for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
      const controller = new AbortController();
      let timeoutId: NodeJS.Timeout | null = null;
      
      try {
        console.log(`🌐 尝试获取项目列表 (${attempt}/${CONFIG.MAX_RETRIES})`);
        
        // 🚀 优化超时时间：第一次尝试较短，后续增加
        const timeoutDuration = attempt === 1 ? 5000 : CONFIG.REQUEST_TIMEOUT;
        timeoutId = setTimeout(() => {
          if (!controller.signal.aborted) {
            console.log(`⏰ 项目列表请求超时 (${timeoutDuration}ms)，中止请求`);
            controller.abort();
          }
        }, timeoutDuration);
        
        const response = await fetch('/api/me/projects', {
          credentials: 'include',
          signal: controller.signal,
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });
        
        // 🧹 及时清理超时
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        
        if (!response.ok) {
          // 🔧 优化错误处理：区分不同类型的错误
          if (response.status === 401) {
            // 认证错误，不重试
            throw new Error('用户未登录或认证已过期');
          } else if (response.status === 403) {
            // 权限错误，不重试
            throw new Error('没有权限访问项目列表');
          } else if (response.status >= 500 && attempt < CONFIG.MAX_RETRIES) {
            // 服务器错误，可以重试
            throw new Error(`服务器错误 ${response.status}，将重试`);
          } else if (response.status === 429) {
            // 限流错误，延长等待时间
            const delay = Math.pow(2, attempt) * 2000; // 更长的等待时间
            console.log(`🚫 请求被限流，等待 ${delay}ms 后重试...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          throw new Error(`网络请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`✅ 项目列表获取成功 (尝试 ${attempt})`);
        return data.projects || [];
        
      } catch (error) {
        // 🧹 确保清理超时
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        lastError = error instanceof Error ? error : new Error('未知错误');
        
        // 🔍 友好的错误处理
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            lastError = new Error('请求超时，请检查网络连接');
          } else if (error.message.includes('用户未登录') || error.message.includes('没有权限')) {
            // 认证或权限错误，不重试
            break;
          }
        }
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < CONFIG.MAX_RETRIES) {
          // 🔧 优化退避策略：根据错误类型调整延迟
          const baseDelay = error instanceof Error && error.name === 'AbortError' ? 2000 : 1000;
          const delay = Math.min(baseDelay * Math.pow(1.5, attempt - 1), 5000); // 最大5秒
          console.log(`⏳ 等待 ${delay}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        break;
      }
    }
    
    // 🔧 优化错误信息：不再抛出异常，而是记录警告并返回空数组
    const errorMessage = lastError?.message || '网络请求失败';
    console.warn('⚠️ 项目列表获取失败，已用完所有重试次数:', errorMessage);
    
    // 🎯 用户友好提示：不显示技术错误，显示友好信息
    if (typeof window !== 'undefined') {
      const { toast } = await import('sonner');
      if (lastError?.message.includes('未登录') || lastError?.message.includes('权限')) {
        toast.error('请重新登录', {
          description: '登录状态已过期',
          duration: 5000
        });
      } else if (lastError?.message.includes('超时')) {
        toast.warning('网络连接较慢', {
          description: '正在重新尝试加载',
          duration: 3000
        });
      } else {
        toast.warning('项目列表加载失败', {
          description: '请稍后刷新页面重试',
          duration: 3000
        });
      }
    }
    
    // 🔧 关键修复：返回空数组而不是抛出异常，避免应用崩溃
    return [];
  }

  private async fetchProjectDetailFromNetwork(projectId: number): Promise<ProjectData | null> {
    const controller = new AbortController();
    let timeoutId: NodeJS.Timeout | null = null;
    
    try {
      // 🚀 安全的超时处理
      timeoutId = setTimeout(() => {
        if (!controller.signal.aborted) {
          controller.abort();
        }
      }, CONFIG.REQUEST_TIMEOUT);
      
      const response = await fetch(`/api/me/projects/${projectId}`, {
        credentials: 'include',
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      // 🧹 及时清理超时
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      
      if (response.status === 404) {
        return null;
      }
      
      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      return data.ok ? {
        ...data.project,
        chatHistory: data.chatHistory || [],
        versions: data.versions || []
      } : null;
    } catch (error) {
      // 🧹 确保清理超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      // 🔍 友好的错误处理
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn(`⚠️ 项目 ${projectId} 请求超时`);
          throw new Error('请求超时，请检查网络连接');
        }
        throw error;
      }
      throw new Error('网络请求失败');
    }
  }

  private async asyncPrefetchProjects(projects: ProjectItem[]): Promise<void> {
    // 使用requestIdleCallback或setTimeout确保不阻塞主线程
    const prefetch = () => {
      projects.slice(0, CONFIG.PREFETCH_COUNT).forEach(async (project, index) => {
        const cacheKey = `project_detail_${project.id}`;
        
        if (!this.memoryCache.has(cacheKey) && !await this.getFromStorage(cacheKey)) {
          // 延迟预加载，避免阻塞
          setTimeout(() => {
            this.getProjectDetail(project.id).catch(() => {
              // 静默处理预加载错误
            });
          }, index * 200 + CONFIG.PREFETCH_DELAY);
        }
      });
    };

    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as unknown as { requestIdleCallback: (callback: () => void) => void }).requestIdleCallback(prefetch);
    } else {
      setTimeout(prefetch, CONFIG.PREFETCH_DELAY);
    }
  }

  private async getFallbackData<T>(key: string): Promise<T | null> {
    try {
      const stored = localStorage.getItem(`loomrun_unified_${key}`);
      if (stored) {
        const entry: CacheEntry<T> = JSON.parse(stored);
        return entry.data;
      }
    } catch {
      // 静默处理
    }
    return null;
  }

  private isCacheExpired(entry: CacheEntry<unknown>): boolean {
    return Date.now() - entry.timestamp > entry.maxAge;
  }

  private isRequestExpired(request: RequestCache): boolean {
    return Date.now() - request.timestamp > CONFIG.REQUEST_TIMEOUT;
  }

  private evictLRUMemoryCache(): void {
    let oldestTime = Date.now();
    let oldestKey: string | null = null;
    let lowestPriority = Infinity;
    
    // 优先淘汰低优先级且最久未访问的项目
    for (const [key, entry] of this.memoryCache) {
      if (entry.priority < lowestPriority || 
          (entry.priority === lowestPriority && entry.lastAccessed < oldestTime)) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
        lowestPriority = entry.priority;
      }
    }
    
    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  private initializeCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, CONFIG.CLEANUP_INTERVAL);
  }

  private performCleanup(): void {
    // 清理过期的内存缓存
    for (const [key, entry] of this.memoryCache) {
      if (this.isCacheExpired(entry)) {
        this.memoryCache.delete(key);
      }
    }

    // 清理localStorage中的过期缓存
    if (typeof window !== 'undefined') {
      try {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key?.startsWith('loomrun_unified_')) {
            const stored = localStorage.getItem(key);
            if (stored) {
              const entry = JSON.parse(stored);
              if (this.isCacheExpired(entry)) {
                localStorage.removeItem(key);
              }
            }
          }
        }
      } catch {
        // 静默处理清理错误
      }
    }
  }

  private setupPerformanceMonitoring(): void {
    if (typeof window !== 'undefined') {
      setInterval(() => {
        const stats = this.getStats();
        console.log('📊 统一缓存性能统计:', stats);
      }, 30000);
    }
  }

  // 清理资源
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  // 🗑️ 项目删除后清理相关缓存
  async clearProjectCaches(projectId: number, userId?: number): Promise<void> {
    console.log(`🧹 清理项目 ${projectId} 相关缓存...`);
    
    // 🔥 清理内存缓存
    const keysToDelete: string[] = [];
    for (const key of this.memoryCache.keys()) {
      if (key.includes(`project_detail_${projectId}`) || 
          key.includes(`project_versions_${projectId}`) ||
          key.includes(`project_chat_${projectId}`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.memoryCache.delete(key));
    
    // 🗑️ 清理localStorage缓存
    if (typeof window !== 'undefined') {
      try {
        const keysToRemove: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key?.startsWith('loomrun_unified_') && 
              (key.includes(`project_detail_${projectId}`) ||
               key.includes(`project_versions_${projectId}`) ||
               key.includes(`project_chat_${projectId}`))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
      } catch (error) {
        console.warn('清理localStorage缓存失败:', error);
      }
    }

    // 🔄 清理项目列表缓存（强制重新获取）
    if (userId) {
      const projectListKey = `projects_${userId}`;
      this.memoryCache.delete(projectListKey);
      
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem(`loomrun_unified_${projectListKey}`);
        } catch (error) {
          console.warn('清理项目列表缓存失败:', error);
        }
      }
    }

    console.log(`✅ 项目 ${projectId} 缓存清理完成`);
  }

  // 🔄 强制刷新项目列表（用于删除后更新）
  async refreshProjectList(userId: number): Promise<ProjectItem[]> {
    return this.getProjectList(userId, true);
  }

  // 🎯 优化：智能项目保存状态管理
  setSavingStatus(projectId: number, saving: boolean): void {
    const current = this.projectSaveStatusCache.get(projectId) || { saving: false, lastSaved: 0 };
    this.projectSaveStatusCache.set(projectId, {
      saving,
      lastSaved: saving ? current.lastSaved : Date.now()
    });
    
    PerformanceLogger.log(`💾 项目${projectId}保存状态: ${saving ? '保存中' : '已保存'}`);
  }

  // 🎯 获取项目保存状态
  getSavingStatus(projectId: number): { saving: boolean; lastSaved: number } {
    return this.projectSaveStatusCache.get(projectId) || { saving: false, lastSaved: 0 };
  }

  // 🔧 智能缓存失效：只清除相关项目的缓存
  invalidateProject(projectId: number): void {
    // 清除项目详情缓存
    this.projectDetailCache.delete(projectId);
    
    // 清除聊天历史缓存
    this.chatHistoryCache.delete(projectId);
    
    // 清除项目列表缓存（因为列表中包含项目摘要信息）
    this.projectListCache = null;
    this.lastFetchTime.delete('project-list');
    
    // 清除保存状态缓存
    this.projectSaveStatusCache.delete(projectId);
    
    PerformanceLogger.log(`🧹 项目${projectId}相关缓存已清除`);
  }

  // 🎯 性能优化：批量缓存预热
  async preWarmCache(projectIds: number[]): Promise<void> {
    PerformanceLogger.startTimer('cache-prewarm');
    
    try {
      // 并行预热多个项目的缓存
      const promises = projectIds.map(async (projectId) => {
        try {
          await this.fetchProjectDetailFromNetwork(projectId);
        } catch (error) {
          PerformanceLogger.warn(`项目${projectId}缓存预热失败:`, error);
        }
      });
      
      await Promise.allSettled(promises);
      PerformanceLogger.log(`🔥 缓存预热完成: ${projectIds.length}个项目`);
    } finally {
      PerformanceLogger.endTimer('cache-prewarm');
    }
  }

  // 🧹 内存优化：定期清理过期缓存
  cleanupExpiredCache(): void {
    const now = Date.now();
    
    // 清理过期的项目详情缓存
    for (const [projectId] of this.projectDetailCache.entries()) {
      const fetchTime = this.lastFetchTime.get(`project-${projectId}`) || 0;
      if (now - fetchTime > this.CACHE_DURATION.PROJECT_DETAIL) {
        this.projectDetailCache.delete(projectId);
        this.lastFetchTime.delete(`project-${projectId}`);
      }
    }
    
    // 清理过期的保存状态缓存
    for (const [projectId, status] of this.projectSaveStatusCache.entries()) {
      if (now - status.lastSaved > this.CACHE_DURATION.SAVE_STATUS) {
        this.projectSaveStatusCache.delete(projectId);
      }
    }
    
    // 清理过期的社区项目缓存
    for (const [cacheKey] of this.communityProjectsCache.entries()) {
      const fetchTime = this.lastFetchTime.get(cacheKey) || 0;
      if (now - fetchTime > this.CACHE_DURATION.COMMUNITY_PROJECTS) {
        this.communityProjectsCache.delete(cacheKey);
        this.lastFetchTime.delete(cacheKey);
      }
    }
    
    PerformanceLogger.log('🧹 过期缓存清理完成');
  }

  // 🎯 社区项目缓存管理
  async getCommunityProjects(
    search: string = '', 
    limit: number = 20, 
    offset: number = 0, 
    userId?: number
  ): Promise<CommunityProjectsResponse | null> {
    const cacheKey = `community:${search}:${limit}:${offset}:${userId || 'guest'}`;
    const fetchTime = this.lastFetchTime.get(cacheKey) || 0;
    const now = Date.now();
    
    // 检查缓存是否有效
    if (now - fetchTime < this.CACHE_DURATION.COMMUNITY_PROJECTS) {
      const cached = this.communityProjectsCache.get(cacheKey);
      if (cached) {
        this.stats.hits++;
        return cached;
      }
    }
    
    return null;
  }

  setCommunityProjects(
    search: string = '', 
    limit: number = 20, 
    offset: number = 0, 
    userId: number | undefined,
    data: CommunityProjectsResponse
  ): void {
    const cacheKey = `community:${search}:${limit}:${offset}:${userId || 'guest'}`;
    this.communityProjectsCache.set(cacheKey, data);
    this.lastFetchTime.set(cacheKey, Date.now());
  }

  invalidateCommunityCache(): void {
    this.communityProjectsCache.clear();
    for (const key of this.lastFetchTime.keys()) {
      if (key.startsWith('community:')) {
        this.lastFetchTime.delete(key);
      }
    }
  }

  // 🎯 后台刷新项目列表
  private async backgroundRefreshProjectList(userId: number, cacheKey: string): Promise<void> {
    if (this.pendingRequests.has(cacheKey)) {
      // 如果已经有其他后台刷新在等待，则不重复启动
      return;
    }

    const refreshPromise = this.fetchProjectListFromNetworkEnhanced();
    this.pendingRequests.set(cacheKey, {
      promise: refreshPromise,
      timestamp: Date.now()
    });

    try {
      const result = await refreshPromise;
      this.pendingRequests.delete(cacheKey);

      if (result && result.length > 0) {
        this.setToMemory(cacheKey, result, CONFIG.PROJECT_LIST_TTL, 5);
        await this.setToStorage(cacheKey, result, CONFIG.PROJECT_LIST_TTL);
        this.lastFetchTime.set(cacheKey, Date.now());
      }
      console.log(`✅ 项目列表后台刷新成功: ${result?.length || 0}个项目`);
    } catch (error) {
      this.pendingRequests.delete(cacheKey);
      console.warn(`❌ 项目列表后台刷新失败:`, error);
    }
  }
}

// 🎯 单例导出
export const unifiedCache = UnifiedCache.getInstance(); 