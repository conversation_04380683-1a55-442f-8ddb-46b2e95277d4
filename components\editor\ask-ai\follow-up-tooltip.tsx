import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Info } from "lucide-react";

export const FollowUpTooltip = () => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Info className="size-3 text-neutral-400 hover:text-neutral-300 cursor-pointer transition-colors" />
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="!rounded-xl !p-3 !w-64 !bg-neutral-900 border-neutral-700/50"
      >
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <p className="text-sm font-medium text-neutral-200">连续对话模式</p>
          </div>
          <p className="text-xs text-neutral-400 leading-relaxed">
            开启后基于当前页面内容进行修改，关闭则从头生成新页面。
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
};
