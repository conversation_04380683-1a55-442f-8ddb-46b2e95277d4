import classNames from "classnames";
import { Code, XCircle } from "lucide-react";

import { Collapsible, CollapsibleTrigger } from "@/components/ui/collapsible";
import { getSafeClassName } from "@/lib/utils";

export const SelectedHtmlElement = ({
  element,
  isAiWorking = false,
  onDelete,
}: {
  element: HTMLElement | null;
  isAiWorking: boolean;
  onDelete?: () => void;
}) => {
  if (!element) return null;

  const tagName = element.tagName.toLowerCase();
  const textContent = element.textContent?.trim() || "";
  const hasText = textContent.length > 0;
  
  // 🎯 精确的元素类型识别 - 优先级排序
  const getElementType = (el: HTMLElement): string => {
    const tag = el.tagName.toLowerCase();
    // 🚨 关键修复：使用安全的className获取函数
    const className = getSafeClassName(el);
    const role = el.getAttribute('role') || '';

    // 🔥 第一优先级：明确的语义标签
    if (tag === 'button') return '按钮';
    if (tag === 'a') return '链接';
    if (tag === 'img') return '图片';
    if (tag === 'input') return '输入框';
    if (tag === 'select') return '下拉选择';
    if (tag === 'textarea') return '文本域';
    if (tag === 'form') return '表单';
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tag)) return '标题';
    if (tag === 'p') return '段落';
    if (tag === 'td' || tag === 'th') return '表格单元';
    if (tag === 'table') return '表格';
    if (tag === 'nav') return '导航';
    if (tag === 'header') return '页头';
    if (tag === 'footer') return '页脚';
    if (tag === 'main') return '主内容';
    if (tag === 'section') return '区块';
    if (tag === 'article') return '文章';
    if (tag === 'aside') return '侧边栏';

    // 🔥 第二优先级：通过role属性识别
    if (role === 'button') return '按钮';
    if (role === 'link') return '链接';
    if (role === 'textbox') return '输入框';
    if (role === 'heading') return '标题';

    // 🔥 第三优先级：通过className识别按钮样式
    const buttonPatterns = [
      /\bbtn\b/i, /\bbutton\b/i, /\bclick\b/i,
      /\bprimary\b/i, /\bsecondary\b/i, /\bsubmit\b/i,
      /\baction\b/i, /\bcta\b/i
    ];
    if (buttonPatterns.some(pattern => pattern.test(className))) {
      return '按钮';
    }

    // 🔥 第四优先级：通过事件监听器识别交互元素
    const hasClickHandler = el.onclick !== null ||
                           el.getAttribute('onclick') !== null ||
                           className.includes('cursor-pointer');
    if (hasClickHandler && hasText) {
      return '可点击元素';
    }

    // 🔥 第五优先级：通用容器和文本元素
    if (tag === 'div') {
      if (!hasText && el.children.length > 0) return '容器';
      if (hasText && el.children.length === 0) return '文本';
      if (hasText && el.children.length > 0) return '混合容器';
      return '空容器';
    }

    if (tag === 'span') {
      return hasText ? '文本' : '空标签';
    }

    // 🔥 最后：默认分类
    return hasText ? '文本元素' : '布局元素';
  };
  
  const elementType = getElementType(element);
  const previewText = textContent.length > 20 ? textContent.substring(0, 20) + '...' : textContent;
  
  return (
    <Collapsible
      className={classNames(
        // 🎯 浅色/深色模式自适应背景和边框
        "selected-element-tag element-tag-animation backdrop-blur-sm rounded-lg shadow-md max-w-max transition-all duration-300 ease-out group",
        // 深色模式样式
        "dark:bg-gradient-to-r dark:from-blue-500/15 dark:to-purple-500/15 dark:border-blue-500/40 dark:shadow-blue-500/10",
        "dark:hover:from-blue-500/25 dark:hover:to-purple-500/25 dark:hover:border-blue-400/60 dark:hover:shadow-blue-500/20",
        // 浅色模式样式
        "light:bg-gradient-to-r light:from-blue-50 light:to-purple-50 light:border-blue-300 light:shadow-blue-200/20",
        "light:hover:from-blue-100 light:hover:to-purple-100 light:hover:border-blue-400 light:hover:shadow-blue-300/30",
        // 通用样式
        "border hover:scale-102",
        {
          "cursor-pointer": !isAiWorking,
          "opacity-50 cursor-not-allowed": isAiWorking,
        }
      )}
      disabled={isAiWorking}
      onClick={() => {
        if (!isAiWorking && onDelete) {
          onDelete();
        }
      }}
    >
      <CollapsibleTrigger className="flex items-center justify-start gap-1.5 px-1.5 py-1 cursor-pointer max-w-80">
        {/* 🎯 图标容器 - 浅色/深色模式自适应 */}
        <div className={classNames(
          "rounded size-4 flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-200 flex-shrink-0",
          // 🎯 精准修正：确保图标背景在任何模式下都是蓝色渐变
          "bg-gradient-to-br from-blue-600 to-purple-600",
          // 深色模式图标背景
          "dark:bg-gradient-to-br dark:from-blue-600 dark:to-purple-600",
          // 浅色模式图标背景 - 强制优先级
          "light:!bg-gradient-to-br light:!from-blue-600 light:!to-purple-600"
        )}>
          <Code className="text-white size-2.5 drop-shadow-sm" />
        </div>

        {/* 🎯 文本内容 - 浅色/深色模式自适应 */}
        <div className="flex flex-col items-start min-w-0 flex-1">
          <p className={classNames(
            "text-xs font-medium leading-none",
            // 深色模式文字颜色
            "dark:text-white",
            // 浅色模式文字颜色
            "light:text-gray-800"
          )}>
            {elementType} ({tagName})
          </p>
          {hasText && (
            <p className={classNames(
              "text-[10px] leading-none mt-0.5 truncate max-w-full",
              // 🎯 精准修正：确保文本内容始终为蓝色
              "text-blue-600 dark:text-blue-200",
              // 额外确保优先级
              "!text-blue-600 dark:!text-blue-200"
            )}>
              {`"${previewText}"`}
            </p>
          )}
        </div>

        {/* 🎯 关闭按钮 - 浅色/深色模式自适应 */}
        <div className="opacity-50 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
          <XCircle className={classNames(
            "size-2.5 transition-colors duration-200",
            // 深色模式关闭按钮颜色
            "dark:text-blue-200 dark:hover:text-white",
            // 浅色模式关闭按钮颜色
            "light:text-blue-500 light:hover:text-blue-700"
          )} />
        </div>
      </CollapsibleTrigger>
      {/* <CollapsibleContent className="border-t border-neutral-700 pt-2 mt-2">
        <div className="text-xs text-neutral-400">
          <p>
            <span className="font-semibold">ID:</span> {element.id || "No ID"}
          </p>
          <p>
            <span className="font-semibold">Classes:</span>{" "}
            {element.className || "No classes"}
          </p>
        </div>
      </CollapsibleContent> */}
    </Collapsible>
  );
};
