import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { inviteCode } = body;

    console.log(`🔍 [测试] 收到的邀请码参数: "${inviteCode}"`);
    console.log(`🔍 [测试] 邀请码类型: ${typeof inviteCode}`);
    console.log(`🔍 [测试] 邀请码 === null: ${inviteCode === null}`);
    console.log(`🔍 [测试] 邀请码 === 'null': ${inviteCode === 'null'}`);
    console.log(`🔍 [测试] 邀请码 truthy: ${!!inviteCode}`);

    // 模拟清理逻辑
    const cleanInviteCode = inviteCode && inviteCode !== 'null' && inviteCode.trim() !== '' ? inviteCode.trim() : null;
    console.log(`🔍 [测试] 清理后的邀请码: "${cleanInviteCode}"`);

    return NextResponse.json({
      success: true,
      original: inviteCode,
      cleaned: cleanInviteCode,
      originalType: typeof inviteCode,
      cleanedType: typeof cleanInviteCode,
      isValid: !!cleanInviteCode
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
