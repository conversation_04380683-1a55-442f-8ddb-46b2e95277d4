import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getUserByToken } from '@/lib/auth-service';
import { executeQuery } from '@/lib/mysql';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const cookieStore = await cookies();
    const token = cookieStore.get('loomrun_token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 401 });
    }

    const { nickname } = await request.json();

    // 验证输入
    if (!nickname || typeof nickname !== 'string') {
      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 });
    }

    const trimmedNickname = nickname.trim();
    if (trimmedNickname.length === 0) {
      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 });
    }

    if (trimmedNickname.length > 20) {
      return NextResponse.json({ error: '用户名不能超过20个字符' }, { status: 400 });
    }

    // 检查是否与当前用户的用户名相同（允许保持原用户名）
    const currentUserResult = await executeQuery(
      'SELECT nickname FROM users WHERE id = ?',
      [user.id]
    ) as { nickname: string }[];

    if (currentUserResult.length > 0 && currentUserResult[0].nickname === trimmedNickname) {
      return NextResponse.json({ 
        available: true, 
        message: '当前用户名' 
      });
    }

    // 检查用户名是否已存在
    const result = await executeQuery(
      'SELECT id FROM users WHERE nickname = ? AND id != ?',
      [trimmedNickname, user.id]
    ) as { id: number }[];

    const isAvailable = result.length === 0;

    return NextResponse.json({
      available: isAvailable,
      message: isAvailable ? '用户名可用' : '用户名已被使用'
    });

  } catch (error) {
    console.error('检查用户名失败:', error);
    return NextResponse.json({ error: '服务器错误' }, { status: 500 });
  }
} 