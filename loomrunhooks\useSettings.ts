import { useState, useEffect } from 'react';

export type Language = 'zh' | 'en' | 'ug';
export type Theme = 'light' | 'dark';

export interface SettingsData {
  dataOptimization: boolean;
  language: Language;
  theme: Theme;
}

const SETTINGS_STORAGE_KEY = 'loomrun_settings';

const defaultSettings: SettingsData = {
  dataOptimization: true, // 默认开启数据优化
  language: 'zh',
  theme: 'dark', // 默认使用深色主题（与系统一致）
};

export function useSettings() {
  const [settings, setSettings] = useState<SettingsData>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  // 从localStorage加载设置
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        const mergedSettings = { ...defaultSettings, ...parsed };
        setSettings(mergedSettings);
        
        // 立即同步到主题和语言系统
        window.dispatchEvent(new CustomEvent('settingsLoaded', {
          detail: mergedSettings
        }));
      } else {
        // 使用默认设置并同步
        window.dispatchEvent(new CustomEvent('settingsLoaded', {
          detail: defaultSettings
        }));
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      // 错误时使用默认设置
      window.dispatchEvent(new CustomEvent('settingsLoaded', {
        detail: defaultSettings
      }));
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存设置到localStorage和服务器
  const saveSettings = async (newSettings: Partial<SettingsData>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
      
      // 同步到服务器
      try {
        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedSettings),
        });

        if (!response.ok) {
          console.warn('服务器同步失败，但本地设置已保存');
        }
      } catch (error) {
        console.warn('服务器同步失败，但本地设置已保存:', error);
      }
      
      // 触发设置变更事件，其他组件可以监听
      window.dispatchEvent(new CustomEvent('settingsChanged', {
        detail: updatedSettings
      }));
      
      return true;
    } catch (error) {
      console.error('保存设置失败:', error);
      return false;
    }
  };

  // 更新数据优化设置
  const updateDataOptimization = async (enabled: boolean) => {
    return await saveSettings({ dataOptimization: enabled });
  };

  // 更新语言设置
  const updateLanguage = async (language: Language) => {
    return await saveSettings({ language });
  };

  // 更新主题设置
  const updateTheme = async (theme: Theme) => {
    return await saveSettings({ theme });
  };

  // 重置所有设置
  const resetSettings = () => {
    try {
      setSettings(defaultSettings);
      localStorage.removeItem(SETTINGS_STORAGE_KEY);
      window.dispatchEvent(new CustomEvent('settingsChanged', {
        detail: defaultSettings
      }));
      return true;
    } catch (error) {
      console.error('重置设置失败:', error);
      return false;
    }
  };

  return {
    settings,
    isLoading,
    updateDataOptimization,
    updateLanguage,
    updateTheme,
    resetSettings,
  };
} 