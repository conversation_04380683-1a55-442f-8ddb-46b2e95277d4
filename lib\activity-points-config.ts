import { executeQuery } from './mysql';

export interface ActivityPointsConfig {
  [activityType: string]: {
    validity_days: number;
    description: string;
    enabled?: boolean;
  };
}

export const DEFAULT_ACTIVITY_CONFIG: ActivityPointsConfig = {
  invitation: {
    validity_days: 15,
    description: "邀请活动积分",
    enabled: true
  },
  registration: {
    validity_days: 30,
    description: "注册奖励积分",
    enabled: true
  },
  special_event: {
    validity_days: 7,
    description: "特殊活动积分",
    enabled: false
  },
  daily_checkin: {
    validity_days: 30,
    description: "每日签到积分",
    enabled: false
  },
  referral_bonus: {
    validity_days: 60,
    description: "推荐奖励积分",
    enabled: false
  }
};

/**
 * 获取活动积分配置
 */
export const getActivityPointsConfig = async (): Promise<ActivityPointsConfig> => {
  try {
    const configResult = await executeQuery(
      `SELECT setting_value FROM system_settings 
       WHERE setting_key = 'activity_points_config' AND is_active = 1`,
      []
    ) as { setting_value: string }[];

    if (configResult.length > 0) {
      try {
        const config = JSON.parse(configResult[0].setting_value);
        // 合并默认配置，确保所有活动类型都有配置
        return { ...DEFAULT_ACTIVITY_CONFIG, ...config };
      } catch (error) {
        console.error('解析活动积分配置失败:', error);
      }
    }

    return DEFAULT_ACTIVITY_CONFIG;
  } catch (error) {
    console.error('获取活动积分配置失败:', error);
    return DEFAULT_ACTIVITY_CONFIG;
  }
};

/**
 * 获取特定活动类型的有效期天数
 */
export const getActivityValidityDays = async (activityType: string): Promise<number> => {
  try {
    const config = await getActivityPointsConfig();
    return config[activityType]?.validity_days || 15;
  } catch (error) {
    console.error(`获取活动类型 ${activityType} 有效期失败:`, error);
    return 15; // 默认15天
  }
};

/**
 * 更新活动积分配置
 */
export const updateActivityPointsConfig = async (config: ActivityPointsConfig): Promise<boolean> => {
  try {
    await executeQuery(
      `INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category, is_active)
       VALUES ('activity_points_config', ?, 'json', '不同活动积分有效期配置', 'points', 1)
       ON DUPLICATE KEY UPDATE 
       setting_value = VALUES(setting_value),
       updated_at = NOW()`,
      [JSON.stringify(config)]
    );

    console.log('活动积分配置更新成功:', config);
    return true;
  } catch (error) {
    console.error('更新活动积分配置失败:', error);
    return false;
  }
};

/**
 * 计算活动积分的过期时间
 * 当有效期 >= 999999 天时，返回 null 表示永久有效
 */
export const calculateActivityPointsExpiry = async (activityType: string, baseDate?: Date): Promise<Date | null> => {
  const validityDays = await getActivityValidityDays(activityType);

  // 如果有效期 >= 999999 天，视为永久有效
  if (validityDays >= 999999) {
    return null;
  }

  const expiresAt = baseDate ? new Date(baseDate) : new Date();
  expiresAt.setDate(expiresAt.getDate() + validityDays);
  return expiresAt;
};

/**
 * 检查活动类型是否启用
 */
export const isActivityEnabled = async (activityType: string): Promise<boolean> => {
  try {
    const config = await getActivityPointsConfig();
    return config[activityType]?.enabled !== false; // 默认启用
  } catch (error) {
    console.error(`检查活动类型 ${activityType} 启用状态失败:`, error);
    return true; // 默认启用
  }
};

/**
 * 获取所有启用的活动类型
 */
export const getEnabledActivityTypes = async (): Promise<string[]> => {
  try {
    const config = await getActivityPointsConfig();
    return Object.keys(config).filter(activityType => 
      config[activityType]?.enabled !== false
    );
  } catch (error) {
    console.error('获取启用的活动类型失败:', error);
    return ['invitation', 'registration']; // 默认启用的活动类型
  }
};
