import { useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { useParams } from 'next/navigation';
import { useUser } from './useUser';

interface UseAutoSaveOptions {
  debounceMs?: number;
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
}

export function useAutoSave(options: UseAutoSaveOptions = {}) {
  const {
    debounceMs = 1000,
    showSuccessToast = true,
    showErrorToast = true,
  } = options;

  const { id } = useParams<{ id: string }>();
  const { user } = useUser();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isAutoSavingRef = useRef(false);

  const autoSave = useCallback(async (html: string, prompts: string[] = []) => {
    if (!user || !id || isAutoSavingRef.current) {
      return false;
    }

    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 设置新的定时器
    timeoutRef.current = setTimeout(async () => {
      try {
        isAutoSavingRef.current = true;
        console.log('🔄 自动保存中...');

        const response = await fetch(`/api/me/projects/${id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            html,
            prompts,
          }),
        });

        const data = await response.json();

        if (data.ok) {
          console.log('✅ 自动保存成功');
          if (showSuccessToast) {
            toast.success("已自动保存", {
              duration: 2000,
              position: "bottom-right",
            });
          }
          return true;
        } else {
          console.error('❌ 自动保存失败:', data.error);
          if (showErrorToast) {
            toast.error("自动保存失败: " + (data.error || "未知错误"));
          }
          return false;
        }
      } catch (error) {
        console.error('❌ 自动保存异常:', error);
        if (showErrorToast) {
          toast.error("自动保存失败，请检查网络连接");
        }
        return false;
      } finally {
        isAutoSavingRef.current = false;
      }
    }, debounceMs);

    return true;
  }, [user, id, debounceMs, showSuccessToast, showErrorToast]);

  const saveImmediately = useCallback(async (html: string, prompts: string[] = []) => {
    if (!user || !id) {
      return false;
    }

    // 清除防抖定时器，立即保存
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    try {
      isAutoSavingRef.current = true;
      console.log('💾 立即保存中...');

      const response = await fetch(`/api/me/projects/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          html,
          prompts,
        }),
      });

      const data = await response.json();

      if (data.ok) {
        console.log('✅ 立即保存成功');
        if (showSuccessToast) {
          toast.success("保存成功", {
            duration: 2000,
            position: "bottom-right",
          });
        }
        return true;
      } else {
        console.error('❌ 立即保存失败:', data.error);
        if (showErrorToast) {
          toast.error("保存失败: " + (data.error || "未知错误"));
        }
        return false;
      }
    } catch (error) {
      console.error('❌ 立即保存异常:', error);
      if (showErrorToast) {
        toast.error("保存失败，请检查网络连接");
      }
      return false;
    } finally {
      isAutoSavingRef.current = false;
    }
  }, [user, id, showSuccessToast, showErrorToast]);

  const isAutoSaving = useCallback(() => isAutoSavingRef.current, []);

  return {
    autoSave,
    saveImmediately,
    isAutoSaving,
  };
} 