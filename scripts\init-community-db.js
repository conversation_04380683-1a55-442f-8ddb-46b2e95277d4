const mysql = require('mysql2/promise');
require('dotenv').config();

// 使用与项目一致的数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+08:00',
};

async function initCommunityDatabase() {
  let connection;
  
  try {
    console.log('🔗 正在连接数据库...');
    console.log('📋 数据库配置:', {
      host: DB_CONFIG.host,
      port: DB_CONFIG.port,
      user: DB_CONFIG.user,
      database: DB_CONFIG.database,
      // 不显示密码
      password: DB_CONFIG.password ? '***' : 'undefined'
    });
    
    connection = await mysql.createConnection(DB_CONFIG);
    
    console.log('✅ 数据库连接成功');
    console.log('🚀 开始初始化社区数据表...');

    // 创建社区项目表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS community_projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_project_id INT NOT NULL,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        html_content LONGTEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (original_project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_project_share (original_project_id),
        INDEX idx_user_id (user_id),
        INDEX idx_project_id (original_project_id),
        INDEX idx_created_at (created_at),
        INDEX idx_title (title)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ 社区项目表创建成功');

    // 验证表是否创建成功
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'community_projects'"
    );
    
    if (tables.length > 0) {
      console.log('✅ 表验证成功：community_projects 表已存在');
      
      // 显示表结构
      const [columns] = await connection.execute(
        "DESCRIBE community_projects"
      );
      console.log('📋 表结构:');
      columns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
      });
    } else {
      console.log('❌ 表验证失败：community_projects 表不存在');
    }

    // 检查外键约束
    const [constraints] = await connection.execute(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME = 'community_projects' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [DB_CONFIG.database]);

    if (constraints.length > 0) {
      console.log('✅ 外键约束验证成功:');
      constraints.forEach(constraint => {
        console.log(`  - ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
      });
    }

    // 检查索引
    const [indexes] = await connection.execute(`
      SHOW INDEX FROM community_projects
    `);
    
    if (indexes.length > 0) {
      console.log('✅ 索引验证成功:');
      const indexNames = [...new Set(indexes.map(idx => idx.Key_name))];
      indexNames.forEach(indexName => {
        console.log(`  - ${indexName}`);
      });
    }

    console.log('🎉 社区数据库初始化完成！');
    console.log('📝 现在您可以：');
    console.log('  1. 在编辑器中点击"共享"按钮分享项目到社区');
    console.log('  2. 在欢迎页面查看社区项目');
    console.log('  3. 在项目栏点击"LoomRun社区"浏览所有项目');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 解决方案：');
      console.log('  1. 检查 .env 文件中的数据库连接配置');
      console.log('  2. 确认数据库用户名和密码正确');
      console.log('  3. 确认数据库服务器允许远程连接');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 解决方案：');
      console.log('  1. 检查数据库服务器是否运行');
      console.log('  2. 确认数据库主机地址和端口正确');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 解决方案：');
      console.log('  1. 确认数据库名称正确');
      console.log('  2. 检查数据库是否存在');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 检查必要的环境变量
function checkEnvironmentVariables() {
  const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ 缺少必要的环境变量:');
    missingVars.forEach(varName => {
      console.error(`  - ${varName}`);
    });
    console.log('💡 请在 .env 文件中配置这些变量');
    process.exit(1);
  }
  
  console.log('✅ 环境变量检查通过');
}

// 运行初始化
console.log('🚀 开始初始化社区数据库...');
checkEnvironmentVariables();
initCommunityDatabase(); 