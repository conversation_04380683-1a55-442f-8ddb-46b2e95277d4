/* 🚀 滚动性能优化 CSS */

/* ====== 社区卡片滚动优化 ====== */

/* 🎯 社区卡片容器优化 */
.community-cards-container {
  /* 硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 优化滚动性能 */
  scroll-behavior: auto;
  -webkit-overflow-scrolling: touch;
  
  /* 减少重绘重排 */
  contain: layout style paint;
  
  /* 优化合成层 */
  isolation: isolate;
  
  /* 减少滚动抖动 */
  scroll-snap-type: y proximity;
}

/* 🚀 社区卡片项优化 */
.community-card {
  /* 硬件加速 */
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  
  /* 避免重排 */
  contain: layout style paint;
  
  /* 优化渲染性能 */
  will-change: auto;
  
  /* 减少内存占用 */
  isolation: isolate;
  
  /* 滚动锚点 */
  scroll-snap-align: start;
  scroll-margin-top: 20px;
}

/* 🎨 社区卡片iframe优化 */
.community-card iframe {
  /* 硬件加速 */
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  
  /* 避免不必要的重排 */
  contain: size layout style paint;
  
  /* 优化渲染性能 */
  will-change: auto;
  
  /* 减少内存占用 */
  pointer-events: none;
}

/* 🎯 社区卡片悬停优化 */
.community-card:hover {
  /* 保持硬件加速但不移动 */
  transform: translate3d(0, 0, 0);
  
  /* 避免重排 */
  will-change: auto;
}

/* 🚀 社区卡片网格容器优化 */
.community-grid {
  /* 硬件加速 */
  transform: translateZ(0);
  
  /* 网格优化 */
  contain: layout style;
  
  /* 减少重绘 */
  will-change: auto;
  
  /* 优化子元素渲染 */
  isolation: isolate;
}

/* ====== 通用滚动优化 ====== */

/* 🎯 优化滚动容器 */
.scroll-optimized {
  /* 开启硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 优化滚动行为 */
  scroll-behavior: auto; /* 避免smooth带来的性能开销 */
  -webkit-overflow-scrolling: touch; /* iOS优化 */
  
  /* 减少重绘 */
  contain: layout style paint;
  
  /* 优化合成层 */
  isolation: isolate;
}

/* 🚀 iframe滚动优化 */
.iframe-scroll-optimized {
  /* 硬件加速 */
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000;
  
  /* 避免不必要的重排 */
  contain: size layout style paint;
  
  /* 优化渲染性能 */
  will-change: auto; /* 只在需要时开启 */
}

/* ====== 元素高亮优化 ====== */

/* 🎨 高性能的高亮效果 */
.element-highlight-optimized {
  /* 硬件加速 */
  transform: translateZ(0);
  will-change: transform, opacity;
  
  /* 避免重排的位置变化 */
  position: absolute;
  pointer-events: none;
  
  /* 优化边框渲染 */
  box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-radius: 4px;
  
  /* 减少重绘开销 */
  contain: layout style paint;
  
  /* 合成层优化 */
  isolation: isolate;
}

/* 🎯 移除性能杀手动画 */
.no-pulse {
  animation: none !important;
}

/* ====== 预览容器优化 ====== */

/* 🚀 预览iframe容器 */
.preview-container-optimized {
  /* 硬件加速 */
  transform: translate3d(0, 0, 0);
  
  /* 包含优化 */
  contain: size layout style;
  
  /* 避免不必要的重排 */
  overflow: hidden;
  
  /* 优化复合 */
  will-change: auto;
}

/* 🎨 设备边框优化 */
.device-frame-optimized {
  /* 硬件加速 */
  transform: translateZ(0);
  
  /* 避免重排 */
  contain: layout style paint;
  
  /* 优化边框渲染 */
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.2);
  
  /* 减少重绘 */
  will-change: auto;
}

/* ====== 代码编辑器优化 ====== */

/* 🚀 代码容器滚动优化 */
.code-scroll-optimized {
  /* 虚拟化友好 */
  transform: translateZ(0);
  contain: strict;
  
  /* 文本渲染优化 */
  text-rendering: optimizeSpeed;
  font-smooth: never;
  -webkit-font-smoothing: subpixel-antialiased;
  
  /* 避免字体度量重计算 */
  font-variant-numeric: tabular-nums;
}

/* 🎯 行号优化 */
.line-numbers-optimized {
  /* 硬件加速 */
  transform: translateZ(0);
  
  /* 包含优化 */
  contain: size layout style paint;
  
  /* 文本渲染优化 */
  text-rendering: optimizeSpeed;
  user-select: none;
  
  /* 减少重绘 */
  will-change: scroll-position;
}

/* ====== 性能关键路径优化 ====== */

/* 🚀 关键渲染路径优化 */
.critical-optimized {
  /* 最小化重排重绘 */
  contain: layout style paint;
  
  /* 硬件加速 */
  transform: translateZ(0);
  
  /* 减少合成开销 */
  will-change: auto;
  
  /* 优化字体加载 */
  font-display: swap;
}

/* 🎯 减少动画卡顿 */
.smooth-animation {
  /* 60fps动画 */
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
  
  /* 硬件加速 */
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* ====== 移动端滚动优化 ====== */

/* 📱 移动端特殊优化 */
@media (max-width: 768px) {
  .mobile-scroll-optimized {
    /* iOS弹性滚动 */
    -webkit-overflow-scrolling: touch;
    
    /* 减少渲染开销 */
    transform: translate3d(0, 0, 0);
    
    /* 避免过度绘制 */
    contain: layout style;
  }
  
  /* 🎯 移动端减少动画 */
  .reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ====== 高DPI显示器优化 ====== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hidpi-optimized {
    /* 高分辨率文本优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* 减少子像素渲染 */
    transform: translateZ(0);
  }
}

/* ====== 内存优化 ====== */

/* 🧹 减少内存占用 */
.memory-optimized {
  /* 避免保留复合层 */
  will-change: auto;
  
  /* 减少纹理内存 */
  transform: none;
  
  /* 限制包含块 */
  contain: layout style;
}

/* ====== 调试工具 ====== */

/* 🔍 性能调试模式 (仅开发环境) */
.dev-performance-debug {
  /* 显示合成层边界 */
  outline: 1px solid rgba(255, 0, 0, 0.3);
}

.dev-performance-debug::after {
  content: attr(data-performance-info);
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  pointer-events: none;
  z-index: 9999;
} 