# LoomRun 支付系统实现文档

## 🎯 系统概述

LoomRun 支付系统是一个完整的订阅和充值解决方案，支持会员订阅和积分充值两种业务模式。系统采用模块化设计，具有高度的可配置性和扩展性。

## 🏗️ 系统架构

### 核心组件
- **订单管理系统**: 处理订阅和充值订单的创建、状态管理
- **支付处理系统**: 模拟支付流程，支持支付宝、微信支付
- **积分管理系统**: 处理积分发放、消费、过期等逻辑
- **会员管理系统**: 处理会员权益、有效期管理

### 数据库设计
- `membership_orders`: 会员订阅订单表
- `recharge_orders`: 积分充值订单表
- `user_subscriptions`: 用户会员记录表
- `user_points_balance`: 用户积分余额表
- `points_transactions`: 积分交易记录表
- `system_settings`: 系统配置表

## 📋 功能特性

### ✅ 已实现功能

#### 1. 订单管理
- [x] 订阅订单创建和管理
- [x] 充值订单创建和管理
- [x] 订单状态管理 (pending/paid/cancelled/expired/refunded)
- [x] 订单自动过期机制 (30分钟可配置)
- [x] 重复下单检测和处理

#### 2. 支付流程
- [x] 弹窗式支付界面
- [x] 支付方式选择 (支付宝/微信)
- [x] 模拟支付处理
- [x] 支付成功页面
- [x] 支付状态实时同步

#### 3. 积分系统
- [x] 多类型积分管理 (activity/subscription/recharge)
- [x] 积分有效期管理
- [x] 智能积分消费优先级
- [x] 积分交易记录

#### 4. 会员系统
- [x] 会员等级管理 (Free/Pro/Max)
- [x] 会员权益发放
- [x] 会员有效期管理

#### 5. 系统配置
- [x] 灵活的参数配置
- [x] 订单过期时间配置
- [x] 支付方式配置
- [x] 定时任务配置

## 🔧 技术实现

### API 接口

#### 订单相关
```
POST /api/orders/subscription    # 创建订阅订单
POST /api/orders/recharge        # 创建充值订单
GET  /api/orders/[orderId]       # 查询订单详情
POST /api/orders/[orderId]/pay   # 处理支付
POST /api/orders/[orderId]/cancel # 取消订单
```

#### 定时任务
```
GET/POST /api/cron/expire-orders # 处理过期订单
```

### 前端组件
- `SubscriptionModal`: 主要的订阅积分弹窗组件
- `PaymentView`: 支付页面组件
- `SuccessView`: 支付成功页面组件

### 核心服务
- `order-service.ts`: 订单管理服务
- `points-service.ts`: 积分管理服务
- `auth-service.ts`: 用户认证服务

## ⚙️ 系统配置

### 环境变量
```env
# 定时任务配置
CRON_SECRET_TOKEN=loomrun-cron-secret-2024

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=loomrun
```

### 系统设置 (system_settings 表)
```sql
-- 订单过期时间（分钟）
order_expire_minutes = 30

-- 定时任务检查间隔（分钟）
order_check_interval_minutes = 5

-- 订单编号前缀
subscription_order_prefix = SUB
recharge_order_prefix = RCH

-- 模拟支付延迟（秒）
mock_payment_delay_seconds = 3

-- 支付方式配置
payment_methods_enabled = ["alipay","wechat","mock"]

-- 重复下单检测开关
duplicate_order_check_enabled = true
```

## 🚀 使用指南

### 1. 用户操作流程
1. 点击用户菜单中的"订阅积分"按钮
2. 选择订阅计划或积分套餐
3. 点击"立即支付"创建订单
4. 选择支付方式
5. 点击"模拟支付成功"完成支付
6. 查看支付成功页面
7. 积分/会员权益自动发放

### 2. 管理员操作
- 通过 system_settings 表调整系统参数
- 监控订单状态和支付情况
- 配置定时任务处理过期订单

### 3. 定时任务设置
建议设置定时任务每5分钟调用一次过期订单处理接口：
```bash
# 示例 cron 配置
*/5 * * * * curl -H "Authorization: Bearer loomrun-cron-secret-2024" \
  http://localhost:3000/api/cron/expire-orders
```

## 🔍 测试验证

### 手动测试步骤
1. 启动开发服务器: `npm run dev`
2. 登录系统
3. 点击"订阅积分"按钮
4. 测试订阅流程
5. 测试充值流程
6. 验证重复下单处理
7. 检查积分和会员权益发放

### 数据库验证
```sql
-- 检查订单记录
SELECT * FROM membership_orders ORDER BY created_at DESC LIMIT 10;
SELECT * FROM recharge_orders ORDER BY created_at DESC LIMIT 10;

-- 检查积分记录
SELECT * FROM user_points_balance WHERE user_id = ? ORDER BY created_at DESC;
SELECT * FROM points_transactions WHERE user_id = ? ORDER BY created_at DESC;

-- 检查会员记录
SELECT * FROM user_subscriptions WHERE user_id = ? ORDER BY created_at DESC;
```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接错误
- 检查数据库配置
- 确认数据库服务运行状态
- 检查连接数限制

#### 2. 订单创建失败
- 检查系统设置是否正确
- 验证用户权限
- 查看服务器日志

#### 3. 支付处理失败
- 检查订单状态
- 验证支付参数
- 查看错误日志

#### 4. 积分发放异常
- 检查积分配置
- 验证交易记录
- 查看余额表数据

## 📈 性能优化

### 数据库优化
- 已添加必要的索引
- 使用事务保证数据一致性
- 定期清理过期数据

### 缓存策略
- 系统设置缓存
- 用户积分缓存
- 订单状态缓存

## 🔮 未来扩展

### 计划功能
- [ ] 真实支付接口集成
- [ ] 订单退款功能
- [ ] 批量订单处理
- [ ] 支付统计报表
- [ ] 优惠券系统
- [ ] 分期付款支持

### 技术改进
- [ ] Redis 缓存集成
- [ ] 消息队列处理
- [ ] 微服务架构
- [ ] API 限流保护

## 📞 技术支持

如有问题或建议，请联系开发团队或提交 Issue。

---

**版本**: v1.0.0  
**更新时间**: 2025-08-01  
**开发团队**: LoomRun Team
