"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { 
  ExternalLink, 
  FileText, 
  Loader2,
  CheckCircle,
  AlertCircle,
  Figma,
  Upload,
  Code2,
  BookOpen,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';

interface FigmaIntegrationProps {
  isOpen: boolean;
  onClose: () => void;
  htmlContent?: string;
  projectId?: number;
  projectTitle?: string;
  mode?: 'import' | 'export' | 'full';
}

interface FigmaValidationResult {
  success: boolean;
  data?: {
    fileId: string;
    nodeId?: string;
    fileName: string;
    urlType: string;
    originalUrl: string;
  };
  message?: string;
  error?: string;
}

type Step = 'validate' | 'guide' | 'upload' | 'create';

export default function FigmaIntegration({ 
  isOpen, 
  onClose
}: FigmaIntegrationProps) {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [currentStep, setCurrentStep] = useState<Step>('validate');
  const [figmaUrl, setFigmaUrl] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [validationResult, setValidationResult] = useState<FigmaValidationResult | null>(null);
  const [uploadedHtml, setUploadedHtml] = useState<string>('');
  const [uploadedFileName, setUploadedFileName] = useState<string>('');

  // 步骤配置
  const steps = [
    { id: 'validate', title: '验证链接', icon: CheckCircle },
    { id: 'guide', title: '导出指南', icon: BookOpen },
    { id: 'upload', title: '上传文件', icon: Upload },
    { id: 'create', title: '创建项目', icon: Code2 }
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  // 🎯 验证 Figma URL
  const handleValidateUrl = useCallback(async () => {
    if (!figmaUrl.trim()) {
      toast.error("请输入Figma设计链接");
      return;
    }

    setIsValidating(true);
    setValidationResult(null);

    try {
      const response = await fetch('/api/figma/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: figmaUrl }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'URL验证失败');
      }

      setValidationResult({
        success: true,
        data: result.data,
        message: result.message
      });

      toast.success('✅ 链接验证成功');
      // 自动跳转到下一步
      setTimeout(() => setCurrentStep('guide'), 800);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "URL验证失败";
      setValidationResult({
        success: false,
        error: errorMessage
      });
      toast.error(errorMessage);
    } finally {
      setIsValidating(false);
    }
  }, [figmaUrl]);

  // 🎯 处理文件上传
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.html') && !file.name.endsWith('.htm')) {
      toast.error('请选择HTML文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content) {
        setUploadedHtml(content);
        setUploadedFileName(file.name);
        toast.success(`文件上传成功`);
        // 自动跳转到下一步
        setTimeout(() => setCurrentStep('create'), 500);
      }
    };
    reader.readAsText(file);
  }, []);

  // 🎯 创建项目
  const handleCreateProject = useCallback(async () => {
    if (!uploadedHtml.trim()) {
      toast.error("请先上传HTML文件");
      return;
    }

    setIsCreatingProject(true);

    try {
      const response = await fetch('/api/figma/create-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: uploadedHtml,
          figmaInfo: validationResult?.data || {
            fileName: uploadedFileName.replace(/\.(html?|htm)$/i, ''),
            originalUrl: figmaUrl
          }
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || '项目创建失败');
      }

      toast.success('🎉 项目创建成功！');
      onClose();
      router.push(`/projects/${result.project.id}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "项目创建失败";
      toast.error(errorMessage);
    } finally {
      setIsCreatingProject(false);
    }
  }, [uploadedHtml, validationResult, uploadedFileName, figmaUrl, onClose, router]);

  // 🎯 重置状态
  const handleClose = () => {
    setCurrentStep('validate');
    setValidationResult(null);
    setFigmaUrl('');
    setUploadedHtml('');
    setUploadedFileName('');
    setIsValidating(false);
    setIsCreatingProject(false);
    onClose();
  };

  // 🎯 下一步
  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1].id as Step);
    }
  };

  // 🎯 上一步
  const handlePrev = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1].id as Step);
    }
  };

  // 🎯 渲染步骤指示器
  const renderStepIndicator = () => (
    <div className="flex items-center justify-between mb-6">
      {steps.map((step, index) => {
        const Icon = step.icon;
        const isActive = step.id === currentStep;
        const isCompleted = index < currentStepIndex;


        return (
          <div key={step.id} className="flex items-center flex-1">
            <div className={`
              w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all
              ${isActive ? 'bg-purple-600 text-white' : 
                isCompleted ? 'bg-green-600 text-white' : 
                'bg-neutral-700 text-neutral-400'}
            `}>
              <Icon className="w-4 h-4" />
            </div>
            <span className={`ml-2 text-xs font-medium whitespace-nowrap ${
              isActive ? 'text-white' : 
              isCompleted ? 'text-green-400' : 
              'text-neutral-500'
            }`}>
              {step.title}
            </span>
            {index < steps.length - 1 && (
              <div className={`flex-1 h-px mx-3 ${
                isCompleted ? 'bg-green-600' : 'bg-neutral-700'
              }`} />
            )}
          </div>
        );
      })}
    </div>
  );

  // 🎯 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 'validate':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <Figma className="w-12 h-12 text-purple-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">验证 Figma 链接</h3>
              <p className="text-sm text-neutral-400">输入您的 Figma 设计链接</p>
            </div>

            <div className="space-y-3">
              <Input
                value={figmaUrl}
                onChange={(e) => setFigmaUrl(e.target.value)}
                placeholder="https://www.figma.com/design/..."
                className="bg-neutral-800 border-neutral-600 text-white"
                disabled={isValidating}
              />

              {validationResult && !validationResult.success && (
                <div className="p-3 bg-red-900/20 border border-red-700 rounded-lg">
                  <div className="flex items-center gap-2 text-red-400 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    {validationResult.error}
                  </div>
                </div>
              )}

              {validationResult && validationResult.success && (
                <div className="p-3 bg-green-900/20 border border-green-700 rounded-lg">
                  <div className="flex items-center gap-2 text-green-400 text-sm">
                    <CheckCircle className="w-4 h-4" />
                    验证成功：{validationResult.data?.fileName}
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 'guide':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <BookOpen className="w-12 h-12 text-blue-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">导出 HTML</h3>
              <p className="text-sm text-neutral-400">在 Figma 中导出您的设计</p>
            </div>

            <div className="bg-neutral-800 rounded-lg p-4">
              <ol className="text-sm text-neutral-300 space-y-2">
                <li className="flex items-start gap-2">
                  <span className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mt-0.5">1</span>
                  打开 Figma 设计文件
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mt-0.5">2</span>
                  选择要导出的框架
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mt-0.5">3</span>
                  使用 Dev Mode 或 HTML 插件导出
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mt-0.5">4</span>
                  保存为 .html 文件
                </li>
              </ol>
                </div>

            <div className="flex gap-2">
              <Button
                onClick={() => window.open(figmaUrl || 'https://www.figma.com', '_blank')}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                打开 Figma
              </Button>
                <Button
                onClick={() => window.open('https://help.figma.com/hc/en-us/articles/360040028114', '_blank')}
                variant="outline"
                className="border-neutral-600 text-neutral-300"
              >
                <BookOpen className="w-4 h-4 mr-2" />
                教程
                </Button>
              </div>
          </div>
        );

      case 'upload':
        return (
              <div className="space-y-4">
            <div className="text-center">
              <Upload className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">上传 HTML 文件</h3>
              <p className="text-sm text-neutral-400">选择从 Figma 导出的 HTML 文件</p>
                    </div>
                    
                    <div className="space-y-3">
              <input
                ref={fileInputRef}
                type="file"
                accept=".html,.htm"
                onChange={handleFileUpload}
                className="hidden"
              />
              
                        <Button
                onClick={() => fileInputRef.current?.click()}
                          variant="outline"
                className="w-full border-dashed border-neutral-600 text-neutral-300 hover:bg-neutral-800 h-20"
              >
                <div className="text-center">
                  <Upload className="w-6 h-6 mx-auto mb-2" />
                  <div>点击选择 HTML 文件</div>
                  <div className="text-xs text-neutral-500">支持 .html 和 .htm 格式</div>
                </div>
                        </Button>
                        
              {uploadedFileName && (
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                  <div className="flex items-center gap-2 text-green-400">
                    <FileText className="w-4 h-4" />
                    <span className="font-medium">{uploadedFileName}</span>
                      </div>
                  <div className="text-xs text-neutral-400 mt-1">
                    {(uploadedHtml.length / 1024).toFixed(1)} KB
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 'create':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <Code2 className="w-12 h-12 text-orange-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">创建项目</h3>
              <p className="text-sm text-neutral-400">在 LoomRun 中创建您的项目</p>
            </div>

            {uploadedFileName && (
              <div className="bg-neutral-800 rounded-lg p-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-neutral-300">文件名称</span>
                  <span className="text-white">{uploadedFileName}</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-neutral-300">文件大小</span>
                  <span className="text-white">{(uploadedHtml.length / 1024).toFixed(1)} KB</span>
                    </div>
                {validationResult?.data && (
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-neutral-300">Figma 设计</span>
                    <span className="text-white">{validationResult.data.fileName}</span>
                  </div>
                )}
              </div>
            )}

            <Button
              onClick={handleCreateProject}
              disabled={isCreatingProject || !uploadedHtml}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
            >
              {isCreatingProject ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  创建中...
                </>
              ) : (
                <>
                  <Code2 className="w-4 h-4 mr-2" />
                  创建项目
                </>
              )}
            </Button>
            </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-neutral-900 border-neutral-700">
        <DialogTitle className="text-white flex items-center gap-2">
          <Figma className="w-5 h-5 text-purple-400" />
          Figma 导入
        </DialogTitle>

        {renderStepIndicator()}
        
        <div className="mb-6">
          {renderStepContent()}
                </div>

        {/* 底部按钮 */}
        <div className="flex justify-between">
          {currentStep === 'validate' ? (
            // 验证链接页面：只显示验证按钮，居中显示
            <div className="w-full flex justify-center">
                <Button
                onClick={handleValidateUrl}
                disabled={isValidating || !figmaUrl.trim()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isValidating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    验证中...
                    </>
                  ) : (
                    <>
                    验证链接
                    <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            ) : (
            // 其他页面：显示上一步和下一步按钮
            <>
                      <Button
                variant="outline"
                onClick={handlePrev}
                disabled={currentStepIndex === 0}
                className="border-neutral-600 text-neutral-300"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                上一步
                      </Button>
                      
              <div className="flex gap-2">
                {currentStep === 'guide' && (
                      <Button
                    onClick={handleNext}
                    className="bg-blue-600 hover:bg-blue-700"
                      >
                    下一步
                    <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                )}

                {currentStep === 'upload' && uploadedHtml && (
                    <Button
                    onClick={handleNext}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    下一步
                    <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                )}
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 