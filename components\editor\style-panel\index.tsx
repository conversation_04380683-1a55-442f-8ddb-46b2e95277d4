"use client";
import { useState, useCallback, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { X, Paintbrush, Type, Save } from "lucide-react";
import { QuickStylesPanel } from "./components/QuickStylesPanel";
import { TextEditor } from "./components/TextEditor";
import { StyleManager } from "./utils/StyleManager";
import { GlobalUndoManager } from "@/lib/global-undo-manager";

// 🎯 颜色格式化工具函数 - 增强版
function normalizeColor(color: string): string {
  if (!color) return '';

  // 如果已经是十六进制格式，直接返回
  if (color.startsWith('#')) return color;

  // 🎯 关键修复：处理透明和特殊值
  if (color === 'transparent' || color === 'rgba(0, 0, 0, 0)' || color === 'initial' || color === 'inherit') {
    return ''; // 返回空字符串表示透明/默认
  }

  // 转换RGB格式为十六进制
  const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (rgbMatch) {
    const r = parseInt(rgbMatch[1]);
    const g = parseInt(rgbMatch[2]);
    const b = parseInt(rgbMatch[3]);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  // 转换RGBA格式为十六进制
  const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
  if (rgbaMatch) {
    const r = parseInt(rgbaMatch[1]);
    const g = parseInt(rgbaMatch[2]);
    const b = parseInt(rgbaMatch[3]);
    const a = parseFloat(rgbaMatch[4]);

    // 如果透明度为0，返回空字符串
    if (a === 0) {
      return '';
    }

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  return color;
}

// 🎯 新增：智能获取元素的真实原始样式
function getElementOriginalStyle(element: HTMLElement, property: string, iframeWindow?: Window): string {
  // 1. 首先检查内联样式
  const inlineValue = element.style.getPropertyValue(property);
  if (inlineValue) {
    return normalizeColor(inlineValue);
  }

  // 2. 获取计算样式
  let computedValue = '';
  if (iframeWindow) {
    try {
      const computedStyle = iframeWindow.getComputedStyle(element);
      computedValue = computedStyle.getPropertyValue(property) || '';
    } catch (error) {
      console.warn('❌ 无法获取计算样式:', error);
    }
  }

  // 3. 🎯 关键：智能分析元素的真实默认值
  if (property === 'background-color') {
    const normalizedComputed = normalizeColor(computedValue);

    // 检查CSS类，无论计算样式是什么
    const className = element.className || '';

    // 🎯 完整的Tailwind背景色映射
    if (className.includes('bg-white')) return '#ffffff';
    if (className.includes('bg-black')) return '#000000';
    if (className.includes('bg-transparent')) return '';

    // 灰色系列
    if (className.includes('bg-gray-50')) return '#f9fafb';
    if (className.includes('bg-gray-100')) return '#f3f4f6';
    if (className.includes('bg-gray-200')) return '#e5e7eb';
    if (className.includes('bg-gray-300')) return '#d1d5db';
    if (className.includes('bg-gray-400')) return '#9ca3af';
    if (className.includes('bg-gray-500')) return '#6b7280';
    if (className.includes('bg-gray-600')) return '#4b5563';
    if (className.includes('bg-gray-700')) return '#374151';
    if (className.includes('bg-gray-800')) return '#1f2937';
    if (className.includes('bg-gray-900')) return '#111827';

    // 如果有计算样式且不是透明，使用计算样式
    if (normalizedComputed) {
      return normalizedComputed;
    }

    // 没有背景类且计算样式透明，返回空字符串
    return '';
  }

  // 🎯 新增：处理文字颜色
  if (property === 'color') {
    const normalizedComputed = normalizeColor(computedValue);

    // 检查CSS类
    const className = element.className || '';

    // 🎯 完整的Tailwind文字颜色映射
    if (className.includes('text-black')) return '#000000';
    if (className.includes('text-white')) return '#ffffff';

    // 灰色文字系列
    if (className.includes('text-gray-50')) return '#f9fafb';
    if (className.includes('text-gray-100')) return '#f3f4f6';
    if (className.includes('text-gray-200')) return '#e5e7eb';
    if (className.includes('text-gray-300')) return '#d1d5db';
    if (className.includes('text-gray-400')) return '#9ca3af';
    if (className.includes('text-gray-500')) return '#6b7280';
    if (className.includes('text-gray-600')) return '#4b5563';
    if (className.includes('text-gray-700')) return '#374151';
    if (className.includes('text-gray-800')) return '#1f2937';
    if (className.includes('text-gray-900')) return '#111827';

    // 其他颜色系列
    if (className.includes('text-red-500')) return '#ef4444';
    if (className.includes('text-blue-500')) return '#3b82f6';
    if (className.includes('text-green-500')) return '#10b981';
    if (className.includes('text-yellow-500')) return '#f59e0b';
    if (className.includes('text-purple-500')) return '#8b5cf6';
    if (className.includes('text-pink-500')) return '#ec4899';

    // 如果有计算样式，使用计算样式
    if (normalizedComputed) {
      return normalizedComputed;
    }

    // 默认文字颜色（通常是黑色或继承）
    return '#000000';
  }

  return normalizeColor(computedValue);
}
import { HTMLCodeIntegrator } from "./utils/HTMLCodeIntegrator";
// import { UndoRedoToolbar } from "../undo-redo-toolbar"; // 已移除，撤销功能移到预览区域工具栏
// import { useUndoRedo } from "@/loomrunhooks/useUndoRedo"; // 已移除，撤销功能移到编辑器布局层
// import { DeleteElementPanel } from "../delete-element-panel"; // 已移除，不再显示删除提示面板

interface StylePanelProps {
  selectedElement: HTMLElement | null;
  onClose: () => void;
  onApplyStyles: (styles: CSSStyleDeclaration) => void;
  onPreviewStyles: (styles: CSSStyleDeclaration) => void;
  onSave?: (htmlContent: string, versionNumber?: number) => Promise<void>; // 🎯 修改：支持传递版本号
  onSwitchToChat?: () => void; // 新增切换到聊天模式的回调
  onHtmlChange?: (newHtml: string) => void; // 新增HTML变化回调
  currentVersionNumber?: number; // 🎯 新增：当前预览的版本号
}

// StyleManager已经处理了样式属性定义，这里不再需要

export function StylePanel({
  selectedElement,
  onClose,
  onApplyStyles,
  onPreviewStyles,
  onSave,
  onSwitchToChat,
  onHtmlChange,
  currentVersionNumber
}: StylePanelProps) {
  const [activeTab, setActiveTab] = useState<'styles' | 'text'>('styles');
  const [styleValues, setStyleValues] = useState<Record<string, string>>({});

  // 🔧 新增：批量修改状态管理
  const [pendingChanges, setPendingChanges] = useState<Record<string, string>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 🎯 防抖引用，避免RGB颜色选择时频繁更新
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 撤销/重做系统已移除，现在由编辑器布局层统一管理

  // 🎯 删除元素状态管理已移除，现在使用撤销系统处理删除操作

  // 使用StyleManager读取有效样式
  const readElementStyles = useCallback((element: HTMLElement) => {
    return StyleManager.getEffectiveStyles(element);
  }, []);

  // 初始化样式值
  useEffect(() => {
    if (selectedElement) {
      const initialValues = readElementStyles(selectedElement);
      setStyleValues(initialValues);
    } else {
      setStyleValues({});
    }
  }, [selectedElement, readElementStyles]);

  // 🧹 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // 🎯 删除事件监听已移除，现在使用撤销系统自动处理删除操作

  // 🎯 关键修复：移除删除状态判断，始终显示正常编辑面板
  // const isElementDeleted = deletedElementInfo !== null; // 已移除

  // 🎯 已移除 handleRestoreElement 函数，恢复功能已整合到 handleResetStyles 中

  // 🎯 撤销/重做功能已移除，现在由预览区域工具栏和编辑器布局层统一管理

  // 🔧 按照测试页面成功逻辑重新实现：批量修改 + 仅预览
  // 🎯 全局撤销管理器和状态
  const globalUndoManager = useRef<GlobalUndoManager>(GlobalUndoManager.getInstance());
  const [globalCanUndo, setGlobalCanUndo] = useState(false);
  const isUndoRedoOperation = useRef(false);

  // 🎯 初始化全局撤销管理器
  useEffect(() => {
    const OWNER_ID = 'StylePanel';

    // 🎯 关键修复：锁定全局撤销管理器，防止其他组件干扰
    const lockSuccess = globalUndoManager.current.lock(OWNER_ID);
    if (!lockSuccess) {
      console.error('❌ StylePanel无法锁定全局撤销管理器');
      return;
    }

    const iframe = document.querySelector('iframe') as HTMLIFrameElement;
    if (iframe && iframe.contentDocument) {
      // 设置iframe引用
      globalUndoManager.current.setIframe(iframe, OWNER_ID);

      // 🎯 关键修复：设置状态变化回调，更新UI状态
      globalUndoManager.current.setOnStateChange((canUndo, canRedo) => {
        console.log('🎯 全局撤销状态变化', { canUndo, canRedo });
        setGlobalCanUndo(canUndo);
        // 注意：新的撤销逻辑不支持重做，所以不需要setGlobalCanRedo
      });

      console.log('✅ 全局撤销管理器已初始化并锁定', { owner: OWNER_ID });
    }

    // 清理函数：解锁管理器
    return () => {
      globalUndoManager.current.unlock(OWNER_ID);
      console.log('🔓 StylePanel已解锁全局撤销管理器');
    };
  }, []); // 只在组件挂载时初始化一次

  const handleStyleChange = useCallback((key: string, value: string) => {
    // 🎯 防止撤销操作时的循环调用
    if (isUndoRedoOperation.current) {
      console.log(`⏭️ 跳过样式变化（撤销操作中）: ${key} = ${value}`);
      return;
    }

    console.log(`🎨 样式值变化: ${key} = ${value}`);

    // 1. 更新本地状态（用于UI显示）
    setStyleValues(prev => {
      if (prev[key] === value) return prev;
      return { ...prev, [key]: value };
    });

    // 2. 🎯 关键：使用全局撤销管理器，支持跨元素连续撤销
    if (selectedElement) {
      // 🔧 修正：清理RGB颜色值中的空格
      let cleanValue = value.replace(/\s+/g, '');

      // 🎯 关键修复：使用智能获取函数获取真实的原始样式
      const iframe = document.querySelector('iframe') as HTMLIFrameElement;
      const iframeWindow = iframe?.contentWindow;

      // 使用智能获取函数
      const currentValue = getElementOriginalStyle(selectedElement, key, iframeWindow || undefined);

      // 🎯 关键修复：统一颜色格式为十六进制
      if (key === 'background-color' || key === 'color') {
        cleanValue = normalizeColor(cleanValue);
      }

      console.log('🎯 获取当前样式值', {
        property: key,
        element: selectedElement.tagName,
        className: selectedElement.className,
        inlineValue: selectedElement.style.getPropertyValue(key),
        computedValue: iframeWindow ?
          iframeWindow.getComputedStyle(selectedElement).getPropertyValue(key) : 'no-iframe',
        intelligentValue: currentValue,
        normalizedCleanValue: cleanValue,
        hasIframeWindow: !!iframeWindow
      });

      // 🎯 核心：记录到全局撤销历史（新的合理逻辑）
      const changeId = globalUndoManager.current.recordElementModification(
        selectedElement,
        key,
        currentValue,
        cleanValue
      );

      // 🔧 关键修复：使用精确的CSS类而不是内联样式，避免影响其他元素
      // 先移除冲突的TailwindCSS类
      StyleManager.removeConflictingTailwindClasses(selectedElement, key, cleanValue);

      // 尝试转换为TailwindCSS类
      const tailwindClass = StyleManager.convertToTailwindClass(key, cleanValue);
      if (tailwindClass) {
        // 添加TailwindCSS类
        selectedElement.classList.add(tailwindClass);
        console.log(`🎯 应用TailwindCSS类: ${key}: ${cleanValue} → ${tailwindClass}`);
      } else {
        // 对于无法转换的属性，使用精确的CSS类选择器
        const appliedClass = StyleManager.applyStylesToCSS(selectedElement, { [key]: cleanValue });
        console.log(`📝 应用精确CSS类: ${key}: ${cleanValue} → ${appliedClass}`);
      }

      console.log('✅ 全局样式应用成功', {
        changeId,
        element: selectedElement.tagName,
        property: key,
        oldValue: currentValue,
        newValue: cleanValue,
        method: tailwindClass ? 'TailwindCSS' : 'CSS类',
        noRefresh: true
      });

      // 🎯 只在需要时保存到HTML（比如用户明确保存时）
      // 这里不立即保存，避免频繁的HTML更新

      // 🚨 关键优化：对颜色选择器使用防抖，减少频繁更新
      const isColorProperty = key.includes('color') || key.includes('Color');

      if (isColorProperty && debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      const updatePreview = () => {
        requestAnimationFrame(() => {
          onPreviewStyles(selectedElement.style);
        });

        console.log(`👀 预览样式已应用: ${key} = ${cleanValue}`, {
          element: selectedElement.tagName,
          previewOnly: true,
          pendingChanges: Object.keys(pendingChanges).length + 1
        });
      };

      if (isColorProperty) {
        // 颜色属性使用防抖，减少RGB选择时的频繁更新
        debounceTimeoutRef.current = setTimeout(updatePreview, 50);
      } else {
        // 非颜色属性立即更新
        updatePreview();
      }
    }
  }, [selectedElement, onPreviewStyles, onApplyStyles, pendingChanges]);

  // 🔧 按照测试页面成功逻辑：批量应用样式（避免HTMLCodeIntegrator错误）
  const applyPendingChanges = useCallback(() => {
    if (!selectedElement || Object.keys(pendingChanges).length === 0) {
      console.log('⚠️ 没有待应用的样式变更');
      return;
    }

    console.log('🔄 开始批量应用样式变更...', {
      element: selectedElement.tagName,
      changes: pendingChanges,
      changeCount: Object.keys(pendingChanges).length
    });

    try {
      // 1. 🔧 关键：智能清理冲突类，然后应用新的TailwindCSS类
      Object.entries(pendingChanges).forEach(([property, value]) => {
        // 清理RGB颜色值中的空格
        const cleanValue = value.replace(/\s+/g, '');

        // 🔧 新增：智能移除冲突的TailwindCSS类
        StyleManager.removeConflictingTailwindClasses(selectedElement, property, cleanValue);

        // 转换为TailwindCSS类
        const tailwindClass = StyleManager.convertToTailwindClass(property, cleanValue);
        if (tailwindClass) {
          // 添加新的TailwindCSS类
          selectedElement.classList.add(tailwindClass);

          // 移除内联样式
          selectedElement.style.removeProperty(property);

          console.log(`🎯 应用TailwindCSS类: ${property}: ${cleanValue} → ${tailwindClass}`);
        } else {
          // 对于无法转换的属性，使用CSS类
          const appliedClass = StyleManager.applyStylesToCSS(selectedElement, { [property]: cleanValue });
          console.log(`📝 应用CSS类: ${property}: ${cleanValue} → ${appliedClass}`);
        }
      });

      // 2. 🔧 安全的HTML同步：只在必要时调用
      if (onHtmlChange) {
        // 延迟调用，确保DOM更新完成
        setTimeout(() => {
          try {
            const currentHTML = HTMLCodeIntegrator.extractCurrentHTML();
            onHtmlChange(currentHTML);
            console.log('🔄 HTML已安全同步');
          } catch (error) {
            console.warn('⚠️ HTML同步跳过（避免错误）:', error);
          }
        }, 100);
      }

      // 3. 清理状态
      setPendingChanges({});
      setHasUnsavedChanges(false);

      console.log('✅ 批量样式应用完成', {
        element: selectedElement.tagName,
        finalClasses: selectedElement.className,
        changeCount: Object.keys(pendingChanges).length
      });

    } catch (error) {
      console.error('❌ 批量应用样式时发生错误:', error);
      // 发生错误时也要清理状态
      setPendingChanges({});
      setHasUnsavedChanges(false);
    }
  }, [selectedElement, pendingChanges, onHtmlChange]);

  // 🔧 按照测试页面逻辑：简单有效的取消功能
  const cancelPendingChanges = useCallback(() => {
    if (!selectedElement) return;

    console.log('❌ 取消未保存的样式变更');

    try {
      // 1. 清理临时内联样式（恢复到修改前状态）
      Object.keys(pendingChanges).forEach(key => {
        selectedElement.style.removeProperty(key);
      });

      // 2. 恢复到原始样式值
      const originalValues = StyleManager.getEffectiveStyles(selectedElement);
      setStyleValues(originalValues);

      // 3. 清理状态
      setPendingChanges({});
      setHasUnsavedChanges(false);

      // 4. 触发预览更新（不刷新iframe）
      requestAnimationFrame(() => {
        onPreviewStyles(selectedElement.style);
      });

      console.log('✅ 样式变更已取消');

    } catch (error) {
      console.error('❌ 取消样式变更时发生错误:', error);
      // 发生错误时也要清理状态
      setPendingChanges({});
      setHasUnsavedChanges(false);
    }
  }, [selectedElement, pendingChanges, onPreviewStyles]);



  // 🎯 已移除 handleResetStyles 函数，现在使用撤销/重做系统

  // 保存到数据库的处理函数
  const handleSaveToDatabase = useCallback(async () => {
    if (!selectedElement || isSaving) return;

    try {
      setIsSaving(true);
      console.log('🔄 开始保存样式修改到数据库...');
      
      // 1. 🎯 关键：确保全局撤销管理器的所有变化都已应用
      if (selectedElement) {
        // 获取全局撤销状态
        const globalState = globalUndoManager.current.getState();
        console.log('🎯 全局撤销状态', {
          modifiedElementsCount: globalState.modifiedElements.length
        });

        // 应用当前样式到CSS类
        if (Object.keys(styleValues).length > 0) {
          StyleManager.applyStylesToCSS(selectedElement, styleValues);
          // 清理内联样式，只保留CSS类
          StyleManager.cleanInlineStyles(selectedElement, Object.keys(styleValues));
          console.log('✅ 全局撤销样式已应用到CSS类，内联样式已清理');
        }
      }

      // 🎯 关键修复：保存前清理选中元素的高亮状态
      const iframe = document.querySelector('iframe');
      if (iframe && iframe.contentDocument) {
        const iframeDocument = iframe.contentDocument;
        // 清理所有选中元素的高亮类
        iframeDocument.querySelectorAll('.selected-element-highlight').forEach(el => {
          el.classList.remove('selected-element-highlight');
        });
        console.log('🧹 保存前已清理选中元素高亮状态');
      }
      
      // 2. 提取完整的HTML内容（包含样式修改）
      let currentHTML = HTMLCodeIntegrator.extractCurrentHTML();
      console.log('🔄 HTML内容已提取', { htmlLength: currentHTML.length });
      
      // 🔧 新增：最终保存前进行彻底的Tailwind清理
      currentHTML = StyleManager.cleanTailwindDuplicates(currentHTML);
      console.log('🧹 HTML内容已清理Tailwind重复内容', { htmlLength: currentHTML.length });
      
      // 3. 关键修复：同步HTML状态到编辑器
      if (onHtmlChange) {
        onHtmlChange(currentHTML);
        console.log('🔄 HTML状态已同步到编辑器');
      }
      
      // 4. 保存到数据库
      if (onSave) {
        console.log('💾 开始保存到数据库...', {
          currentVersionNumber,
          htmlLength: currentHTML.length
        });
        await onSave(currentHTML, currentVersionNumber);
        console.log('✅ 样式修改已成功保存到数据库');
        
        // 5. 显示成功提示
        console.log('✅ 样式修改已成功保存到数据库');

        // 6. 保存成功后切换到聊天模式
        if (onSwitchToChat) {
          console.log('🔄 切换到聊天模式...');
          // 延迟一点点时间，让用户看到保存成功的反馈
          setTimeout(() => {
            onSwitchToChat();
          }, 200);
        }

        // 7. 关闭样式面板
        onClose();
      }
    } catch (error) {
      console.error('❌ 保存样式时发生错误:', error);
      alert('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  }, [selectedElement, styleValues, onSave, onSwitchToChat, onClose, onHtmlChange, currentVersionNumber, isSaving]);

  if (!selectedElement) {
    return null;
  }

  return (
    <div className="h-full bg-background border-r border-border flex flex-col shadow-lg">
      {/* 头部信息栏 - 浅色模式优化 */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-border bg-card/50">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
            <Paintbrush className="w-4 h-4 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-semibold text-foreground truncate">
              {(() => {
                const tagName = selectedElement.tagName.toLowerCase();
                const className = selectedElement.className || '';
                const role = selectedElement.getAttribute('role') || '';
                const textContent = selectedElement.textContent?.trim();
                const hasChildren = selectedElement.children.length > 0;

                // 🎯 精确的元素类型识别和显示
                // 第一优先级：明确的语义标签
                if (tagName === 'button') {
                  return textContent ? `按钮（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '按钮';
                }
                if (tagName === 'a') {
                  return textContent ? `链接（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '链接';
                }
                if (tagName === 'img') return '图片';
                if (tagName === 'input') return '输入框';
                if (tagName === 'select') return '下拉选择';
                if (tagName === 'textarea') return '文本域';
                if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
                  return textContent ? `${tagName.toUpperCase()}标题（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : `${tagName.toUpperCase()}标题`;
                }
                if (tagName === 'p') {
                  return textContent ? `段落（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '段落';
                }
                if (tagName === 'nav') return '导航';
                if (tagName === 'header') return '页头';
                if (tagName === 'footer') return '页脚';
                if (tagName === 'main') return '主内容';

                // 第二优先级：通过role属性识别
                if (role === 'button') {
                  return textContent ? `按钮（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '按钮';
                }
                if (role === 'link') {
                  return textContent ? `链接（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '链接';
                }

                // 第三优先级：通过className识别按钮样式
                const buttonPatterns = [
                  /\bbtn\b/i, /\bbutton\b/i, /\bclick\b/i,
                  /\bprimary\b/i, /\bsecondary\b/i, /\bsubmit\b/i
                ];
                if (buttonPatterns.some(pattern => pattern.test(className))) {
                  return textContent ? `按钮（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '按钮';
                }

                // 第四优先级：div和span的精确分类
                if (tagName === 'div') {
                  if (!textContent && hasChildren) return '容器';
                  if (textContent && !hasChildren) return `文本（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）`;
                  if (textContent && hasChildren) return `混合容器（${textContent.substring(0, 8)}...）`;
                  return '空容器';
                }

                if (tagName === 'span') {
                  return textContent ? `文本（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）` : '空标签';
                }

                // 默认显示
                if (textContent && textContent.length > 0) {
                  return `文本（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）`;
                }
                return `${tagName.toUpperCase()} 元素`;
              })()}
            </div>
            <div className="text-xs text-muted-foreground">
              样式编辑器
            </div>
          </div>
        </div>

        {/* 🔧 批量操作按钮 */}
        <div className="flex items-center space-x-2">
          {hasUnsavedChanges && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={cancelPendingChanges}
                className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground border-border"
              >
                取消
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={applyPendingChanges}
                className="h-8 px-3 text-xs bg-primary text-primary-foreground hover:bg-primary/90"
              >
                应用 ({Object.keys(pendingChanges).length})
              </Button>
            </>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 标签页切换 - 浅色模式优化 */}
      <div className="flex bg-muted/30 border-b border-border">
        {[
          { key: 'styles', label: '快捷样式', icon: Paintbrush },
          { key: 'text', label: '文本编辑', icon: Type }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as 'styles' | 'text')}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium transition-all duration-200 ${
              activeTab === key
                ? 'text-primary bg-background border-b-2 border-primary shadow-sm'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* 内容面板 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          {/* 🎯 关键修复：始终显示正常编辑面板，不显示删除提示面板 */}
          {activeTab === 'styles' ? (
            <QuickStylesPanel
              selectedElement={selectedElement}
              styleValues={styleValues}
              onStyleChange={handleStyleChange}
            />
          ) : (
            <TextEditor
              selectedElement={selectedElement}
              onPreviewStyles={onPreviewStyles}
            />
          )}
        </div>
      </div>

      {/* 底部操作栏 - 只保留数据库保存功能 */}
      <div className="p-4 border-t border-border bg-card/50">
        <div className="flex space-x-2">
          <Button
            onClick={handleSaveToDatabase}
            disabled={isSaving}
            className="flex-1 h-9 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:opacity-50 text-white text-sm font-medium transition-all duration-200 shadow-sm"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                上传保存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                上传保存到云端
              </>
            )}
          </Button>
          <Button
            onClick={() => {
              // 🎯 设置撤销操作标志，防止循环调用
              isUndoRedoOperation.current = true;

              try {
                // 使用全局撤销管理器的撤销功能
                const success = globalUndoManager.current.undo();
                if (success) {
                  console.log('✅ 全局撤销成功');

                  // 显示撤销历史摘要（调试用）
                  const summary = globalUndoManager.current.getHistorySummary();
                  console.log('🎯 撤销后历史摘要:', summary);
                } else {
                  console.log('❌ 全局撤销失败');
                }
              } finally {
                // 重置标志
                setTimeout(() => {
                  isUndoRedoOperation.current = false;
                }, 100);
              }
            }}
            disabled={!globalCanUndo}
            variant="outline"
            className="flex-1 h-9 border-border text-muted-foreground hover:bg-muted hover:text-foreground text-sm font-medium transition-all duration-200 disabled:opacity-50"
            title="撤销上一步操作 (Ctrl+Z)"
          >
            撤销
          </Button>
        </div>
      </div>
    </div>
  );
}