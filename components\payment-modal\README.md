# PaymentModal 组件

## 概述

`PaymentModal` 是一个独立的支付弹窗组件，从原来的 `SubscriptionModal` 中拆分出来，专门负责处理支付流程。这样的设计提高了代码的可维护性和复用性。

## 组件结构

```
components/payment-modal/
├── index.tsx          # 主要的支付弹窗组件
└── README.md         # 组件文档
```

## 功能特性

### ✅ 支付流程管理
- 支付页面展示
- 支付成功页面展示
- 支付方式选择（支付宝/微信）
- 模拟支付处理

### ✅ 订单信息展示
- 订单编号、金额、商品信息
- 积分信息（订阅积分/充值积分/赠送积分）
- 订单类型区分（subscription/recharge）

### ✅ 重复订单处理
- 未完成订单提醒对话框
- 继续支付/重新下单选择
- 自动取消旧订单逻辑

### ✅ 用户体验优化
- 响应式设计
- 加载状态显示
- 错误处理
- 成功动画效果

## 接口定义

### PaymentData
```typescript
interface PaymentData {
  orderId: string;           // 订单ID
  orderNo: string;           // 订单编号
  orderType: 'subscription' | 'recharge';  // 订单类型
  amount: number;            // 支付金额
  planKey?: string;          // 订阅计划key
  planName?: string;         // 订阅计划名称
  pointsIncluded?: number;   // 包含积分数量
  packageKey?: string;       // 积分套餐key
  packageName?: string;      // 积分套餐名称
  pointsAmount?: number;     // 充值积分数量
  bonusPoints?: number;      // 赠送积分数量
}
```

### PendingOrderData
```typescript
interface PendingOrderData {
  orderId: string;           // 订单ID
  orderNo: string;           // 订单编号
  orderType: 'subscription' | 'recharge';  // 订单类型
  amount: number;            // 订单金额
  createdAt: string;         // 创建时间
}
```

### PaymentModalProps
```typescript
interface PaymentModalProps {
  isOpen: boolean;                          // 是否显示弹窗
  onClose: () => void;                      // 关闭弹窗回调
  paymentData: PaymentData | null;          // 支付数据
  pendingOrderData?: PendingOrderData | null;  // 未完成订单数据
  showPendingOrderDialog?: boolean;         // 是否显示重复订单对话框
  onPendingOrderContinue?: (orderId: string) => void;  // 继续支付回调
  onPendingOrderReorder?: () => void;       // 重新下单回调
  onPaymentSuccess?: () => void;            // 支付成功回调
}
```

## 使用示例

```tsx
import PaymentModal, { PaymentData, PendingOrderData } from '../payment-modal';

function MyComponent() {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);

  const handlePayment = (data: PaymentData) => {
    setPaymentData(data);
    setShowPaymentModal(true);
  };

  return (
    <>
      {/* 其他组件内容 */}
      
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => {
          setShowPaymentModal(false);
          setPaymentData(null);
        }}
        paymentData={paymentData}
        onPaymentSuccess={() => {
          // 支付成功后的处理
          window.location.reload();
        }}
      />
    </>
  );
}
```

## 内部组件

### PaymentView
负责显示支付页面，包括：
- 订单信息展示
- 支付方式选择
- 二维码展示
- 模拟支付按钮

### SuccessView
负责显示支付成功页面，包括：
- 成功动画
- 订单完成信息
- 权益说明
- 完成按钮

### PendingOrderDialog
负责处理重复订单情况，包括：
- 未完成订单信息展示
- 继续支付/重新下单选择
- 订单取消逻辑

## 样式特点

- 使用 Tailwind CSS 进行样式设计
- 支持深色模式
- 响应式布局
- 流畅的动画效果
- 一致的设计语言

## 与 SubscriptionModal 的集成

`PaymentModal` 与 `SubscriptionModal` 通过以下方式集成：

1. **数据传递**: `SubscriptionModal` 创建订单后，将订单数据传递给 `PaymentModal`
2. **状态管理**: 两个组件独立管理各自的状态
3. **回调处理**: 通过回调函数处理支付成功、取消等事件
4. **错误处理**: 统一的错误处理机制

## 优势

### 🎯 代码组织
- 单一职责原则：每个组件专注于特定功能
- 代码复用：支付组件可以在其他地方复用
- 易于维护：独立的组件更容易调试和修改

### 🚀 性能优化
- 按需加载：只有在需要支付时才渲染支付组件
- 状态隔离：避免不必要的重新渲染
- 内存优化：组件卸载时自动清理状态

### 🛠️ 开发体验
- 清晰的接口定义
- 完整的类型支持
- 详细的文档说明
- 易于测试和调试

## 未来扩展

- [ ] 支持更多支付方式
- [ ] 添加支付进度指示器
- [ ] 支持分期付款
- [ ] 添加支付历史记录
- [ ] 集成真实支付接口

---

**版本**: v1.0.0  
**更新时间**: 2025-08-01  
**维护者**: LoomRun Team
