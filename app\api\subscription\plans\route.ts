import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';
import { getBooleanSetting } from '@/lib/points-service';

export async function GET(request: NextRequest) {
  try {
    // 检查订阅服务是否启用
    const subscriptionEnabled = await getBooleanSetting('subscription_service_enabled', false);

    if (!subscriptionEnabled) {
      return NextResponse.json({
        success: true,
        data: {
          enabled: false,
          plans: []
        }
      });
    }

    // 🔧 修复：获取各个计划类型的开关状态
    const [freeEnabled, proEnabled, maxEnabled] = await Promise.all([
      getBooleanSetting('free_plan_enabled', false),
      getBooleanSetting('pro_plan_enabled', false),
      getBooleanSetting('max_plan_enabled', false)
    ]);

    // 获取所有激活的订阅计划
    const allPlans = await executeQuery(
      `SELECT
        id,
        plan_key,
        plan_type,
        plan_name,
        duration_months,
        original_price,
        discount_price,
        points_included,
        points_validity_days,
        features,
        display_order
       FROM subscription_plans
       WHERE is_active = 1
       ORDER BY display_order ASC, plan_type ASC`,
      []
    ) as any[];

    // 🔧 修复：根据系统设置过滤计划
    const filteredPlans = allPlans.filter(plan => {
      switch (plan.plan_type) {
        case 'free':
          return freeEnabled;
        case 'pro':
          return proEnabled;
        case 'max':
          return maxEnabled;
        default:
          return false;
      }
    });

    // 🔧 优化：按计划类型分组，合并月度和年度计划
    const planGroups: Record<string, any> = {};

    filteredPlans.forEach(plan => {
      const planType = plan.plan_type;

      if (!planGroups[planType]) {
        planGroups[planType] = {
          plan_type: planType,
          plan_name: plan.plan_name.replace(/月度|年度/, '').trim(), // 移除月度/年度后缀
          points_included: plan.points_included,
          points_validity_days: plan.points_validity_days,
          features: typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features,
          display_order: plan.display_order,
          monthly: null,
          yearly: null
        };
      }

      // 根据时长分类
      if (plan.duration_months === 1) {
        planGroups[planType].monthly = {
          plan_key: plan.plan_key,
          duration_months: plan.duration_months,
          original_price: plan.original_price,
          discount_price: plan.discount_price,
          savings: plan.original_price - plan.discount_price,
          discount_percent: plan.original_price > 0 ?
            Math.round((1 - plan.discount_price / plan.original_price) * 100) : 0
        };
      } else if (plan.duration_months === 12) {
        planGroups[planType].yearly = {
          plan_key: plan.plan_key,
          duration_months: plan.duration_months,
          original_price: plan.original_price,
          discount_price: plan.discount_price,
          monthly_price: (plan.discount_price / 12).toFixed(2),
          savings: plan.original_price - plan.discount_price,
          discount_percent: plan.original_price > 0 ?
            Math.round((1 - plan.discount_price / plan.original_price) * 100) : 0
        };
      }
    });

    // 转换为数组并排序
    const plans = Object.values(planGroups).sort((a: any, b: any) => a.display_order - b.display_order);

    return NextResponse.json({
      success: true,
      data: {
        enabled: true,
        plans: plans
      }
    });

  } catch (error) {
    console.error('获取订阅计划失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
