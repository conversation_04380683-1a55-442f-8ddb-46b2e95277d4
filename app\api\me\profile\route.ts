import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const { nickname, avatar_url } = await request.json();
    
    // 验证输入
    if (!nickname || nickname.trim().length === 0) {
      return NextResponse.json({ error: "用户名不能为空" }, { status: 400 });
    }

    if (nickname.trim().length > 20) {
      return NextResponse.json({ error: "用户名不能超过20个字符" }, { status: 400 });
    }

    // 检查用户名是否已被其他用户使用
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE nickname = ? AND id != ?',
      [nickname.trim(), user.id]
    ) as { id: number }[];

    if (existingUser.length > 0) {
      return NextResponse.json({ error: "用户名已被使用" }, { status: 400 });
    }

    // 更新用户信息
    await executeQuery(
      'UPDATE users SET nickname = ?, avatar_url = ?, updated_at = NOW() WHERE id = ?',
      [nickname.trim(), avatar_url || null, user.id]
    );

    return NextResponse.json({ 
      success: true, 
      message: "用户信息更新成功" 
    });

  } catch (error) {
    console.error("更新用户信息失败:", error);
    return NextResponse.json({ error: "更新失败" }, { status: 500 });
  }
} 