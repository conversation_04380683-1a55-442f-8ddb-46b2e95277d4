import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取积分来源统计
    const earnStats = await executeQuery(
      `SELECT 
        source_type,
        points_type,
        COUNT(*) as transaction_count,
        SUM(points_amount) as total_points
       FROM points_transactions 
       WHERE user_id = ? AND transaction_type = 'earn'
       GROUP BY source_type, points_type
       ORDER BY total_points DESC`,
      [user.id]
    );

    // 获取积分消费统计
    const spendStats = await executeQuery(
      `SELECT 
        source_type,
        points_type,
        COUNT(*) as transaction_count,
        SUM(points_amount) as total_points
       FROM points_transactions 
       WHERE user_id = ? AND transaction_type = 'spend'
       GROUP BY source_type, points_type
       ORDER BY total_points DESC`,
      [user.id]
    );

    // 获取月度积分变化
    const monthlyStats = await executeQuery(
      `SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        transaction_type,
        SUM(points_amount) as total_points,
        COUNT(*) as transaction_count
       FROM points_transactions 
       WHERE user_id = ? 
         AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
       GROUP BY DATE_FORMAT(created_at, '%Y-%m'), transaction_type
       ORDER BY month DESC`,
      [user.id]
    );

    // 获取最近7天的积分变化
    const weeklyStats = await executeQuery(
      `SELECT 
        DATE(created_at) as date,
        transaction_type,
        SUM(points_amount) as total_points,
        COUNT(*) as transaction_count
       FROM points_transactions 
       WHERE user_id = ? 
         AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
       GROUP BY DATE(created_at), transaction_type
       ORDER BY date DESC`,
      [user.id]
    );

    return NextResponse.json({
      success: true,
      data: {
        earnStats,
        spendStats,
        monthlyStats,
        weeklyStats,
        summary: {
          currentPoints: user.points || 0,
          totalEarned: user.total_earned_points || 0,
          totalSpent: user.total_spent_points || 0
        }
      }
    });

  } catch (error) {
    console.error("获取积分统计失败:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}
