import { useState, useCallback } from 'react';
import { ChatMessage, ProjectManager } from '@/lib/project-manager';
import { flushSync } from 'react-dom';

export const useChatHistory = () => {
  const [chatHistory, setChatHistoryState] = useState<ChatMessage[]>([]);
  const [isLoadingChat, setIsLoadingChat] = useState(false);
  const [renderKey, setRenderKey] = useState(0);
  const projectManager = ProjectManager.getInstance();

  // 🔧 调试：包装setChatHistory函数
  const setChatHistory = useCallback((newChatHistory: ChatMessage[] | ((prev: ChatMessage[]) => ChatMessage[])) => {
    if (typeof newChatHistory === 'function') {
      setChatHistoryState(prev => {
        const result = newChatHistory(prev);
        console.log('🔧 useChatHistory: setChatHistory函数调用', {
          action: 'function_update',
          prevLength: prev.length,
          newLength: result.length,
          changes: {
            added: result.length - prev.length,
            prevMessages: prev.map(msg => ({ id: msg.id, type: msg.type, isGenerating: msg.isGenerating })),
            newMessages: result.map(msg => ({ id: msg.id, type: msg.type, isGenerating: msg.isGenerating }))
          },
          timestamp: new Date().toISOString()
        });
        return result;
      });
    } else {
      console.log('🔧 useChatHistory: setChatHistory直接调用', {
        action: 'direct_update',
        newLength: newChatHistory.length,
        messages: newChatHistory.map(msg => ({ 
          id: msg.id, 
          type: msg.type, 
          isGenerating: msg.isGenerating,
          hasContent: !!msg.content,
          hasHtmlContent: !!msg.htmlContent 
        })),
        timestamp: new Date().toISOString()
      });
      setChatHistoryState(newChatHistory);
    }
  }, []);

  // 🔧 加载项目的聊天历史
  const loadChatHistory = useCallback(async (projectId: number) => {
    console.log('🔄 useChatHistory: 开始加载项目聊天历史', { projectId });
    
    setIsLoadingChat(true);
    setChatHistory([]);
    
    try {
      // 🔧 关键修复：直接从API加载聊天历史，绕过缓存问题
      console.log('🌐 useChatHistory: 直接从API加载聊天历史');
      const response = await fetch(`/api/me/projects/${projectId}/chat`);
      const data = await response.json();
      
      if (data.ok && data.chatHistory && data.chatHistory.length > 0) {
        const transformedHistory = projectManager.transformChatHistory(data.chatHistory);
        setChatHistory(transformedHistory);
        console.log('✅ 聊天历史加载完成:', {
          totalMessages: transformedHistory.length,
          messageTypes: transformedHistory.map(msg => msg.type),
          messageDetails: transformedHistory.map(msg => ({
            id: msg.id,
            type: msg.type,
            hasContent: !!msg.content,
            hasHtml: !!msg.htmlContent
          }))
        });
      } else {
        setChatHistory([]);
        console.log('ℹ️ 该项目暂无聊天历史', {
          apiResponse: data.ok,
          chatHistoryLength: data.chatHistory?.length || 0
        });
      }
    } catch (error) {
      console.error('❌ 加载聊天历史失败:', error);
      setChatHistory([]);
    } finally {
      setIsLoadingChat(false);
    }
  }, [projectManager]);

  // 🔧 简化：添加用户消息
  const addUserMessage = useCallback((content: string, images?: string[]) => {
    const userMessage: ChatMessage = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-user`,
      type: 'user',
      content,
      timestamp: new Date(),
      images: images,
    };
    
    setChatHistory(prev => [...prev, userMessage]);
    return userMessage;
  }, []);

  // 🔧 简化：添加AI消息
  const addAIMessage = useCallback((content: string = '') => {
    const aiMessage: ChatMessage = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-ai`,
      type: 'ai',
      content,
      timestamp: new Date(),
      isGenerating: true,
    };
    
    setChatHistory(prev => [...prev, aiMessage]);
    return aiMessage;
  }, []);

  // 🔧 更新最后一条AI消息
  const updateLastAIMessage = useCallback((content?: string, htmlContent?: string, isGenerating?: boolean) => {
    setChatHistory(prev => {
      const updated = [...prev];
      const lastMessage = updated[updated.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        if (content !== undefined) lastMessage.content = content;
        if (htmlContent !== undefined) lastMessage.htmlContent = htmlContent;
        if (isGenerating !== undefined) lastMessage.isGenerating = isGenerating;
      }
      return updated;
    });
  }, []);

  // 🔧 标记AI生成完成
  const completeAIGeneration = useCallback((htmlContent: string) => {
    console.log('🎯 useChatHistory: 标记AI生成完成', { htmlContentLength: htmlContent.length });
    
    // 🔧 关键修复：使用flushSync强制同步更新，确保状态立即生效
    flushSync(() => {
      setChatHistory(prev => {
        console.log('🔧 useChatHistory: completeAIGeneration - 更新前状态', {
          totalMessages: prev.length,
          messages: prev.map(msg => ({
            id: msg.id,
            type: msg.type,
            isGenerating: msg.isGenerating,
            hasContent: !!msg.content,
            hasHtmlContent: !!msg.htmlContent
          }))
        });
        
        const updated = [...prev];
        const lastMessage = updated[updated.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
          console.log('✅ useChatHistory: 找到正在生成的AI消息，更新为完成状态', { 
            messageId: lastMessage.id,
            beforeUpdate: {
              isGenerating: lastMessage.isGenerating,
              content: lastMessage.content,
              hasHtmlContent: !!lastMessage.htmlContent
            }
          });
          lastMessage.isGenerating = false;
          lastMessage.htmlContent = htmlContent;
          // 🔧 关键修复：清空content，因为完成后应该显示htmlContent而不是生成中的文本
          lastMessage.content = '';
          console.log('🔧 useChatHistory: AI消息状态已更新', {
            messageId: lastMessage.id,
            afterUpdate: {
              isGenerating: lastMessage.isGenerating,
              content: lastMessage.content,
              hasHtmlContent: !!lastMessage.htmlContent,
              htmlContentLength: lastMessage.htmlContent?.length || 0
            }
          });
        } else {
          console.log('⚠️ useChatHistory: 未找到正在生成的AI消息', {
            lastMessageType: lastMessage?.type,
            isGenerating: lastMessage?.isGenerating,
            totalMessages: updated.length,
            allMessages: updated.map(msg => ({
              id: msg.id,
              type: msg.type,
              isGenerating: msg.isGenerating
            }))
          });
        }
        
        console.log('🔧 useChatHistory: completeAIGeneration - 更新后状态', {
          totalMessages: updated.length,
          messages: updated.map(msg => ({
            id: msg.id,
            type: msg.type,
            isGenerating: msg.isGenerating,
            hasContent: !!msg.content,
            hasHtmlContent: !!msg.htmlContent
          }))
        });
        
        return updated;
      });
    });
    
    // 🔧 强制重新渲染
    setRenderKey(prev => prev + 1);
    
    console.log('✅ useChatHistory: flushSync完成，状态应该已立即更新');
  }, []);

  // 🔧 标记AI生成停止
  const stopAIGeneration = useCallback((htmlContent?: string) => {
    setChatHistory(prev => {
      const updated = [...prev];
      const lastMessage = updated[updated.length - 1];
      if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating) {
        lastMessage.isGenerating = false;
        lastMessage.content = '生成已停止';
        if (htmlContent) {
          lastMessage.htmlContent = htmlContent;
        }
      }
      return updated;
    });
  }, []);

  // 🔧 清空聊天历史
  const clearChatHistory = useCallback(() => {
    console.log('🧹 useChatHistory: 清空聊天历史', {
      previousLength: chatHistory.length,
      timestamp: new Date().toISOString()
    });
    setChatHistory([]);
  }, [chatHistory.length]);

  // 🔧 更新最后一条AI消息的版本号
  const updateLastAIMessageVersion = useCallback((versionNumber: number) => {
    setChatHistory(prev => {
      const updated = [...prev];
      const lastMessage = updated[updated.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        lastMessage.versionNumber = versionNumber;
      }
      return updated;
    });
  }, []);

  // 🔧 重置到新会话状态
  const startNewChat = useCallback(() => {
    setChatHistory([]);
    console.log('🆕 useChatHistory: 已重置到新会话状态');
  }, []);

  // 🎯 清理最后两条消息（用于积分不足时回退）
  const clearLastTwoMessages = useCallback(() => {
    setChatHistory(prev => {
      if (prev.length >= 2) {
        const updated = prev.slice(0, -2);
        console.log('🔄 useChatHistory: 已清理最后两条消息', {
          原始消息数: prev.length,
          清理后消息数: updated.length,
          清理的消息: prev.slice(-2).map(msg => ({ id: msg.id, type: msg.type }))
        });
        return updated;
      }
      return prev;
    });
  }, []);

  return {
    // 状态
    chatHistory,
    setChatHistory,
    isLoadingChat,
    renderKey,

    // 项目管理
    loadChatHistory,       // 加载现有项目的聊天历史
    startNewChat,          // 开始新会话

    // 消息管理
    addUserMessage,
    addAIMessage,
    updateLastAIMessage,
    completeAIGeneration,
    stopAIGeneration,
    clearChatHistory,
    clearLastTwoMessages,  // 清理最后两条消息（积分不足回退用）
    updateLastAIMessageVersion,
  };
}; 