<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie政策 - LoomRun</title>
    <!-- 浅色模式图标（默认） -->
    <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/light/ghost-icon-light-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/light/ghost-icon-light-16x16.png">

    <!-- 深色模式图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/dark/ghost-icon-dark-32x32.png" media="(prefers-color-scheme: dark)">
    <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/dark/ghost-icon-dark-16x16.png" media="(prefers-color-scheme: dark)">

    <!-- 兜底方案 -->
    <link rel="icon" type="image/x-icon" href="/favicon_io/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .sidebar {
            width: 280px;
            background: #1e293b;
            color: white;
            padding: 2rem 0;
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
        }

        .logo {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #334155;
            margin-bottom: 2rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: block;
            padding: 0.75rem 2rem;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .nav-link:hover, .nav-link.active {
            background: #334155;
            color: white;
            border-left-color: #3b82f6;
        }

        .content {
            flex: 1;
            padding: 3rem;
            overflow-y: auto;
        }

        .header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .header .meta {
            color: #64748b;
            font-size: 0.9rem;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin: 1.5rem 0 1rem;
        }

        .section p {
            margin-bottom: 1rem;
            color: #4b5563;
            line-height: 1.7;
        }

        .section ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        .section li {
            margin-bottom: 0.5rem;
            color: #4b5563;
        }

        .highlight {
            background: #fef3c7;
            padding: 1rem;
            border-left: 4px solid #f59e0b;
            margin: 1.5rem 0;
            border-radius: 0 4px 4px 0;
        }

        .cookie-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .cookie-table th,
        .cookie-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .cookie-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
        }

        .cookie-table tr:last-child td {
            border-bottom: none;
        }

        .cookie-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .cookie-type.necessary {
            background: #dcfce7;
            color: #166534;
        }

        .cookie-type.functional {
            background: #dbeafe;
            color: #1e40af;
        }

        .cookie-type.analytics {
            background: #fef3c7;
            color: #92400e;
        }

        .cookie-type.marketing {
            background: #fce7f3;
            color: #be185d;
        }

        .contact-info {
            background: #f1f5f9;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .contact-info h4 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }

            .content {
                padding: 2rem 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .cookie-table {
                font-size: 0.875rem;
            }

            .cookie-table th,
            .cookie-table td {
                padding: 0.75rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <h1>🚀 LoomRun</h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#overview" class="nav-link active">Cookie概述</a>
                </li>
                <li class="nav-item">
                    <a href="#what-are-cookies" class="nav-link">什么是Cookie</a>
                </li>
                <li class="nav-item">
                    <a href="#why-we-use" class="nav-link">使用目的</a>
                </li>
                <li class="nav-item">
                    <a href="#types" class="nav-link">Cookie类型</a>
                </li>
                <li class="nav-item">
                    <a href="#details" class="nav-link">详细信息</a>
                </li>
                <li class="nav-item">
                    <a href="#third-party" class="nav-link">第三方Cookie</a>
                </li>
                <li class="nav-item">
                    <a href="#management" class="nav-link">管理Cookie</a>
                </li>
                <li class="nav-item">
                    <a href="#browser-settings" class="nav-link">浏览器设置</a>
                </li>
                <li class="nav-item">
                    <a href="#updates" class="nav-link">政策更新</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系我们</a>
                </li>
            </ul>
        </nav>

        <main class="content">
            <div class="header">
                <h1>Cookie政策</h1>
                <div class="meta">
                    最后更新时间：2025年7月29日 | 生效日期：2025年7月29日
                </div>
            </div>

            <section id="overview" class="section">
                <h2>Cookie政策概述</h2>
                <p>本Cookie政策解释了LoomRun如何在我们的网站和服务中使用Cookie和类似技术。我们致力于透明地告知您我们如何使用这些技术，以及您如何控制它们。</p>

                <div class="highlight">
                    <strong>重要提示：</strong>通过继续使用LoomRun，您同意我们按照本政策使用Cookie。您可以随时通过浏览器设置或我们提供的工具来管理您的Cookie偏好。
                </div>

                <h3>政策要点</h3>
                <ul>
                    <li>我们使用Cookie来改善您的用户体验</li>
                    <li>某些Cookie对网站功能是必需的</li>
                    <li>您可以控制非必需Cookie的使用</li>
                    <li>我们会定期审查和更新我们的Cookie使用</li>
                </ul>
            </section>

            <section id="what-are-cookies" class="section">
                <h2>什么是Cookie</h2>

                <h3>1. Cookie定义</h3>
                <p>Cookie是当您访问网站时存储在您的计算机或移动设备上的小型文本文件。它们被广泛用于使网站工作，或更高效地工作，以及向网站所有者提供信息。</p>

                <h3>2. Cookie的工作原理</h3>
                <ul>
                    <li><strong>设置</strong>：当您首次访问网站时，服务器会向您的浏览器发送Cookie</li>
                    <li><strong>存储</strong>：浏览器将Cookie存储在您的设备上</li>
                    <li><strong>发送</strong>：在后续访问中，浏览器会将Cookie发送回服务器</li>
                    <li><strong>识别</strong>：服务器使用Cookie来识别您并记住您的偏好</li>
                </ul>

                <h3>3. 类似技术</h3>
                <p>除了Cookie，我们还可能使用其他类似技术：</p>
                <ul>
                    <li><strong>本地存储</strong>：HTML5本地存储，用于在您的设备上存储数据</li>
                    <li><strong>会话存储</strong>：临时存储，浏览器关闭时自动清除</li>
                    <li><strong>像素标签</strong>：用于跟踪用户行为的小型图像文件</li>
                    <li><strong>Web信标</strong>：嵌入在网页或邮件中的透明图像</li>
                </ul>
            </section>

            <section id="why-we-use" class="section">
                <h2>我们为什么使用Cookie</h2>

                <h3>1. 提供基本功能</h3>
                <ul>
                    <li>维持您的登录状态</li>
                    <li>记住您的语言和主题偏好</li>
                    <li>保存您的项目设置</li>
                    <li>确保网站安全性</li>
                </ul>

                <h3>2. 改善用户体验</h3>
                <ul>
                    <li>记住您的选择和偏好</li>
                    <li>提供个性化内容</li>
                    <li>优化网站性能</li>
                    <li>简化导航和使用流程</li>
                </ul>

                <h3>3. 分析和优化</h3>
                <ul>
                    <li>了解用户如何使用我们的服务</li>
                    <li>识别和修复技术问题</li>
                    <li>测试新功能的效果</li>
                    <li>改进服务质量</li>
                </ul>

                <h3>4. 安全保护</h3>
                <ul>
                    <li>防止欺诈和滥用</li>
                    <li>验证用户身份</li>
                    <li>保护账户安全</li>
                    <li>检测异常活动</li>
                </ul>
            </section>

            <section id="types" class="section">
                <h2>Cookie类型</h2>

                <h3>1. 按持续时间分类</h3>
                <ul>
                    <li><strong>会话Cookie</strong>：临时Cookie，浏览器关闭时自动删除</li>
                    <li><strong>持久Cookie</strong>：在设定的时间内保留在您的设备上</li>
                </ul>

                <h3>2. 按来源分类</h3>
                <ul>
                    <li><strong>第一方Cookie</strong>：由LoomRun直接设置的Cookie</li>
                    <li><strong>第三方Cookie</strong>：由我们的合作伙伴设置的Cookie</li>
                </ul>

                <h3>3. 按功能分类</h3>
                <ul>
                    <li><strong>必需Cookie</strong>：网站正常运行所必需的Cookie</li>
                    <li><strong>功能Cookie</strong>：增强网站功能的Cookie</li>
                    <li><strong>分析Cookie</strong>：用于分析网站使用情况的Cookie</li>
                    <li><strong>营销Cookie</strong>：用于个性化广告的Cookie（如适用）</li>
                </ul>
            </section>

            <section id="details" class="section">
                <h2>Cookie详细信息</h2>

                <h3>1. 必需Cookie</h3>
                <p>这些Cookie对于网站的基本功能是必需的，无法禁用：</p>

                <table class="cookie-table">
                    <thead>
                        <tr>
                            <th>Cookie名称</th>
                            <th>类型</th>
                            <th>用途</th>
                            <th>有效期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>loomrun_token</td>
                            <td><span class="cookie-type necessary">必需</span></td>
                            <td>用户身份验证和会话管理</td>
                            <td>30天</td>
                        </tr>
                        <tr>
                            <td>loomrun_session</td>
                            <td><span class="cookie-type necessary">必需</span></td>
                            <td>维持用户会话状态</td>
                            <td>会话结束</td>
                        </tr>
                        <tr>
                            <td>csrf_token</td>
                            <td><span class="cookie-type necessary">必需</span></td>
                            <td>防止跨站请求伪造攻击</td>
                            <td>会话结束</td>
                        </tr>
                    </tbody>
                </table>

                <h3>2. 功能Cookie</h3>
                <p>这些Cookie用于增强网站功能和用户体验：</p>

                <table class="cookie-table">
                    <thead>
                        <tr>
                            <th>Cookie名称</th>
                            <th>类型</th>
                            <th>用途</th>
                            <th>有效期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>loomrun_theme</td>
                            <td><span class="cookie-type functional">功能</span></td>
                            <td>记住用户的主题偏好</td>
                            <td>1年</td>
                        </tr>
                        <tr>
                            <td>loomrun_language</td>
                            <td><span class="cookie-type functional">功能</span></td>
                            <td>记住用户的语言设置</td>
                            <td>1年</td>
                        </tr>
                        <tr>
                            <td>editor_settings</td>
                            <td><span class="cookie-type functional">功能</span></td>
                            <td>保存编辑器配置</td>
                            <td>6个月</td>
                        </tr>
                    </tbody>
                </table>

                <h3>3. 分析Cookie</h3>
                <p>这些Cookie帮助我们了解网站的使用情况：</p>

                <table class="cookie-table">
                    <thead>
                        <tr>
                            <th>Cookie名称</th>
                            <th>类型</th>
                            <th>用途</th>
                            <th>有效期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>_analytics_id</td>
                            <td><span class="cookie-type analytics">分析</span></td>
                            <td>匿名用户行为分析</td>
                            <td>2年</td>
                        </tr>
                        <tr>
                            <td>_performance</td>
                            <td><span class="cookie-type analytics">分析</span></td>
                            <td>网站性能监控</td>
                            <td>30天</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section id="third-party" class="section">
                <h2>第三方Cookie</h2>

                <h3>1. 第三方服务</h3>
                <p>我们可能使用第三方服务，这些服务会设置自己的Cookie：</p>
                <ul>
                    <li><strong>CDN服务</strong>：用于加速内容传输</li>
                    <li><strong>分析服务</strong>：用于网站使用情况分析</li>
                    <li><strong>安全服务</strong>：用于防护和安全监控</li>
                    <li><strong>支付服务</strong>：用于处理付款（如适用）</li>
                </ul>

                <h3>2. 社交媒体插件</h3>
                <p>如果我们的网站包含社交媒体插件，这些平台可能会设置Cookie：</p>
                <ul>
                    <li>微信分享插件</li>
                    <li>微博分享插件</li>
                    <li>其他社交平台插件</li>
                </ul>

                <h3>3. 第三方Cookie控制</h3>
                <p>对于第三方Cookie，您可以：</p>
                <ul>
                    <li>通过浏览器设置阻止第三方Cookie</li>
                    <li>访问第三方服务的隐私政策了解更多信息</li>
                    <li>使用第三方提供的退出机制</li>
                    <li>联系我们了解具体的第三方服务</li>
                </ul>
            </section>

            <section id="management" class="section">
                <h2>如何管理Cookie</h2>

                <h3>1. Cookie偏好设置</h3>
                <p>您可以通过以下方式管理Cookie偏好：</p>
                <ul>
                    <li>在我们网站的Cookie横幅中选择您的偏好</li>
                    <li>访问账户设置页面调整Cookie设置</li>
                    <li>使用浏览器的Cookie管理功能</li>
                </ul>

                <h3>2. 禁用Cookie的影响</h3>
                <p>如果您选择禁用某些Cookie，可能会影响：</p>
                <ul>
                    <li><strong>必需Cookie</strong>：禁用会导致网站无法正常工作</li>
                    <li><strong>功能Cookie</strong>：禁用会影响个性化功能</li>
                    <li><strong>分析Cookie</strong>：禁用不会影响网站功能，但会影响我们改进服务的能力</li>
                </ul>

                <h3>3. 清除Cookie</h3>
                <p>您可以随时清除已存储的Cookie：</p>
                <ul>
                    <li>通过浏览器设置清除所有Cookie</li>
                    <li>选择性删除特定网站的Cookie</li>
                    <li>设置浏览器在关闭时自动清除Cookie</li>
                </ul>
            </section>

            <section id="browser-settings" class="section">
                <h2>浏览器Cookie设置</h2>

                <h3>1. Chrome浏览器</h3>
                <ul>
                    <li>点击右上角的三点菜单 → 设置</li>
                    <li>点击"隐私设置和安全性" → "Cookie及其他网站数据"</li>
                    <li>选择您的Cookie设置偏好</li>
                    <li>可以添加允许或阻止的网站列表</li>
                </ul>

                <h3>2. Firefox浏览器</h3>
                <ul>
                    <li>点击右上角的菜单按钮 → 选项</li>
                    <li>选择"隐私与安全"面板</li>
                    <li>在"Cookie和网站数据"部分调整设置</li>
                    <li>可以查看和管理已存储的Cookie</li>
                </ul>

                <h3>3. Safari浏览器</h3>
                <ul>
                    <li>点击Safari菜单 → 偏好设置</li>
                    <li>点击"隐私"标签</li>
                    <li>选择"阻止所有Cookie"或其他选项</li>
                    <li>可以管理网站数据</li>
                </ul>

                <h3>4. Edge浏览器</h3>
                <ul>
                    <li>点击右上角的三点菜单 → 设置</li>
                    <li>点击"Cookie和站点权限"</li>
                    <li>点击"Cookie和站点数据"</li>
                    <li>调整Cookie设置和管理例外情况</li>
                </ul>

                <h3>5. 移动设备</h3>
                <p><strong>iOS Safari：</strong></p>
                <ul>
                    <li>设置 → Safari → 隐私与安全性</li>
                    <li>选择"阻止所有Cookie"或其他选项</li>
                </ul>

                <p><strong>Android Chrome：</strong></p>
                <ul>
                    <li>Chrome应用 → 设置 → 网站设置</li>
                    <li>点击"Cookie" → 调整设置</li>
                </ul>
            </section>

            <section id="updates" class="section">
                <h2>政策更新</h2>

                <h3>1. 更新通知</h3>
                <p>我们可能会不时更新本Cookie政策。重大更改时，我们会：</p>
                <ul>
                    <li>在网站上发布更新通知</li>
                    <li>通过邮件通知注册用户</li>
                    <li>在Cookie横幅中提示变更</li>
                    <li>提供合理的时间让您审查变更</li>
                </ul>

                <h3>2. 持续同意</h3>
                <p>继续使用我们的服务表示您接受更新后的Cookie政策。如果您不同意变更，请：</p>
                <ul>
                    <li>调整您的Cookie设置</li>
                    <li>停止使用我们的服务</li>
                    <li>联系我们表达您的关切</li>
                </ul>

                <div class="highlight">
                    <strong>版本更新记录：</strong><br>
                    v1.0 (2025年7月29日)：初始版本发布
                </div>
            </section>

            <section id="contact" class="section">
                <h2>联系我们</h2>
                <p>如果您对我们的Cookie使用有任何疑问或关切，请联系我们：</p>

                <div class="contact-info">
                    <h4>联系方式</h4>
                    <p><strong>邮箱：</strong> <EMAIL></p>
                    <p><strong>地址：</strong> 中国</p>
                    <p><strong>邮编：</strong> 100000</p>
                </div>

                <p>我们将在收到您的询问后，在合理时间内给予回复。</p>

                <h3>常见问题</h3>
                <ul>
                    <li><strong>Q: 我可以完全禁用Cookie吗？</strong><br>
                        A: 您可以通过浏览器设置禁用Cookie，但这可能影响网站的正常功能。</li>
                    <li><strong>Q: Cookie会收集我的个人信息吗？</strong><br>
                        A: 大多数Cookie只包含匿名标识符，不直接包含个人信息。</li>
                    <li><strong>Q: 第三方Cookie安全吗？</strong><br>
                        A: 我们只与可信的第三方合作，但建议您查看他们的隐私政策。</li>
                    <li><strong>Q: 如何知道网站使用了哪些Cookie？</strong><br>
                        A: 您可以通过浏览器的开发者工具查看当前页面的Cookie。</li>
                </ul>
            </section>
        </main>
    </div>

    <script>
        // 导航高亮
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');

            // 点击导航链接
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });

                        // 更新活跃状态
                        navLinks.forEach(l => l.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });

            // 滚动时更新导航状态
            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>