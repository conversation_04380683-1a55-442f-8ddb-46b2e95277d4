// Gemini API 客户端
// 使用中转服务器访问 Gemini 2.5 Flash 模型

export interface GeminiMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

export interface GeminiRequest {
  model: string;
  messages: GeminiMessage[];
  max_tokens?: number;
  temperature?: number;
  stream?: boolean;
}

export interface GeminiResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface GeminiStreamChunk {
  choices: Array<{
    delta: {
      content?: string;
      role?: string;
    };
    finish_reason?: string;
  }>;
}

export class GeminiClient {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL = "https://imxzlpwclisz.ap-southeast-1.clawcloudrun.com") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  private async makeRequest(requestPayload: any, retryCount = 0): Promise<Response> {
    const maxRetries = 3;
    console.log(`🔗 Gemini API 请求 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, this.baseURL);

    try {
      // 🔧 简化超时控制，避免中转服务兼容性问题
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

      const response = await fetch(`${this.baseURL}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestPayload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log('✅ Gemini API 请求成功');
      return response;

    } catch (error: any) {
      console.error(`❌ Gemini API 请求失败 (尝试 ${retryCount + 1}):`, error.message);

      // 🔧 判断是否应该重试
      const isRetryableError =
        error.name === 'AbortError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ETIMEDOUT' ||
        error.message?.includes('fetch failed') ||
        error.message?.includes('network error');

      if (isRetryableError && retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // 指数退避：1s, 2s, 4s
        console.log(`🔄 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(requestPayload, retryCount + 1);
      }

      // 🔧 提供更详细的错误信息
      if (error.name === 'AbortError') {
        throw new Error('Gemini API request timeout (180s) - 请求超时，可能是因为生成内容过长');
      } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
        throw new Error(`Network error: ${error.message}`);
      } else {
        throw error;
      }
    }
  }



  async *chatCompletionStream(request: GeminiRequest): AsyncGenerator<GeminiStreamChunk> {
    // 验证模型名称
    if (!request.model.includes('gemini')) {
      throw new Error(`Invalid model: ${request.model}. Expected gemini model.`);
    }

    // 验证消息
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: true,
      ...(request.max_tokens && { max_tokens: request.max_tokens }),
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('Gemini API Stream Request:', JSON.stringify(requestPayload, null, 2));

    let response: Response;
    try {
      response = await this.makeRequest(requestPayload);
    } catch (error: any) {
      console.error('❌ Gemini 流式请求失败:', error.message);
      throw new Error(`Gemini streaming request failed: ${error.message}`);
    }

    if (!response.body) {
      throw new Error('Gemini API response body is null');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = ''; // 🔧 关键修复：添加缓冲区处理，避免数据包截断

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('✅ Gemini 流式请求完成');
          break;
        }

        // 🔧 关键修复：使用缓冲区处理，确保完整的数据行
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个可能不完整的行

        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed === '' || trimmed === 'data: [DONE]') {
            if (trimmed === 'data: [DONE]') {
              console.log('✅ Gemini 流式数据结束');
              return;
            }
            continue;
          }

          if (trimmed.startsWith('data: ')) {
            const data = trimmed.slice(6);

            try {
              const parsed = JSON.parse(data);
              yield parsed;
            } catch (e) {
              console.warn('⚠️ 解析流式数据失败:', { data: data.substring(0, 100), error: e.message });
              // 🔧 不要因为单个解析失败就中断整个流
            }
          }
        }
      }
    } catch (error: any) {
      console.error('❌ Gemini 流式读取失败:', error.message);

      // 🔧 区分不同类型的错误，避免因为小错误就完全失败
      if (error.name === 'AbortError') {
        console.log('🔧 Gemini 流式请求被中止（可能是超时）');
        throw new Error('Gemini streaming request was aborted (timeout)');
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        console.log('🔧 Gemini 网络连接问题');
        throw new Error(`Gemini network error: ${error.message}`);
      } else {
        console.log('🔧 Gemini 其他流式读取错误');
        throw new Error(`Gemini streaming read failed: ${error.message}`);
      }
    } finally {
      try {
        reader.releaseLock();
      } catch (e) {
        console.warn('⚠️ 释放reader锁失败:', e);
      }
    }
  }

  async chatCompletion(request: GeminiRequest): Promise<GeminiResponse> {
    // 验证模型名称
    if (!request.model.includes('gemini')) {
      throw new Error(`Invalid model: ${request.model}. Expected gemini model.`);
    }

    // 验证消息
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    const requestPayload = {
      model: request.model,
      messages: request.messages,
      stream: false,
      ...(request.max_tokens && { max_tokens: request.max_tokens }),
      ...(request.temperature && { temperature: Math.max(0, Math.min(2, request.temperature)) }),
    };

    console.log('Gemini API Request:', JSON.stringify(requestPayload, null, 2));

    const response = await this.makeRequest(requestPayload);

    return response.json();
  }
}
