import { NextRequest, NextResponse } from 'next/server';

// 设置数据类型
interface SettingsData {
  dataOptimization: boolean;
  language: 'zh' | 'en' | 'ug';
  theme: 'light' | 'dark' | 'system';
}

// 模拟数据库存储（实际项目中应该使用真实数据库）
const userSettings = new Map<string, SettingsData>();

// 获取用户IP作为临时标识（实际项目中应该使用用户ID）
function getUserId(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : 'anonymous';
  return ip;
}

// GET - 获取用户设置
export async function GET(request: NextRequest) {
  try {
    const userId = getUserId(request);
    const settings = userSettings.get(userId) || {
      dataOptimization: true, // 默认开启
      language: 'zh',
      theme: 'system',
    };

    return NextResponse.json({
      success: true,
      settings,
    });
  } catch (error) {
    console.error('获取设置失败:', error);
    return NextResponse.json(
      { success: false, error: '获取设置失败' },
      { status: 500 }
    );
  }
}

// POST - 保存用户设置
export async function POST(request: NextRequest) {
  try {
    const userId = getUserId(request);
    const body = await request.json();
    
    // 验证设置数据
    const { dataOptimization, language, theme } = body;
    
    if (typeof dataOptimization !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'dataOptimization 必须是布尔值' },
        { status: 400 }
      );
    }

    if (!['zh', 'en', 'ug'].includes(language)) {
      return NextResponse.json(
        { success: false, error: '不支持的语言设置' },
        { status: 400 }
      );
    }

    if (!['light', 'dark', 'system'].includes(theme)) {
      return NextResponse.json(
        { success: false, error: '不支持的主题设置' },
        { status: 400 }
      );
    }

    // 保存设置
    const settings: SettingsData = {
      dataOptimization,
      language,
      theme,
    };

    userSettings.set(userId, settings);

    // 记录数据优化设置变更
    console.log(`用户 ${userId} ${dataOptimization ? '启用' : '禁用'}了数据优化功能`);

    return NextResponse.json({
      success: true,
      message: '设置保存成功',
      settings,
    });
  } catch (error) {
    console.error('保存设置失败:', error);
    return NextResponse.json(
      { success: false, error: '保存设置失败' },
      { status: 500 }
    );
  }
}

// PUT - 更新特定设置
export async function PUT(request: NextRequest) {
  try {
    const userId = getUserId(request);
    const body = await request.json();
    const { key, value } = body;

    // 获取当前设置
    const currentSettings = userSettings.get(userId) || {
      dataOptimization: true,
      language: 'zh',
      theme: 'system',
    };

    // 更新特定设置
    switch (key) {
      case 'dataOptimization':
        if (typeof value !== 'boolean') {
          return NextResponse.json(
            { success: false, error: 'dataOptimization 必须是布尔值' },
            { status: 400 }
          );
        }
        currentSettings.dataOptimization = value;
        console.log(`用户 ${userId} ${value ? '启用' : '禁用'}了数据优化功能`);
        break;

      case 'language':
        if (!['zh', 'en', 'ug'].includes(value)) {
          return NextResponse.json(
            { success: false, error: '不支持的语言设置' },
            { status: 400 }
          );
        }
        currentSettings.language = value;
        break;

      case 'theme':
        if (!['light', 'dark', 'system'].includes(value)) {
          return NextResponse.json(
            { success: false, error: '不支持的主题设置' },
            { status: 400 }
          );
        }
        currentSettings.theme = value;
        break;

      default:
        return NextResponse.json(
          { success: false, error: '不支持的设置项' },
          { status: 400 }
        );
    }

    userSettings.set(userId, currentSettings);

    return NextResponse.json({
      success: true,
      message: '设置更新成功',
      settings: currentSettings,
    });
  } catch (error) {
    console.error('更新设置失败:', error);
    return NextResponse.json(
      { success: false, error: '更新设置失败' },
      { status: 500 }
    );
  }
} 