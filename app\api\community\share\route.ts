import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/mysql';
import { cookies } from 'next/headers';
import { getUserByToken } from '@/lib/auth-service';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

export async function POST(request: NextRequest) {
  try {
    // 检查内容类型以决定如何解析请求
    const contentType = request.headers.get('content-type') || '';
    
    let projectId: number;
    let htmlContent: string;
    let customTitle: string;
    let category: string = 'sites'; // 默认分类
    let previewImage: File | null = null;
    let previewImageType: 'auto' | 'manual' | 'upload' = 'static';
    let cropArea: any = null;

    if (contentType.includes('multipart/form-data')) {
      // 处理包含文件上传的请求
      const formData = await request.formData();
      
      projectId = parseInt(formData.get('projectId') as string);
      htmlContent = formData.get('htmlContent') as string;
      customTitle = formData.get('title') as string;
      category = formData.get('category') as string || 'sites';
      previewImage = formData.get('previewImage') as File | null;
      previewImageType = (formData.get('previewImageType') as any) || 'static';
      
      const cropAreaStr = formData.get('cropArea') as string;
      if (cropAreaStr) {
        try {
          cropArea = JSON.parse(cropAreaStr);
        } catch (e) {
          console.warn('无法解析裁剪区域数据:', e);
        }
      }
    } else {
      // 处理JSON请求（向后兼容）
      const body = await request.json();
      projectId = body.projectId;
      htmlContent = body.htmlContent;
      customTitle = body.title || body.customTitle; // 兼容旧版本
    }

    // 验证用户身份
    const cookieStore = await cookies();
    const authToken = cookieStore.get('loomrun_token');
    
    if (!authToken) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const user = await getUserByToken(authToken.value);
    if (!user) {
      return NextResponse.json({ error: '无效的认证令牌' }, { status: 401 });
    }

    const userId = user.id;

    if (!projectId || !htmlContent || !customTitle) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 检查项目是否存在且属于当前用户
    const projectRows = await executeQuery(
      'SELECT id, title FROM projects WHERE id = ? AND user_id = ?',
      [projectId, userId]
    );

    if (!projectRows || !Array.isArray(projectRows) || projectRows.length === 0) {
      return NextResponse.json({ error: '项目不存在或无权限' }, { status: 403 });
    }

    // 处理预览图上传
    let previewImageUrl: string | null = null;
    let previewMetadata: any = null;

    if (previewImage && previewImage.size > 0) {
      try {
        // 创建上传目录
        const uploadDir = join(process.cwd(), 'public', 'uploads', 'community');
        await mkdir(uploadDir, { recursive: true });

        // 生成唯一文件名（使用时间戳和随机数）
        const fileExtension = previewImage.name.split('.').pop() || 'png';
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 15);
        const fileName = `${timestamp}_${random}.${fileExtension}`;
        const filePath = join(uploadDir, fileName);

        // 保存文件
        const bytes = await previewImage.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // 设置预览图URL
        previewImageUrl = `/uploads/community/${fileName}`;
        
        // 记录预览图元数据
        previewMetadata = {
          originalName: previewImage.name,
          size: previewImage.size,
          type: previewImage.type,
          uploadedAt: new Date().toISOString(),
          imageType: previewImageType,
          cropArea: cropArea
        };

        console.log('✅ 预览图上传成功:', previewImageUrl);
      } catch (uploadError) {
        console.error('❌ 预览图上传失败:', uploadError);
        // 继续执行，不因为图片上传失败而中断分享
      }
    }

    // 检查是否已经共享过
    const existingRows = await executeQuery(
      'SELECT id FROM community_projects WHERE original_project_id = ?',
      [projectId]
    );

    let communityProjectId: number;

    if (existingRows && Array.isArray(existingRows) && existingRows.length > 0) {
      // 更新已存在的社区项目（基础字段）
      let updateQuery = `
        UPDATE community_projects 
        SET html_content = ?, 
            title = ?, 
            updated_at = NOW() 
        WHERE original_project_id = ?
      `;
      
      let updateParams = [htmlContent, customTitle, projectId];

             // 如果有预览图数据，尝试更新扩展字段
       if (previewImageUrl || previewImageType || previewMetadata) {
         try {
           updateQuery = `
             UPDATE community_projects 
             SET html_content = ?, 
                 title = ?, 
                 category = ?,
                 preview_image_url = COALESCE(?, preview_image_url),
                 preview_image_type = ?,
                 preview_metadata = ?,
                 updated_at = NOW() 
             WHERE original_project_id = ?
           `;
           
           updateParams = [
             htmlContent, 
             customTitle,
             category || 'sites',
             previewImageUrl,
             previewImageType,
             previewMetadata ? JSON.stringify(previewMetadata) : null,
             projectId
           ];
         } catch (e) {
           console.warn('扩展字段不存在，使用基础更新:', e);
         }
       }
      
      await executeQuery(updateQuery, updateParams);

      communityProjectId = (existingRows as any[])[0].id;
      console.log('✅ 更新社区项目:', communityProjectId);
    } else {
      // 创建新的社区项目
      let insertQuery = `
        INSERT INTO community_projects (
          original_project_id, 
          user_id, 
          title, 
          html_content, 
          category,
          created_at, 
          updated_at
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      let insertParams = [projectId, userId, customTitle, htmlContent, category || 'sites'];

             // 如果有预览图数据，尝试使用扩展字段
       if (previewImageUrl || previewImageType || previewMetadata) {
         try {
           insertQuery = `
             INSERT INTO community_projects (
               original_project_id, 
               user_id, 
               title, 
               html_content, 
               category,
               preview_image_url,
               preview_image_type,
               preview_metadata,
               created_at, 
               updated_at
             ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
           `;
           
           insertParams = [
             projectId, 
             userId, 
             customTitle,
             htmlContent,
             category || 'sites',
             previewImageUrl,
             previewImageType,
             previewMetadata ? JSON.stringify(previewMetadata) : null
           ];
         } catch (e) {
           console.warn('扩展字段不存在，使用基础插入:', e);
         }
       }
      
      const result = await executeQuery(insertQuery, insertParams);

      communityProjectId = (result as any).insertId;
      console.log('✅ 创建新社区项目:', communityProjectId);
    }

    // 构建社区页面URL
    const communityUrl = `${request.nextUrl.origin}/?tab=community&highlight=${communityProjectId}`;

    return NextResponse.json({ 
      success: true, 
      message: '项目已成功共享到社区',
      communityProjectId,
      communityUrl,
      previewImageUrl,
      data: {
        id: communityProjectId,
        title: customTitle,
        previewImage: previewImageUrl,
        hasPreviewImage: !!previewImageUrl
      }
    });

  } catch (error) {
    console.error('❌ 共享项目失败:', error);
    return NextResponse.json({ 
      error: '服务器错误',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
} 