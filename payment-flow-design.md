# LoomRun 订阅积分支付流程设计

## 1. 整体架构设计

### 1.1 页面展示方式
- **弹窗切换方案**：在同一个弹窗内完成整个支付流程
- **三个视图状态**：选择套餐 → 支付页面 → 成功页面
- **用户体验**：全程无页面跳转，保持上下文连续性

### 1.2 订单类型
- **订阅订单**：会员套餐购买（membership_orders表）
- **充值订单**：积分套餐购买（recharge_orders表）

## 2. 订单状态管理

### 2.1 订单状态定义
- **pending**：订单已创建，等待支付
- **paid**：支付成功，积分已发放/会员已开通
- **cancelled**：用户选择重新下单时，旧订单被取消
- **expired**：订单超过30分钟未支付，系统自动过期
- **refunded**：管理员操作退款（暂时不实现）

### 2.2 状态转换规则
```
pending → paid (用户支付成功)
pending → cancelled (用户主动选择重新下单)
pending → expired (30分钟后系统自动过期)
paid → refunded (管理员退款操作)
```

### 2.3 订单过期机制
- **有效期**：订单创建后30分钟内有效
- **自动过期**：系统定时任务每5分钟检查一次，将过期的pending订单标记为expired
- **过期处理**：过期订单不影响用户重新下单

## 3. 用户操作流程

### 3.1 正常支付流程
1. **进入订阅页面**
   - 用户点击菜单"订阅积分"
   - 打开订阅积分弹窗（选择视图）

2. **选择套餐**
   - 选择订阅计划或积分套餐
   - 选择购买数量（积分套餐）
   - 确认订单信息

3. **创建订单**
   - 用户点击"立即支付"
   - 系统检查是否有pending订单
   - 创建新订单（pending状态）
   - 弹窗切换到支付视图

4. **支付处理**
   - 显示订单详情（订单号、金额、套餐信息）
   - 选择支付方式（支付宝/微信）
   - 显示支付二维码
   - 点击"模拟支付成功"按钮

5. **支付成功**
   - 2-3秒支付处理动画
   - 订单状态更新为paid
   - 发放积分或开通会员服务
   - 弹窗切换到成功视图

6. **完成流程**
   - 显示获得的积分/会员权益
   - 用户点击"完成"按钮
   - 关闭弹窗，回到原页面
   - 页面积分信息自动更新

### 3.2 重复下单处理流程
1. **检查pending订单**
   - 用户点击"立即支付"时
   - 系统检查该用户该类型是否有pending订单

2. **情况1：没有pending订单**
   - 直接创建新订单
   - 进入支付页面

3. **情况2：有pending订单且未过期**
   - 弹出确认对话框
   - 显示：`您有一个未完成的订单（订单号：XXX，金额：¥XX.XX）`
   - 提供两个选择：`[继续支付]` `[重新下单]`
   
   **选择继续支付**：
   - 直接进入该订单的支付页面
   
   **选择重新下单**：
   - 旧订单标记为cancelled
   - 创建新订单
   - 进入新订单支付页面

4. **情况3：有pending订单但已过期**
   - 自动标记旧订单为expired
   - 创建新订单
   - 进入支付页面

### 3.3 异常处理流程
1. **用户关闭弹窗**
   - 订单保持pending状态
   - 不做任何处理
   - 等待自动过期或用户重新支付

2. **支付失败**
   - 订单保持pending状态
   - 用户可以重新尝试支付
   - 显示错误提示信息

3. **网络异常**
   - 订单状态以数据库为准
   - 前端重新查询订单状态
   - 根据状态决定显示内容

## 4. 数据库结构设计

### 4.1 订单表字段补充

#### membership_orders表新增字段
- `plan_key`：关联的订阅计划key
- `expires_at`：订单过期时间（创建时间+30分钟）

#### recharge_orders表新增字段
- `points_validity_days`：积分有效期天数（0表示永久）
- `package_key`：关联的积分套餐key
- `bonus_points`：赠送积分数量
- `expires_at`：订单过期时间（创建时间+30分钟）

### 4.2 订单编号规则
- **订阅订单**：`SUB{YYYYMMDD}{6位随机数}`，如：`SUB20250731123456`
- **充值订单**：`RCH{YYYYMMDD}{6位随机数}`，如：`RCH20250731789012`

## 5. 积分发放逻辑

### 5.1 订阅会员支付成功后
1. 在`user_subscriptions`表创建会员记录
2. 在`user_points_balance`表添加subscription类型积分
3. 在`points_transactions`表记录积分获得交易
4. 更新用户总积分和总获得积分

### 5.2 充值积分支付成功后
1. 在`user_points_balance`表添加recharge类型积分
2. 在`points_transactions`表记录积分获得交易
3. 更新用户总积分和总获得积分
4. 如有赠送积分，一并处理

## 6. API接口设计

### 6.1 订单相关接口
- `POST /api/orders/subscription`：创建订阅订单
- `POST /api/orders/recharge`：创建充值订单
- `GET /api/orders/pending`：检查用户pending订单
- `POST /api/orders/[orderId]/pay`：处理支付
- `GET /api/orders/[orderId]`：查询订单详情
- `POST /api/orders/[orderId]/cancel`：取消订单（重新下单时使用）

### 6.2 支付相关接口
- `POST /api/payment/mock`：模拟支付处理
- `GET /api/payment/[orderId]/status`：查询支付状态

## 7. 用户界面设计要点

### 7.1 支付页面元素
- 订单信息展示（订单号、套餐名称、金额）
- 支付方式选择（支付宝、微信图标）
- 支付二维码展示区域
- 模拟支付按钮（仅开发环境）
- 关闭按钮（不是取消按钮）

### 7.2 成功页面元素
- 简洁的成功动画（勾选图标）
- 订单完成信息
- 获得的积分/会员权益说明
- "完成"按钮返回原页面

### 7.3 确认对话框设计
- 清晰的未完成订单信息
- 明确的操作选择按钮
- 友好的提示文案

## 8. 系统监控和统计

### 8.1 订单统计指标
- 订单创建数量
- 支付成功率（paid/pending+paid）
- 订单取消率（cancelled/total）
- 订单过期率（expired/total）
- 平均支付时长

### 8.2 用户行为分析
- 重复下单频率
- 支付方式偏好
- 套餐选择偏好
- 支付时段分布

## 9. 技术实现要点

### 9.1 前端状态管理
- 弹窗视图状态切换
- 订单信息状态管理
- 支付状态实时更新
- 错误处理和重试机制

### 9.2 后端处理逻辑
- 订单创建的原子性操作
- 支付成功后的事务处理
- 定时任务处理过期订单
- 并发订单创建的防重复处理

### 9.3 数据一致性保证
- 订单状态更新的事务性
- 积分发放的原子性操作
- 会员开通的一致性检查
- 异常情况的回滚机制
