/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 01/08/2025 08:22:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for membership_orders
-- ----------------------------
DROP TABLE IF EXISTS `membership_orders`;
CREATE TABLE `membership_orders`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `membership_type` enum('pro','max') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `plan_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联的订阅计划key',
  `duration_months` int NOT NULL,
  `original_price` decimal(10, 2) NOT NULL,
  `discount_price` decimal(10, 2) NOT NULL,
  `payment_method` enum('alipay','wechat','mock') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'mock',
  `status` enum('pending','paid','cancelled','expired','refunded') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending',
  `paid_at` datetime NULL DEFAULT NULL,
  `expires_at` datetime NULL DEFAULT NULL,
  `order_expires_at` datetime NULL DEFAULT NULL COMMENT '订单过期时间（创建时间+30分钟）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created`(`created_at` ASC) USING BTREE,
  INDEX `idx_plan_key`(`plan_key` ASC) USING BTREE,
  INDEX `idx_order_expires_at`(`order_expires_at` ASC) USING BTREE,
  INDEX `idx_user_status_expires`(`user_id` ASC, `status` ASC, `order_expires_at` ASC) USING BTREE,
  INDEX `idx_status_expires`(`status` ASC, `order_expires_at` ASC) USING BTREE,
  CONSTRAINT `membership_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of membership_orders
-- ----------------------------
INSERT INTO `membership_orders` VALUES (1, 46, 'SUB20250801751726', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'paid', '2025-08-01 06:19:56', NULL, '2025-07-31 22:45:18', '2025-08-01 06:15:18', '2025-08-01 06:19:56');
INSERT INTO `membership_orders` VALUES (2, 46, 'SUB20250801051590', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'expired', NULL, NULL, '2025-07-31 23:21:51', '2025-08-01 06:51:51', '2025-08-01 08:15:58');
INSERT INTO `membership_orders` VALUES (3, 47, 'SUB20250801826900', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'cancelled', NULL, NULL, '2025-08-01 00:32:20', '2025-08-01 08:02:20', '2025-08-01 08:13:57');
INSERT INTO `membership_orders` VALUES (4, 47, 'SUB20250801668735', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'cancelled', NULL, NULL, '2025-08-01 00:46:55', '2025-08-01 08:16:55', '2025-08-01 08:18:57');
INSERT INTO `membership_orders` VALUES (5, 47, 'SUB20250801667114', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'cancelled', NULL, NULL, '2025-08-01 00:48:57', '2025-08-01 08:18:57', '2025-08-01 08:21:33');
INSERT INTO `membership_orders` VALUES (6, 47, 'SUB20250801632555', 'pro', 'pro_monthly', 1, 29.90, 19.90, 'mock', 'pending', NULL, NULL, '2025-08-01 00:51:34', '2025-08-01 08:21:34', '2025-08-01 08:21:34');

SET FOREIGN_KEY_CHECKS = 1;
