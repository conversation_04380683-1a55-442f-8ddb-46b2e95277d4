import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { executeQuery } from '@/lib/mysql';
import { getUserByToken } from '@/lib/auth-service';
import { getNumberSetting } from '@/lib/points-service';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ success: false, error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    if (!user) {
      return NextResponse.json({ success: false, error: "用户不存在" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const orderType = searchParams.get('type') as 'subscription' | 'recharge';

    if (!orderType || !['subscription', 'recharge'].includes(orderType)) {
      return NextResponse.json({ success: false, error: "订单类型参数错误" }, { status: 400 });
    }

    // 获取订单过期时间设置
    const expireMinutes = await getNumberSetting('order_expire_minutes', 30);
    const expireTime = new Date(Date.now() - expireMinutes * 60 * 1000);

    let query: string;
    let pendingOrder: any = null;

    if (orderType === 'subscription') {
      const results = await executeQuery(
        `SELECT id, order_no, membership_type, plan_key, duration_months, 
                original_price, discount_price, order_expires_at, created_at
         FROM membership_orders 
         WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
         ORDER BY created_at DESC LIMIT 1`,
        [user.id]
      ) as any[];

      if (results.length > 0) {
        const order = results[0];
        pendingOrder = {
          id: order.id,
          order_no: order.order_no,
          order_type: 'subscription',
          amount: order.discount_price,
          original_price: order.original_price,
          discount_price: order.discount_price,
          status: 'pending',
          expires_at: order.order_expires_at,
          created_at: order.created_at,
          membership_type: order.membership_type,
          plan_key: order.plan_key,
          duration_months: order.duration_months
        };
      }
    } else {
      const results = await executeQuery(
        `SELECT id, order_no, package_key, points_amount, bonus_points, points_validity_days,
                original_price, discount_price, order_expires_at, created_at
         FROM recharge_orders 
         WHERE user_id = ? AND status = 'pending' AND order_expires_at > NOW()
         ORDER BY created_at DESC LIMIT 1`,
        [user.id]
      ) as any[];

      if (results.length > 0) {
        const order = results[0];
        pendingOrder = {
          id: order.id,
          order_no: order.order_no,
          order_type: 'recharge',
          amount: order.discount_price,
          original_price: order.original_price,
          discount_price: order.discount_price,
          status: 'pending',
          expires_at: order.order_expires_at,
          created_at: order.created_at,
          package_key: order.package_key,
          points_amount: order.points_amount,
          bonus_points: order.bonus_points,
          points_validity_days: order.points_validity_days
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: pendingOrder
    });

  } catch (error) {
    console.error('检查pending订单失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误' },
      { status: 500 }
    );
  }
}
