import { NextRequest, NextResponse } from "next/server";
import { executeQuery } from "@/lib/mysql";
import { ProjectVersion } from "@/lib/auth-service";

// GET /api/projects/[id]/versions - 获取项目的所有版本
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: "Invalid project ID" },
        { status: 400 }
      );
    }
    
    // 获取项目的所有版本，按版本号降序排序
    const versions = await executeQuery(
      `SELECT 
        id,
        version_number,
        title,
        prompt,
        html_content,
        parent_version_id as parent_version,
        is_active,
        created_at,
        metadata
      FROM project_versions 
      WHERE project_id = ? 
      ORDER BY version_number DESC`,
      [projectId]
    ) as ProjectVersion[];

    return NextResponse.json({
      success: true,
      versions: versions.map((version: any) => ({
        id: version.id.toString(),
        version_number: version.version_number,
        title: version.title,
        prompt: version.prompt,
        html_content: version.html_content,
        parent_version: version.parent_version,
        is_active: version.is_active,
        created_at: version.created_at,
        metadata: version.metadata ? JSON.parse(version.metadata) : null,
      })),
    });
  } catch (error) {
    console.error("Failed to fetch versions:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch versions" },
      { status: 500 }
    );
  }
}

// POST /api/projects/[id]/versions - 创建新版本
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: "Invalid project ID" },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    const { 
      userId, 
      title, 
      prompt, 
      htmlContent, 
      parentVersion = null, 
      metadata = {} 
    } = body;

    // 获取当前最大版本号
    const latestVersionResult = await executeQuery(
      `SELECT version_number FROM project_versions 
       WHERE project_id = ? 
       ORDER BY version_number DESC 
       LIMIT 1`,
      [projectId]
    ) as any[];

    const nextVersionNumber = latestVersionResult.length > 0 
      ? latestVersionResult[0].version_number + 1 
      : 1;

    // 将所有版本设为非激活状态
    await executeQuery(
      `UPDATE project_versions 
       SET is_active = false 
       WHERE project_id = ?`,
      [projectId]
    );

    // 创建新版本
    const metadataJson = JSON.stringify({
      lines_changed: metadata.linesChanged || 0,
      generation_time: metadata.generationTime || 0,
      model_used: metadata.modelUsed || "deepseek-chat",
      is_modification: metadata.isModification || false,
    });

    const result = await executeQuery(
      `INSERT INTO project_versions (
        project_id, 
        user_id, 
        version_number, 
        title, 
        prompt, 
        html_content, 
        parent_version_id, 
        is_active, 
        metadata,
        created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        projectId,
        userId,
        nextVersionNumber,
        title || `Version ${nextVersionNumber}`,
        prompt,
        htmlContent,
        parentVersion,
        true,
        metadataJson
      ]
    ) as any;

    return NextResponse.json({
      success: true,
      version: {
        id: result.insertId.toString(),
        version_number: nextVersionNumber,
        title: title || `Version ${nextVersionNumber}`,
        prompt: prompt,
        html_content: htmlContent,
        parent_version: parentVersion,
        is_active: true,
        created_at: new Date().toISOString(),
        metadata: JSON.parse(metadataJson),
      },
    });
  } catch (error) {
    console.error("Failed to create version:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create version" },
      { status: 500 }
    );
  }
}